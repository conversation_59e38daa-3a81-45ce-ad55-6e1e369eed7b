<template>
  <div class="login-container">
    <div class="login-card">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <img src="/logo.png" alt="SOIC" class="logo-image" />
        </div>
        <h1 class="title">欢迎回来</h1>
        <p class="subtitle">登录您的SOIC账户，开启社交创新之旅</p>
      </div>

      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名或邮箱"
            prefix-icon="User"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">
              记住我
            </el-checkbox>
            <el-link type="primary" @click="showForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 第三方登录 -->
      <div class="social-login">
        <div class="divider">
          <span>或使用以下方式登录</span>
        </div>
        
        <div class="social-buttons">
          <el-button class="social-button wechat" @click="loginWithWechat">
            <i class="icon-wechat"></i>
            微信
          </el-button>
          <el-button class="social-button qq" @click="loginWithQQ">
            <i class="icon-qq"></i>
            QQ
          </el-button>
          <el-button class="social-button github" @click="loginWithGithub">
            <i class="icon-github"></i>
            GitHub
          </el-button>
        </div>
      </div>

      <!-- 注册链接 -->
      <div class="register-link">
        <span>还没有账户？</span>
        <el-link type="primary" @click="goToRegister">
          立即注册
        </el-link>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import type { LoginCredentials } from '@/types/user'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive<LoginCredentials & { rememberMe: boolean }>({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 128, message: '密码长度在 6 到 128 个字符', trigger: 'blur' }
  ]
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    
    loading.value = true
    
    const credentials: LoginCredentials = {
      username: loginForm.username,
      password: loginForm.password
    }
    
    await userStore.login(credentials)
    
    ElMessage.success('登录成功！')
    
    // 跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

/**
 * 显示忘记密码对话框
 */
const showForgotPassword = async () => {
  try {
    const { value: email } = await ElMessageBox.prompt(
      '请输入您的邮箱地址，我们将发送重置密码的链接',
      '忘记密码',
      {
        confirmButtonText: '发送',
        cancelButtonText: '取消',
        inputPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        inputErrorMessage: '请输入有效的邮箱地址'
      }
    )
    
    // TODO: 调用忘记密码API
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    
  } catch (error) {
    // 用户取消操作
  }
}

/**
 * 第三方登录
 */
const loginWithWechat = () => {
  ElMessage.info('微信登录功能开发中...')
}

const loginWithQQ = () => {
  ElMessage.info('QQ登录功能开发中...')
}

const loginWithGithub = () => {
  ElMessage.info('GitHub登录功能开发中...')
}

/**
 * 跳转到注册页面
 */
const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
  
  .logo {
    margin-bottom: 20px;
    
    .logo-image {
      width: 60px;
      height: 60px;
      border-radius: 12px;
    }
  }
  
  .title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
  }
  
  .subtitle {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 12px;
  }
}

.social-login {
  margin-top: 30px;
  
  .divider {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e1e8ed;
    }
    
    span {
      background: rgba(255, 255, 255, 0.95);
      padding: 0 15px;
      font-size: 12px;
      color: #7f8c8d;
    }
  }
  
  .social-buttons {
    display: flex;
    gap: 10px;
    
    .social-button {
      flex: 1;
      height: 40px;
      border-radius: 8px;
      font-size: 12px;
      
      &.wechat {
        background: #07c160;
        border-color: #07c160;
        color: white;
        
        &:hover {
          background: #06ad56;
        }
      }
      
      &.qq {
        background: #12b7f5;
        border-color: #12b7f5;
        color: white;
        
        &:hover {
          background: #0fa5e0;
        }
      }
      
      &.github {
        background: #24292e;
        border-color: #24292e;
        color: white;
        
        &:hover {
          background: #1c2025;
        }
      }
    }
  }
}

.register-link {
  text-align: center;
  margin-top: 30px;
  font-size: 14px;
  color: #7f8c8d;
  
  .el-link {
    margin-left: 5px;
  }
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    
    &.circle-1 {
      width: 200px;
      height: 200px;
      top: -100px;
      left: -100px;
      animation: float 6s ease-in-out infinite;
    }
    
    &.circle-2 {
      width: 150px;
      height: 150px;
      top: 50%;
      right: -75px;
      animation: float 8s ease-in-out infinite reverse;
    }
    
    &.circle-3 {
      width: 100px;
      height: 100px;
      bottom: -50px;
      left: 50%;
      transform: translateX(-50%);
      animation: float 10s ease-in-out infinite;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-card {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .login-header .title {
    font-size: 24px;
  }
}
</style>
