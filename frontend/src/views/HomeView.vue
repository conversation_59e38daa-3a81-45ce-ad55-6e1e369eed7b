<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <AppHeader />
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧边栏 -->
      <aside class="sidebar-left">
        <UserProfileCard />
        <QuickActions />
        <NavigationMenu />
      </aside>
      
      <!-- 中间内容区 -->
      <main class="content-main">
        <!-- 发布内容区域 -->
        <div class="publish-section">
          <PostComposer @published="handlePostPublished" />
        </div>
        
        <!-- 内容动态流 -->
        <div class="feed-section">
          <FeedTabs v-model="activeTab" @change="handleTabChange" />
          <PostFeed 
            :posts="posts" 
            :loading="feedLoading"
            @load-more="loadMorePosts"
            @like="handleLike"
            @comment="handleComment"
            @share="handleShare"
          />
        </div>
      </main>
      
      <!-- 右侧边栏 -->
      <aside class="sidebar-right">
        <OnlineUsers />
        <RecommendedUsers />
        <TrendingTopics />
        <RecentActivities />
      </aside>
    </div>
    
    <!-- 浮动操作按钮 -->
    <FloatingActionButton />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useSocialStore } from '@/stores/modules/social'
import { useMessagingStore } from '@/stores/modules/messaging'
import { contentAPI } from '@/api/content'
import { wsService } from '@/services/websocket'

// 组件导入
import AppHeader from '@/components/layout/AppHeader.vue'
import UserProfileCard from '@/components/user/UserProfileCard.vue'
import QuickActions from '@/components/common/QuickActions.vue'
import NavigationMenu from '@/components/layout/NavigationMenu.vue'
import PostComposer from '@/components/content/PostComposer.vue'
import FeedTabs from '@/components/content/FeedTabs.vue'
import PostFeed from '@/components/content/PostFeed.vue'
import OnlineUsers from '@/components/social/OnlineUsers.vue'
import RecommendedUsers from '@/components/social/RecommendedUsers.vue'
import TrendingTopics from '@/components/content/TrendingTopics.vue'
import RecentActivities from '@/components/social/RecentActivities.vue'
import FloatingActionButton from '@/components/common/FloatingActionButton.vue'

// Store实例
const userStore = useUserStore()
const socialStore = useSocialStore()
const messagingStore = useMessagingStore()

// 响应式数据
const activeTab = ref('following') // 'following' | 'recommended' | 'trending'
const posts = ref<any[]>([])
const feedLoading = ref(false)
const currentPage = ref(1)
const hasMore = ref(true)

// 计算属性
const isAuthenticated = computed(() => userStore.isAuthenticated)

/**
 * 组件挂载时的初始化
 */
onMounted(async () => {
  if (isAuthenticated.value) {
    await initializeApp()
  }
})

/**
 * 初始化应用
 */
const initializeApp = async () => {
  try {
    // 连接WebSocket
    await wsService.connect()
    
    // 并行加载初始数据
    await Promise.all([
      loadPosts(),
      loadUserData(),
      loadSocialData()
    ])
    
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
}

/**
 * 加载帖子数据
 */
const loadPosts = async (reset = true) => {
  if (feedLoading.value) return
  
  feedLoading.value = true
  
  try {
    const params = {
      page: reset ? 1 : currentPage.value,
      pageSize: 10,
      sortBy: getPostSortBy(),
      categoryId: getPostCategoryFilter()
    }
    
    const response = await contentAPI.getPosts(params)
    
    if (response.success) {
      if (reset) {
        posts.value = response.data.posts
        currentPage.value = 1
      } else {
        posts.value.push(...response.data.posts)
      }
      
      hasMore.value = response.data.hasNext
      currentPage.value++
    }
    
  } catch (error) {
    console.error('加载帖子失败:', error)
  } finally {
    feedLoading.value = false
  }
}

/**
 * 加载用户相关数据
 */
const loadUserData = async () => {
  try {
    // 获取未读消息数量
    await messagingStore.fetchConversations({ pageSize: 5 })
    
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

/**
 * 加载社交相关数据
 */
const loadSocialData = async () => {
  try {
    await Promise.all([
      socialStore.fetchRecommendedUsers({ limit: 5 }),
      socialStore.fetchActivities({ pageSize: 10 })
    ])
    
  } catch (error) {
    console.error('加载社交数据失败:', error)
  }
}

/**
 * 获取帖子排序方式
 */
const getPostSortBy = () => {
  switch (activeTab.value) {
    case 'following':
      return 'created_at'
    case 'recommended':
      return 'likes'
    case 'trending':
      return 'views'
    default:
      return 'created_at'
  }
}

/**
 * 获取帖子分类过滤
 */
const getPostCategoryFilter = () => {
  // 根据当前标签返回相应的分类过滤
  return undefined
}

/**
 * 处理标签切换
 */
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  loadPosts(true)
}

/**
 * 加载更多帖子
 */
const loadMorePosts = () => {
  if (hasMore.value && !feedLoading.value) {
    loadPosts(false)
  }
}

/**
 * 处理帖子发布
 */
const handlePostPublished = (newPost: any) => {
  posts.value.unshift(newPost)
}

/**
 * 处理点赞
 */
const handleLike = async (postId: string) => {
  try {
    const post = posts.value.find(p => p.id === postId)
    if (!post) return
    
    if (post.isLiked) {
      await contentAPI.unlikePost(postId)
      post.isLiked = false
      post.likeCount--
    } else {
      await contentAPI.likePost(postId)
      post.isLiked = true
      post.likeCount++
    }
    
  } catch (error) {
    console.error('点赞操作失败:', error)
  }
}

/**
 * 处理评论
 */
const handleComment = (postId: string) => {
  // 跳转到帖子详情页或打开评论弹窗
  console.log('评论帖子:', postId)
}

/**
 * 处理分享
 */
const handleShare = async (postId: string) => {
  try {
    const response = await contentAPI.sharePost(postId)
    
    if (response.success) {
      // 显示分享选项或复制链接
      await navigator.clipboard.writeText(response.data.shareUrl)
      ElMessage.success('分享链接已复制到剪贴板')
    }
    
  } catch (error) {
    console.error('分享失败:', error)
  }
}
</script>

<style scoped lang="scss">
.home-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.main-content {
  display: grid;
  grid-template-columns: 280px 1fr 320px;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 60px); // 减去header高度
}

.sidebar-left {
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  > * {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.content-main {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0; // 允许内容区域收缩
}

.publish-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.feed-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar-right {
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  > * {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr 320px;
    
    .sidebar-left {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    padding: 10px;
    gap: 10px;
    
    .sidebar-right {
      display: none;
    }
  }
  
  .publish-section,
  .feed-section {
    border-radius: 8px;
    margin: 0 -10px;
  }
}

// 滚动条样式
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background-color: #a8a8a8;
    }
  }
}

// 加载动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 卡片悬停效果
.sidebar-left > *,
.sidebar-right > *,
.publish-section,
.feed-section {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}
</style>
