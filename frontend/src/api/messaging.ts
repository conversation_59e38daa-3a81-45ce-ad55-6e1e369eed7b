import { request, type APIResponse } from './base'
import type { 
  Conversation, 
  Message, 
  MessageAttachment,
  MessageReaction,
  MessageThread 
} from '@/types/messaging'

/**
 * 消息系统API接口
 * 职责：处理私聊、群聊、消息管理、文件传输等API请求
 */
export const messagingAPI = {
  // ==================== 会话管理 ====================

  /**
   * 获取会话列表
   */
  getConversations(params?: {
    page?: number
    pageSize?: number
    type?: 'private' | 'group'
    unreadOnly?: boolean
  }): Promise<APIResponse<{
    conversations: Conversation[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/messaging/conversations/', { params })
  },

  /**
   * 创建私聊会话
   */
  createPrivateConversation(userId: string): Promise<APIResponse<Conversation>> {
    return request.post('/messaging/conversations/private/', { userId })
  },

  /**
   * 创建群聊会话
   */
  createGroupConversation(data: {
    name: string
    description?: string
    memberIds: string[]
    avatar?: string
  }): Promise<APIResponse<Conversation>> {
    return request.post('/messaging/conversations/group/', data)
  },

  /**
   * 获取会话详情
   */
  getConversationDetail(conversationId: string): Promise<APIResponse<{
    conversation: Conversation
    members: any[]
    unreadCount: number
    lastReadMessageId?: string
  }>> {
    return request.get(`/messaging/conversations/${conversationId}/`)
  },

  /**
   * 更新会话信息
   */
  updateConversation(conversationId: string, data: {
    name?: string
    description?: string
    avatar?: string
  }): Promise<APIResponse<Conversation>> {
    return request.patch(`/messaging/conversations/${conversationId}/`, data)
  },

  /**
   * 删除会话
   */
  deleteConversation(conversationId: string): Promise<APIResponse<void>> {
    return request.delete(`/messaging/conversations/${conversationId}/`)
  },

  /**
   * 离开群聊
   */
  leaveGroupConversation(conversationId: string): Promise<APIResponse<void>> {
    return request.post(`/messaging/conversations/${conversationId}/leave/`)
  },

  /**
   * 添加群聊成员
   */
  addGroupMembers(conversationId: string, memberIds: string[]): Promise<APIResponse<void>> {
    return request.post(`/messaging/conversations/${conversationId}/members/`, { memberIds })
  },

  /**
   * 移除群聊成员
   */
  removeGroupMember(conversationId: string, userId: string): Promise<APIResponse<void>> {
    return request.delete(`/messaging/conversations/${conversationId}/members/${userId}/`)
  },

  // ==================== 消息管理 ====================

  /**
   * 获取消息列表
   */
  getMessages(conversationId: string, params?: {
    page?: number
    pageSize?: number
    beforeMessageId?: string
    afterMessageId?: string
  }): Promise<APIResponse<{
    messages: Message[]
    total: number
    hasNext: boolean
    hasPrevious: boolean
  }>> {
    return request.get(`/messaging/conversations/${conversationId}/messages/`, { params })
  },

  /**
   * 发送文本消息
   */
  sendTextMessage(conversationId: string, data: {
    content: string
    replyToId?: string
    mentionIds?: string[]
  }): Promise<APIResponse<Message>> {
    return request.post(`/messaging/conversations/${conversationId}/messages/`, {
      type: 'text',
      ...data
    })
  },

  /**
   * 发送图片消息
   */
  sendImageMessage(conversationId: string, data: {
    imageUrl: string
    thumbnailUrl?: string
    width?: number
    height?: number
    caption?: string
    replyToId?: string
  }): Promise<APIResponse<Message>> {
    return request.post(`/messaging/conversations/${conversationId}/messages/`, {
      type: 'image',
      ...data
    })
  },

  /**
   * 发送文件消息
   */
  sendFileMessage(conversationId: string, data: {
    fileUrl: string
    fileName: string
    fileSize: number
    mimeType: string
    caption?: string
    replyToId?: string
  }): Promise<APIResponse<Message>> {
    return request.post(`/messaging/conversations/${conversationId}/messages/`, {
      type: 'file',
      ...data
    })
  },

  /**
   * 发送语音消息
   */
  sendVoiceMessage(conversationId: string, data: {
    audioUrl: string
    duration: number
    waveform?: number[]
    replyToId?: string
  }): Promise<APIResponse<Message>> {
    return request.post(`/messaging/conversations/${conversationId}/messages/`, {
      type: 'voice',
      ...data
    })
  },

  /**
   * 发送位置消息
   */
  sendLocationMessage(conversationId: string, data: {
    latitude: number
    longitude: number
    address?: string
    title?: string
    replyToId?: string
  }): Promise<APIResponse<Message>> {
    return request.post(`/messaging/conversations/${conversationId}/messages/`, {
      type: 'location',
      ...data
    })
  },

  /**
   * 编辑消息
   */
  editMessage(messageId: string, content: string): Promise<APIResponse<Message>> {
    return request.patch(`/messaging/messages/${messageId}/`, { content })
  },

  /**
   * 删除消息
   */
  deleteMessage(messageId: string, deleteForEveryone?: boolean): Promise<APIResponse<void>> {
    return request.delete(`/messaging/messages/${messageId}/`, {
      params: { deleteForEveryone }
    })
  },

  /**
   * 撤回消息
   */
  recallMessage(messageId: string): Promise<APIResponse<void>> {
    return request.post(`/messaging/messages/${messageId}/recall/`)
  },

  /**
   * 转发消息
   */
  forwardMessage(messageId: string, conversationIds: string[]): Promise<APIResponse<void>> {
    return request.post(`/messaging/messages/${messageId}/forward/`, { conversationIds })
  },

  // ==================== 消息状态 ====================

  /**
   * 标记消息为已读
   */
  markAsRead(conversationId: string, messageId?: string): Promise<APIResponse<void>> {
    return request.post(`/messaging/conversations/${conversationId}/read/`, { messageId })
  },

  /**
   * 获取未读消息数量
   */
  getUnreadCount(): Promise<APIResponse<{
    totalUnread: number
    conversationUnreads: Array<{
      conversationId: string
      unreadCount: number
    }>
  }>> {
    return request.get('/messaging/unread-count/')
  },

  /**
   * 设置消息免打扰
   */
  setMute(conversationId: string, muted: boolean, muteUntil?: string): Promise<APIResponse<void>> {
    return request.post(`/messaging/conversations/${conversationId}/mute/`, { muted, muteUntil })
  },

  /**
   * 置顶会话
   */
  pinConversation(conversationId: string, pinned: boolean): Promise<APIResponse<void>> {
    return request.post(`/messaging/conversations/${conversationId}/pin/`, { pinned })
  },

  // ==================== 消息反应和线程 ====================

  /**
   * 添加消息反应
   */
  addReaction(messageId: string, emoji: string): Promise<APIResponse<MessageReaction>> {
    return request.post(`/messaging/messages/${messageId}/reactions/`, { emoji })
  },

  /**
   * 移除消息反应
   */
  removeReaction(messageId: string, emoji: string): Promise<APIResponse<void>> {
    return request.delete(`/messaging/messages/${messageId}/reactions/${emoji}/`)
  },

  /**
   * 获取消息反应列表
   */
  getReactions(messageId: string): Promise<APIResponse<MessageReaction[]>> {
    return request.get(`/messaging/messages/${messageId}/reactions/`)
  },

  /**
   * 创建消息线程
   */
  createThread(messageId: string, content: string): Promise<APIResponse<MessageThread>> {
    return request.post(`/messaging/messages/${messageId}/thread/`, { content })
  },

  /**
   * 获取消息线程
   */
  getThread(messageId: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    thread: MessageThread
    messages: Message[]
    total: number
  }>> {
    return request.get(`/messaging/messages/${messageId}/thread/`, { params })
  },

  // ==================== 文件上传 ====================

  /**
   * 上传图片
   */
  uploadImage(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<{
    imageUrl: string
    thumbnailUrl: string
    width: number
    height: number
  }>> {
    return request.upload('/messaging/upload/image/', file, onProgress)
  },

  /**
   * 上传文件
   */
  uploadFile(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<{
    fileUrl: string
    fileName: string
    fileSize: number
    mimeType: string
  }>> {
    return request.upload('/messaging/upload/file/', file, onProgress)
  },

  /**
   * 上传语音
   */
  uploadVoice(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<{
    audioUrl: string
    duration: number
    waveform: number[]
  }>> {
    return request.upload('/messaging/upload/voice/', file, onProgress)
  },

  // ==================== 搜索功能 ====================

  /**
   * 搜索消息
   */
  searchMessages(params: {
    query: string
    conversationId?: string
    type?: 'text' | 'image' | 'file' | 'voice'
    fromUserId?: string
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    messages: Message[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/messaging/search/', { params })
  },

  /**
   * 获取消息统计
   */
  getMessageStats(): Promise<APIResponse<{
    totalMessages: number
    totalConversations: number
    todayMessages: number
    weeklyMessages: number
    monthlyMessages: number
  }>> {
    return request.get('/messaging/stats/')
  }
}
