import { request, type APIResponse } from './base'
import type { 
  Friendship, 
  Follow, 
  Group, 
  GroupMember,
  SocialActivity,
  UserSpace 
} from '@/types/social'

/**
 * 社交功能API接口
 * 职责：处理好友关系、关注系统、群组管理、用户空间等API请求
 */
export const socialAPI = {
  // ==================== 好友关系管理 ====================
  
  /**
   * 获取好友列表
   */
  getFriends(params?: {
    page?: number
    pageSize?: number
    status?: 'pending' | 'accepted' | 'blocked'
  }): Promise<APIResponse<{
    friends: Friendship[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/friends/', { params })
  },

  /**
   * 发送好友请求
   */
  sendFriendRequest(userId: string, message?: string): Promise<APIResponse<Friendship>> {
    return request.post('/social/friends/request/', { userId, message })
  },

  /**
   * 接受好友请求
   */
  acceptFriendRequest(requestId: string): Promise<APIResponse<Friendship>> {
    return request.post(`/social/friends/${requestId}/accept/`)
  },

  /**
   * 拒绝好友请求
   */
  rejectFriendRequest(requestId: string): Promise<APIResponse<void>> {
    return request.post(`/social/friends/${requestId}/reject/`)
  },

  /**
   * 删除好友
   */
  removeFriend(friendshipId: string): Promise<APIResponse<void>> {
    return request.delete(`/social/friends/${friendshipId}/`)
  },

  /**
   * 屏蔽用户
   */
  blockUser(userId: string): Promise<APIResponse<void>> {
    return request.post('/social/friends/block/', { userId })
  },

  /**
   * 取消屏蔽用户
   */
  unblockUser(userId: string): Promise<APIResponse<void>> {
    return request.post('/social/friends/unblock/', { userId })
  },

  // ==================== 关注系统 ====================

  /**
   * 获取关注列表
   */
  getFollowing(params?: {
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    following: Follow[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/following/', { params })
  },

  /**
   * 获取粉丝列表
   */
  getFollowers(params?: {
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    followers: Follow[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/followers/', { params })
  },

  /**
   * 关注用户
   */
  followUser(userId: string): Promise<APIResponse<Follow>> {
    return request.post('/social/follow/', { userId })
  },

  /**
   * 取消关注用户
   */
  unfollowUser(userId: string): Promise<APIResponse<void>> {
    return request.delete(`/social/follow/${userId}/`)
  },

  /**
   * 检查关注状态
   */
  checkFollowStatus(userId: string): Promise<APIResponse<{
    isFollowing: boolean
    isFollowedBy: boolean
    isFriend: boolean
  }>> {
    return request.get(`/social/follow/status/${userId}/`)
  },

  // ==================== 群组管理 ====================

  /**
   * 获取群组列表
   */
  getGroups(params?: {
    page?: number
    pageSize?: number
    type?: 'public' | 'private' | 'secret'
    category?: string
  }): Promise<APIResponse<{
    groups: Group[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/groups/', { params })
  },

  /**
   * 创建群组
   */
  createGroup(data: {
    name: string
    description?: string
    type: 'public' | 'private' | 'secret'
    category?: string
    maxMembers?: number
    avatar?: string
  }): Promise<APIResponse<Group>> {
    return request.post('/social/groups/', data)
  },

  /**
   * 获取群组详情
   */
  getGroupDetail(groupId: string): Promise<APIResponse<{
    group: Group
    members: GroupMember[]
    memberCount: number
    isJoined: boolean
    role?: 'owner' | 'admin' | 'member'
  }>> {
    return request.get(`/social/groups/${groupId}/`)
  },

  /**
   * 更新群组信息
   */
  updateGroup(groupId: string, data: Partial<Group>): Promise<APIResponse<Group>> {
    return request.patch(`/social/groups/${groupId}/`, data)
  },

  /**
   * 删除群组
   */
  deleteGroup(groupId: string): Promise<APIResponse<void>> {
    return request.delete(`/social/groups/${groupId}/`)
  },

  /**
   * 加入群组
   */
  joinGroup(groupId: string, message?: string): Promise<APIResponse<GroupMember>> {
    return request.post(`/social/groups/${groupId}/join/`, { message })
  },

  /**
   * 离开群组
   */
  leaveGroup(groupId: string): Promise<APIResponse<void>> {
    return request.post(`/social/groups/${groupId}/leave/`)
  },

  /**
   * 邀请用户加入群组
   */
  inviteToGroup(groupId: string, userId: string): Promise<APIResponse<void>> {
    return request.post(`/social/groups/${groupId}/invite/`, { userId })
  },

  /**
   * 移除群组成员
   */
  removeGroupMember(groupId: string, userId: string): Promise<APIResponse<void>> {
    return request.delete(`/social/groups/${groupId}/members/${userId}/`)
  },

  /**
   * 设置群组成员角色
   */
  setMemberRole(groupId: string, userId: string, role: 'admin' | 'member'): Promise<APIResponse<GroupMember>> {
    return request.patch(`/social/groups/${groupId}/members/${userId}/`, { role })
  },

  // ==================== 用户空间 ====================

  /**
   * 获取用户空间列表
   */
  getUserSpaces(params?: {
    page?: number
    pageSize?: number
    category?: string
    isPublic?: boolean
  }): Promise<APIResponse<{
    spaces: UserSpace[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/spaces/', { params })
  },

  /**
   * 创建用户空间
   */
  createUserSpace(data: {
    name: string
    description?: string
    category: string
    isPublic: boolean
    maxVisitors?: number
    sceneData?: any
  }): Promise<APIResponse<UserSpace>> {
    return request.post('/social/spaces/', data)
  },

  /**
   * 获取空间详情
   */
  getSpaceDetail(spaceId: string): Promise<APIResponse<UserSpace>> {
    return request.get(`/social/spaces/${spaceId}/`)
  },

  /**
   * 更新空间信息
   */
  updateSpace(spaceId: string, data: Partial<UserSpace>): Promise<APIResponse<UserSpace>> {
    return request.patch(`/social/spaces/${spaceId}/`, data)
  },

  /**
   * 删除空间
   */
  deleteSpace(spaceId: string): Promise<APIResponse<void>> {
    return request.delete(`/social/spaces/${spaceId}/`)
  },

  /**
   * 进入空间
   */
  enterSpace(spaceId: string): Promise<APIResponse<{
    spaceData: UserSpace
    sessionId: string
    accessToken: string
  }>> {
    return request.post(`/social/spaces/${spaceId}/enter/`)
  },

  /**
   * 离开空间
   */
  leaveSpace(spaceId: string): Promise<APIResponse<void>> {
    return request.post(`/social/spaces/${spaceId}/leave/`)
  },

  // ==================== 社交活动 ====================

  /**
   * 获取社交活动动态
   */
  getSocialActivities(params?: {
    page?: number
    pageSize?: number
    type?: string
    userId?: string
  }): Promise<APIResponse<{
    activities: SocialActivity[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/social/activities/', { params })
  },

  /**
   * 获取推荐用户
   */
  getRecommendedUsers(params?: {
    limit?: number
    category?: string
  }): Promise<APIResponse<{
    users: any[]
    reason: string
  }[]>> {
    return request.get('/social/recommendations/users/', { params })
  },

  /**
   * 获取推荐群组
   */
  getRecommendedGroups(params?: {
    limit?: number
    category?: string
  }): Promise<APIResponse<{
    groups: Group[]
    reason: string
  }[]>> {
    return request.get('/social/recommendations/groups/', { params })
  },

  /**
   * 搜索社交内容
   */
  searchSocial(params: {
    query: string
    type?: 'users' | 'groups' | 'spaces'
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    users?: any[]
    groups?: Group[]
    spaces?: UserSpace[]
    total: number
  }>> {
    return request.get('/social/search/', { params })
  }
}
