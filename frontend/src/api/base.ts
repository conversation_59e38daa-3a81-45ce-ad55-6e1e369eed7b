import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import router from '@/router'

// API响应接口定义
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  requestId: string
  version: string
}

/**
 * API客户端类 - 统一的HTTP请求处理
 * 职责：处理HTTP请求、响应拦截、错误处理、认证管理
 */
class APIClient {
  private instance: AxiosInstance

  constructor() {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 设置请求拦截器
    this.setupRequestInterceptor()
    
    // 设置响应拦截器
    this.setupResponseInterceptor()
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor() {
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证令牌
        const token = getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId()

        // 添加时间戳
        config.headers['X-Timestamp'] = new Date().toISOString()

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor() {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<APIResponse>) => {
        // 检查业务状态码
        if (response.data && !response.data.success) {
          const error = response.data.error
          
          // 显示错误消息
          if (error?.message) {
            ElMessage.error(error.message)
          }
          
          return Promise.reject(new Error(error?.message || '请求失败'))
        }

        return response
      },
      (error) => {
        // 处理HTTP错误
        if (error.response) {
          const { status, data } = error.response

          switch (status) {
            case 401:
              // 未认证，清除令牌并跳转到登录页
              removeToken()
              router.push('/login')
              ElMessage.error('登录已过期，请重新登录')
              break
            
            case 403:
              ElMessage.error('没有权限访问该资源')
              break
            
            case 404:
              ElMessage.error('请求的资源不存在')
              break
            
            case 422:
              // 验证错误，显示详细错误信息
              if (data?.error?.details) {
                const details = data.error.details
                if (Array.isArray(details)) {
                  details.forEach((detail: any) => {
                    ElMessage.error(`${detail.field}: ${detail.message}`)
                  })
                } else {
                  ElMessage.error(data.error.message || '数据验证失败')
                }
              }
              break
            
            case 429:
              ElMessage.error('请求过于频繁，请稍后再试')
              break
            
            case 500:
              ElMessage.error('服务器内部错误')
              break
            
            default:
              ElMessage.error(data?.error?.message || '网络请求失败')
          }
        } else if (error.request) {
          // 网络错误
          ElMessage.error('网络连接失败，请检查网络设置')
        } else {
          // 其他错误
          ElMessage.error(error.message || '未知错误')
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<APIResponse<T>> {
    const response = await this.instance.get<APIResponse<T>>(url, config)
    return response.data
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<APIResponse<T>> {
    const response = await this.instance.post<APIResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<APIResponse<T>> {
    const response = await this.instance.put<APIResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<APIResponse<T>> {
    const response = await this.instance.patch<APIResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<APIResponse<T>> {
    const response = await this.instance.delete<APIResponse<T>>(url, config)
    return response.data
  }

  /**
   * 文件上传
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<APIResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }

    const response = await this.instance.post<APIResponse<T>>(url, formData, config)
    return response.data
  }
}

// 创建全局API客户端实例
export const apiClient = new APIClient()

// 导出便捷方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => apiClient.get<T>(url, config),
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => apiClient.post<T>(url, data, config),
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => apiClient.put<T>(url, data, config),
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => apiClient.patch<T>(url, data, config),
  delete: <T = any>(url: string, config?: AxiosRequestConfig) => apiClient.delete<T>(url, config),
  upload: <T = any>(url: string, file: File, onProgress?: (progress: number) => void) => apiClient.upload<T>(url, file, onProgress),
}