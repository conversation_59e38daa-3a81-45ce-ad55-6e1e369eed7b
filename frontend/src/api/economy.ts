import { request, type APIResponse } from './base'
import type { 
  Wallet, 
  Transaction, 
  Product,
  Order,
  VirtualCurrency,
  PaymentMethod 
} from '@/types/economy'

/**
 * 经济系统API接口
 * 职责：处理钱包管理、交易系统、商品管理、虚拟货币等API请求
 */
export const economyAPI = {
  // ==================== 钱包管理 ====================

  /**
   * 获取钱包信息
   */
  getWallet(): Promise<APIResponse<{
    wallet: Wallet
    currencies: VirtualCurrency[]
    totalBalance: number
    availableBalance: number
    frozenBalance: number
  }>> {
    return request.get('/economy/wallet/')
  },

  /**
   * 获取钱包余额
   */
  getWalletBalance(): Promise<APIResponse<{
    balances: Array<{
      currencyType: string
      balance: number
      frozenAmount: number
    }>
    totalValueInUSD: number
  }>> {
    return request.get('/economy/wallet/balance/')
  },

  /**
   * 充值钱包
   */
  rechargeWallet(data: {
    amount: number
    currencyType: string
    paymentMethod: string
    paymentData?: any
  }): Promise<APIResponse<{
    transaction: Transaction
    paymentUrl?: string
    qrCode?: string
  }>> {
    return request.post('/economy/wallet/recharge/', data)
  },

  /**
   * 提现钱包
   */
  withdrawWallet(data: {
    amount: number
    currencyType: string
    withdrawMethod: string
    withdrawAccount: string
    password: string
  }): Promise<APIResponse<Transaction>> {
    return request.post('/economy/wallet/withdraw/', data)
  },

  /**
   * 转账
   */
  transferMoney(data: {
    toUserId: string
    amount: number
    currencyType: string
    message?: string
    password: string
  }): Promise<APIResponse<Transaction>> {
    return request.post('/economy/wallet/transfer/', data)
  },

  // ==================== 交易记录 ====================

  /**
   * 获取交易记录
   */
  getTransactions(params?: {
    page?: number
    pageSize?: number
    type?: 'recharge' | 'withdraw' | 'transfer' | 'purchase' | 'reward' | 'refund'
    status?: 'pending' | 'completed' | 'failed' | 'cancelled'
    currencyType?: string
    startDate?: string
    endDate?: string
  }): Promise<APIResponse<{
    transactions: Transaction[]
    total: number
    hasNext: boolean
    summary: {
      totalIncome: number
      totalExpense: number
      netAmount: number
    }
  }>> {
    return request.get('/economy/transactions/', { params })
  },

  /**
   * 获取交易详情
   */
  getTransactionDetail(transactionId: string): Promise<APIResponse<{
    transaction: Transaction
    relatedData?: any
  }>> {
    return request.get(`/economy/transactions/${transactionId}/`)
  },

  /**
   * 取消交易
   */
  cancelTransaction(transactionId: string, reason?: string): Promise<APIResponse<Transaction>> {
    return request.post(`/economy/transactions/${transactionId}/cancel/`, { reason })
  },

  /**
   * 申请退款
   */
  requestRefund(transactionId: string, data: {
    reason: string
    description?: string
    amount?: number
  }): Promise<APIResponse<{
    refundRequest: any
    estimatedProcessTime: string
  }>> {
    return request.post(`/economy/transactions/${transactionId}/refund/`, data)
  },

  // ==================== 虚拟货币管理 ====================

  /**
   * 获取虚拟货币列表
   */
  getCurrencies(): Promise<APIResponse<VirtualCurrency[]>> {
    return request.get('/economy/currencies/')
  },

  /**
   * 获取汇率信息
   */
  getExchangeRates(): Promise<APIResponse<{
    rates: Array<{
      fromCurrency: string
      toCurrency: string
      rate: number
      lastUpdated: string
    }>
    baseCurrency: string
  }>> {
    return request.get('/economy/exchange-rates/')
  },

  /**
   * 货币兑换
   */
  exchangeCurrency(data: {
    fromCurrency: string
    toCurrency: string
    amount: number
    password: string
  }): Promise<APIResponse<{
    transaction: Transaction
    exchangeRate: number
    receivedAmount: number
    fee: number
  }>> {
    return request.post('/economy/exchange/', data)
  },

  // ==================== 商品管理 ====================

  /**
   * 获取商品列表
   */
  getProducts(params?: {
    page?: number
    pageSize?: number
    category?: string
    type?: 'virtual' | 'service' | 'subscription'
    status?: 'active' | 'inactive'
    search?: string
    sortBy?: 'price' | 'popularity' | 'created_at'
    sortOrder?: 'asc' | 'desc'
  }): Promise<APIResponse<{
    products: Product[]
    total: number
    hasNext: boolean
    categories: string[]
  }>> {
    return request.get('/economy/products/', { params })
  },

  /**
   * 获取商品详情
   */
  getProductDetail(productId: string): Promise<APIResponse<{
    product: Product
    relatedProducts: Product[]
    reviews: any[]
    averageRating: number
    totalSales: number
  }>> {
    return request.get(`/economy/products/${productId}/`)
  },

  /**
   * 创建商品（商家）
   */
  createProduct(data: {
    name: string
    description: string
    category: string
    type: 'virtual' | 'service' | 'subscription'
    price: number
    currencyType: string
    images: string[]
    metadata?: any
    isActive?: boolean
  }): Promise<APIResponse<Product>> {
    return request.post('/economy/products/', data)
  },

  /**
   * 更新商品
   */
  updateProduct(productId: string, data: Partial<Product>): Promise<APIResponse<Product>> {
    return request.patch(`/economy/products/${productId}/`, data)
  },

  /**
   * 删除商品
   */
  deleteProduct(productId: string): Promise<APIResponse<void>> {
    return request.delete(`/economy/products/${productId}/`)
  },

  // ==================== 订单管理 ====================

  /**
   * 创建订单
   */
  createOrder(data: {
    productId: string
    quantity: number
    paymentMethod: string
    deliveryInfo?: any
    couponCode?: string
  }): Promise<APIResponse<{
    order: Order
    paymentUrl?: string
    qrCode?: string
  }>> {
    return request.post('/economy/orders/', data)
  },

  /**
   * 获取订单列表
   */
  getOrders(params?: {
    page?: number
    pageSize?: number
    status?: 'pending' | 'paid' | 'processing' | 'completed' | 'cancelled' | 'refunded'
    type?: 'purchase' | 'sale'
    startDate?: string
    endDate?: string
  }): Promise<APIResponse<{
    orders: Order[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/economy/orders/', { params })
  },

  /**
   * 获取订单详情
   */
  getOrderDetail(orderId: string): Promise<APIResponse<{
    order: Order
    product: Product
    transactions: Transaction[]
    timeline: Array<{
      status: string
      timestamp: string
      description: string
    }>
  }>> {
    return request.get(`/economy/orders/${orderId}/`)
  },

  /**
   * 支付订单
   */
  payOrder(orderId: string, data: {
    paymentMethod: string
    paymentData?: any
    password?: string
  }): Promise<APIResponse<{
    transaction: Transaction
    paymentUrl?: string
    qrCode?: string
  }>> {
    return request.post(`/economy/orders/${orderId}/pay/`, data)
  },

  /**
   * 取消订单
   */
  cancelOrder(orderId: string, reason?: string): Promise<APIResponse<Order>> {
    return request.post(`/economy/orders/${orderId}/cancel/`, { reason })
  },

  /**
   * 确认收货
   */
  confirmOrder(orderId: string): Promise<APIResponse<Order>> {
    return request.post(`/economy/orders/${orderId}/confirm/`)
  },

  /**
   * 申请退款
   */
  refundOrder(orderId: string, data: {
    reason: string
    description?: string
    amount?: number
    images?: string[]
  }): Promise<APIResponse<{
    refundRequest: any
    estimatedProcessTime: string
  }>> {
    return request.post(`/economy/orders/${orderId}/refund/`, data)
  },

  // ==================== 支付方式管理 ====================

  /**
   * 获取支付方式列表
   */
  getPaymentMethods(): Promise<APIResponse<PaymentMethod[]>> {
    return request.get('/economy/payment-methods/')
  },

  /**
   * 添加支付方式
   */
  addPaymentMethod(data: {
    type: 'card' | 'bank' | 'alipay' | 'wechat' | 'paypal'
    name: string
    details: any
    isDefault?: boolean
  }): Promise<APIResponse<PaymentMethod>> {
    return request.post('/economy/payment-methods/', data)
  },

  /**
   * 更新支付方式
   */
  updatePaymentMethod(methodId: string, data: Partial<PaymentMethod>): Promise<APIResponse<PaymentMethod>> {
    return request.patch(`/economy/payment-methods/${methodId}/`, data)
  },

  /**
   * 删除支付方式
   */
  deletePaymentMethod(methodId: string): Promise<APIResponse<void>> {
    return request.delete(`/economy/payment-methods/${methodId}/`)
  },

  /**
   * 设置默认支付方式
   */
  setDefaultPaymentMethod(methodId: string): Promise<APIResponse<PaymentMethod>> {
    return request.post(`/economy/payment-methods/${methodId}/set-default/`)
  },

  // ==================== 统计和报告 ====================

  /**
   * 获取经济统计
   */
  getEconomyStats(): Promise<APIResponse<{
    totalBalance: number
    totalIncome: number
    totalExpense: number
    totalOrders: number
    totalProducts: number
    monthlyIncome: number
    monthlyExpense: number
    topProducts: Product[]
    recentTransactions: Transaction[]
  }>> {
    return request.get('/economy/stats/')
  },

  /**
   * 获取收入报告
   */
  getIncomeReport(params: {
    period: 'day' | 'week' | 'month' | 'year'
    startDate?: string
    endDate?: string
  }): Promise<APIResponse<{
    totalIncome: number
    periodData: Array<{
      date: string
      income: number
      expense: number
      netIncome: number
    }>
    topSources: Array<{
      source: string
      amount: number
      percentage: number
    }>
  }>> {
    return request.get('/economy/reports/income/', { params })
  },

  /**
   * 导出交易记录
   */
  exportTransactions(params: {
    format: 'csv' | 'excel' | 'pdf'
    startDate: string
    endDate: string
    type?: string
  }): Promise<APIResponse<{
    downloadUrl: string
    fileName: string
  }>> {
    return request.post('/economy/export/transactions/', params)
  }
}
