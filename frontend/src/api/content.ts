import { request, type APIResponse } from './base'
import type { 
  Post, 
  Comment, 
  ContentCategory,
  MediaFile,
  ContentReport 
} from '@/types/content'

/**
 * 内容管理API接口
 * 职责：处理帖子发布、评论系统、内容审核、分类管理等API请求
 */
export const contentAPI = {
  // ==================== 内容分类 ====================

  /**
   * 获取内容分类列表
   */
  getCategories(): Promise<APIResponse<ContentCategory[]>> {
    return request.get('/content/categories/')
  },

  /**
   * 创建内容分类
   */
  createCategory(data: {
    name: string
    description?: string
    parentId?: string
    icon?: string
    color?: string
  }): Promise<APIResponse<ContentCategory>> {
    return request.post('/content/categories/', data)
  },

  /**
   * 更新内容分类
   */
  updateCategory(categoryId: string, data: Partial<ContentCategory>): Promise<APIResponse<ContentCategory>> {
    return request.patch(`/content/categories/${categoryId}/`, data)
  },

  /**
   * 删除内容分类
   */
  deleteCategory(categoryId: string): Promise<APIResponse<void>> {
    return request.delete(`/content/categories/${categoryId}/`)
  },

  // ==================== 帖子管理 ====================

  /**
   * 获取帖子列表
   */
  getPosts(params?: {
    page?: number
    pageSize?: number
    categoryId?: string
    authorId?: string
    status?: 'draft' | 'published' | 'archived'
    sortBy?: 'created_at' | 'updated_at' | 'likes' | 'views'
    sortOrder?: 'asc' | 'desc'
    search?: string
  }): Promise<APIResponse<{
    posts: Post[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/content/posts/', { params })
  },

  /**
   * 获取推荐帖子
   */
  getRecommendedPosts(params?: {
    limit?: number
    categoryId?: string
  }): Promise<APIResponse<Post[]>> {
    return request.get('/content/posts/recommended/', { params })
  },

  /**
   * 获取热门帖子
   */
  getTrendingPosts(params?: {
    limit?: number
    timeRange?: 'day' | 'week' | 'month'
  }): Promise<APIResponse<Post[]>> {
    return request.get('/content/posts/trending/', { params })
  },

  /**
   * 获取帖子详情
   */
  getPostDetail(postId: string): Promise<APIResponse<{
    post: Post
    author: any
    category: ContentCategory
    isLiked: boolean
    isBookmarked: boolean
    likeCount: number
    commentCount: number
    viewCount: number
  }>> {
    return request.get(`/content/posts/${postId}/`)
  },

  /**
   * 创建帖子
   */
  createPost(data: {
    title: string
    content: string
    categoryId: string
    tags?: string[]
    mediaFiles?: string[]
    status?: 'draft' | 'published'
    allowComments?: boolean
    isPublic?: boolean
  }): Promise<APIResponse<Post>> {
    return request.post('/content/posts/', data)
  },

  /**
   * 更新帖子
   */
  updatePost(postId: string, data: Partial<Post>): Promise<APIResponse<Post>> {
    return request.patch(`/content/posts/${postId}/`, data)
  },

  /**
   * 删除帖子
   */
  deletePost(postId: string): Promise<APIResponse<void>> {
    return request.delete(`/content/posts/${postId}/`)
  },

  /**
   * 发布帖子
   */
  publishPost(postId: string): Promise<APIResponse<Post>> {
    return request.post(`/content/posts/${postId}/publish/`)
  },

  /**
   * 归档帖子
   */
  archivePost(postId: string): Promise<APIResponse<Post>> {
    return request.post(`/content/posts/${postId}/archive/`)
  },

  /**
   * 点赞帖子
   */
  likePost(postId: string): Promise<APIResponse<{
    isLiked: boolean
    likeCount: number
  }>> {
    return request.post(`/content/posts/${postId}/like/`)
  },

  /**
   * 取消点赞帖子
   */
  unlikePost(postId: string): Promise<APIResponse<{
    isLiked: boolean
    likeCount: number
  }>> {
    return request.delete(`/content/posts/${postId}/like/`)
  },

  /**
   * 收藏帖子
   */
  bookmarkPost(postId: string): Promise<APIResponse<{
    isBookmarked: boolean
  }>> {
    return request.post(`/content/posts/${postId}/bookmark/`)
  },

  /**
   * 取消收藏帖子
   */
  unbookmarkPost(postId: string): Promise<APIResponse<{
    isBookmarked: boolean
  }>> {
    return request.delete(`/content/posts/${postId}/bookmark/`)
  },

  /**
   * 分享帖子
   */
  sharePost(postId: string, platform?: string): Promise<APIResponse<{
    shareUrl: string
    shareCount: number
  }>> {
    return request.post(`/content/posts/${postId}/share/`, { platform })
  },

  // ==================== 评论管理 ====================

  /**
   * 获取评论列表
   */
  getComments(postId: string, params?: {
    page?: number
    pageSize?: number
    parentId?: string
    sortBy?: 'created_at' | 'likes'
    sortOrder?: 'asc' | 'desc'
  }): Promise<APIResponse<{
    comments: Comment[]
    total: number
    hasNext: boolean
  }>> {
    return request.get(`/content/posts/${postId}/comments/`, { params })
  },

  /**
   * 创建评论
   */
  createComment(postId: string, data: {
    content: string
    parentId?: string
    mentionIds?: string[]
  }): Promise<APIResponse<Comment>> {
    return request.post(`/content/posts/${postId}/comments/`, data)
  },

  /**
   * 更新评论
   */
  updateComment(commentId: string, content: string): Promise<APIResponse<Comment>> {
    return request.patch(`/content/comments/${commentId}/`, { content })
  },

  /**
   * 删除评论
   */
  deleteComment(commentId: string): Promise<APIResponse<void>> {
    return request.delete(`/content/comments/${commentId}/`)
  },

  /**
   * 点赞评论
   */
  likeComment(commentId: string): Promise<APIResponse<{
    isLiked: boolean
    likeCount: number
  }>> {
    return request.post(`/content/comments/${commentId}/like/`)
  },

  /**
   * 取消点赞评论
   */
  unlikeComment(commentId: string): Promise<APIResponse<{
    isLiked: boolean
    likeCount: number
  }>> {
    return request.delete(`/content/comments/${commentId}/like/`)
  },

  // ==================== 媒体文件管理 ====================

  /**
   * 上传图片
   */
  uploadImage(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<MediaFile>> {
    return request.upload('/content/media/image/', file, onProgress)
  },

  /**
   * 上传视频
   */
  uploadVideo(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<MediaFile>> {
    return request.upload('/content/media/video/', file, onProgress)
  },

  /**
   * 上传音频
   */
  uploadAudio(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<MediaFile>> {
    return request.upload('/content/media/audio/', file, onProgress)
  },

  /**
   * 上传文档
   */
  uploadDocument(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<MediaFile>> {
    return request.upload('/content/media/document/', file, onProgress)
  },

  /**
   * 获取媒体文件列表
   */
  getMediaFiles(params?: {
    page?: number
    pageSize?: number
    type?: 'image' | 'video' | 'audio' | 'document'
    search?: string
  }): Promise<APIResponse<{
    files: MediaFile[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/content/media/', { params })
  },

  /**
   * 删除媒体文件
   */
  deleteMediaFile(fileId: string): Promise<APIResponse<void>> {
    return request.delete(`/content/media/${fileId}/`)
  },

  // ==================== 内容举报 ====================

  /**
   * 举报内容
   */
  reportContent(data: {
    contentType: 'post' | 'comment'
    contentId: string
    reason: string
    description?: string
  }): Promise<APIResponse<ContentReport>> {
    return request.post('/content/reports/', data)
  },

  /**
   * 获取举报列表（管理员）
   */
  getReports(params?: {
    page?: number
    pageSize?: number
    status?: 'pending' | 'reviewed' | 'resolved'
    contentType?: 'post' | 'comment'
  }): Promise<APIResponse<{
    reports: ContentReport[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/content/reports/', { params })
  },

  /**
   * 处理举报（管理员）
   */
  handleReport(reportId: string, data: {
    action: 'approve' | 'reject' | 'delete_content'
    reason?: string
  }): Promise<APIResponse<ContentReport>> {
    return request.post(`/content/reports/${reportId}/handle/`, data)
  },

  // ==================== 搜索和统计 ====================

  /**
   * 搜索内容
   */
  searchContent(params: {
    query: string
    type?: 'posts' | 'comments'
    categoryId?: string
    authorId?: string
    dateRange?: {
      start: string
      end: string
    }
    page?: number
    pageSize?: number
  }): Promise<APIResponse<{
    posts?: Post[]
    comments?: Comment[]
    total: number
    hasNext: boolean
  }>> {
    return request.get('/content/search/', { params })
  },

  /**
   * 获取内容统计
   */
  getContentStats(): Promise<APIResponse<{
    totalPosts: number
    totalComments: number
    totalViews: number
    totalLikes: number
    todayPosts: number
    weeklyPosts: number
    monthlyPosts: number
    topCategories: Array<{
      category: ContentCategory
      postCount: number
    }>
  }>> {
    return request.get('/content/stats/')
  },

  /**
   * 获取用户内容统计
   */
  getUserContentStats(userId?: string): Promise<APIResponse<{
    postsCount: number
    commentsCount: number
    likesReceived: number
    viewsReceived: number
    bookmarksCount: number
  }>> {
    const url = userId ? `/content/stats/user/${userId}/` : '/content/stats/user/'
    return request.get(url)
  }
}
