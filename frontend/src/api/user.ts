import { request, type APIResponse } from './base'
import type { 
  User, 
  UserProfile, 
  Avatar, 
  LoginCredentials, 
  RegisterData,
  UserPreferences 
} from '@/types/user'

/**
 * 用户API接口 - 高内聚的用户相关接口
 * 职责：处理用户认证、个人资料、虚拟形象等API请求
 */
export const userAPI = {
  /**
   * 用户登录
   */
  login(credentials: LoginCredentials): Promise<APIResponse<{
    user: User
    profile: UserProfile
    avatar: Avatar
    tokens: {
      accessToken: string
      refreshToken: string
    }
    sessionId: string
  }>> {
    return request.post('/auth/login/', credentials)
  },

  /**
   * 用户注册
   */
  register(data: RegisterData): Promise<APIResponse<{
    userId: string
    status: string
    message: string
  }>> {
    return request.post('/auth/register/', data)
  },

  /**
   * 用户登出
   */
  logout(): Promise<APIResponse<void>> {
    return request.post('/auth/logout/')
  },

  /**
   * 刷新令牌
   */
  refreshToken(refreshToken: string): Promise<APIResponse<{
    accessToken: string
    refreshToken: string
  }>> {
    return request.post('/auth/refresh/', { refreshToken })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): Promise<APIResponse<{
    user: User
    profile: UserProfile
    avatar: Avatar
  }>> {
    return request.get('/users/me/')
  },

  /**
   * 更新用户资料
   */
  updateProfile(data: Partial<UserProfile>): Promise<APIResponse<UserProfile>> {
    return request.patch('/users/me/profile/', data)
  },

  /**
   * 更新虚拟形象
   */
  updateAvatar(data: Partial<Avatar>): Promise<APIResponse<Avatar>> {
    return request.patch('/users/me/avatar/', data)
  },

  /**
   * 上传头像
   */
  uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<{
    avatarUrl: string
  }>> {
    return request.upload('/users/me/avatar/upload/', file, onProgress)
  },

  /**
   * 更新用户偏好设置
   */
  updatePreferences(preferences: Partial<UserPreferences>): Promise<APIResponse<UserPreferences>> {
    return request.patch('/users/me/preferences/', preferences)
  },

  /**
   * 修改密码
   */
  changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<APIResponse<void>> {
    return request.post('/users/me/change-password/', data)
  },

  /**
   * 发送邮箱验证码
   */
  sendEmailVerification(): Promise<APIResponse<{
    message: string
  }>> {
    return request.post('/users/me/send-verification/')
  },

  /**
   * 验证邮箱
   */
  verifyEmail(code: string): Promise<APIResponse<{
    message: string
  }>> {
    return request.post('/users/me/verify-email/', { code })
  },

  /**
   * 获取用户统计信息
   */
  getUserStats(): Promise<APIResponse<{
    spacesCreated: number
    spacesJoined: number
    friendsCount: number
    messagesCount: number
    contentUploaded: number
  }>> {
    return request.get('/users/me/stats/')
  },

  /**
   * 搜索用户
   */
  searchUsers(params: {
    query: string
    page?: number
    pageSize?: number
  }): Promise<APIResponse<User[]>> {
    return request.get('/users/search/', { params })
  },

  /**
   * 获取用户详情（公开信息）
   */
  getUserDetail(userId: string): Promise<APIResponse<{
    user: User
    profile: UserProfile
    avatar: Avatar
    stats: any
  }>> {
    return request.get(`/users/${userId}/`)
  }
}