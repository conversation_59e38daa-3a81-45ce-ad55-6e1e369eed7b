// 状态管理模块入口文件
// 按业务域组织状态管理，体现高内聚设计原则

export { useUserStore } from './modules/user'
export { useSpaceStore } from './modules/space'
export { useSocialStore } from './modules/social'
export { useChatStore } from './modules/chat'
export { useContentStore } from './modules/content'
export { useEconomyStore } from './modules/economy'

// 状态管理类型定义
export type {
  UserState,
  SpaceState,
  SocialState,
  ChatState,
  ContentState,
  EconomyState
} from './types'