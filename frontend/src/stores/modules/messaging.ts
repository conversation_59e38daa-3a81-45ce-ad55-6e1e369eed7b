import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Conversation, Message, MessageAttachment } from '@/types/messaging'
import { messagingAPI } from '@/api/messaging'

/**
 * 消息系统状态管理模块
 * 职责：管理会话列表、消息记录、在线状态、输入状态等
 */
export const useMessagingStore = defineStore('messaging', () => {
  // ==================== 状态定义 ====================
  
  // 会话相关状态
  const conversations = ref<Conversation[]>([])
  const currentConversation = ref<Conversation | null>(null)
  const conversationLoading = ref(false)
  
  // 消息相关状态
  const messages = ref<Map<string, Message[]>>(new Map())
  const messageLoading = ref(false)
  const sendingMessages = ref<Set<string>>(new Set())
  
  // 未读消息状态
  const unreadCounts = ref<Map<string, number>>(new Map())
  const totalUnreadCount = ref(0)
  
  // 在线状态
  const onlineUsers = ref<Map<string, boolean>>(new Map())
  const userLastSeen = ref<Map<string, string>>(new Map())
  
  // 输入状态
  const typingUsers = ref<Map<string, Set<string>>>(new Map())
  
  // 错误状态
  const error = ref<string | null>(null)

  // ==================== 计算属性 ====================
  
  /**
   * 获取当前会话的消息列表
   */
  const currentMessages = computed(() => {
    if (!currentConversation.value) return []
    return messages.value.get(currentConversation.value.id) || []
  })
  
  /**
   * 获取当前会话的未读数量
   */
  const currentUnreadCount = computed(() => {
    if (!currentConversation.value) return 0
    return unreadCounts.value.get(currentConversation.value.id) || 0
  })
  
  /**
   * 获取当前会话的输入用户
   */
  const currentTypingUsers = computed(() => {
    if (!currentConversation.value) return []
    const typingSet = typingUsers.value.get(currentConversation.value.id)
    return typingSet ? Array.from(typingSet) : []
  })
  
  /**
   * 检查用户是否在线
   */
  const isUserOnline = computed(() => (userId: string) => {
    return onlineUsers.value.get(userId) || false
  })

  // ==================== 会话管理 ====================
  
  /**
   * 获取会话列表
   */
  const fetchConversations = async (params?: any) => {
    conversationLoading.value = true
    error.value = null
    
    try {
      const response = await messagingAPI.getConversations(params)
      
      if (response.success) {
        conversations.value = response.data.conversations
        
        // 更新未读数量
        response.data.conversations.forEach(conv => {
          if (conv.unreadCount) {
            unreadCounts.value.set(conv.id, conv.unreadCount)
          }
        })
        
        updateTotalUnreadCount()
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      conversationLoading.value = false
    }
  }
  
  /**
   * 设置当前会话
   */
  const setCurrentConversation = async (conversationId: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      currentConversation.value = conversation
      
      // 如果没有加载过消息，则加载
      if (!messages.value.has(conversationId)) {
        await fetchMessages(conversationId)
      }
      
      // 标记为已读
      await markAsRead(conversationId)
    }
  }
  
  /**
   * 创建私聊会话
   */
  const createPrivateConversation = async (userId: string) => {
    try {
      const response = await messagingAPI.createPrivateConversation(userId)
      
      if (response.success) {
        const newConversation = response.data
        
        // 添加到会话列表
        const existingIndex = conversations.value.findIndex(c => c.id === newConversation.id)
        if (existingIndex >= 0) {
          conversations.value[existingIndex] = newConversation
        } else {
          conversations.value.unshift(newConversation)
        }
        
        return newConversation
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  // ==================== 消息管理 ====================
  
  /**
   * 获取消息列表
   */
  const fetchMessages = async (conversationId: string, params?: any) => {
    messageLoading.value = true
    error.value = null
    
    try {
      const response = await messagingAPI.getMessages(conversationId, params)
      
      if (response.success) {
        const existingMessages = messages.value.get(conversationId) || []
        
        if (params?.beforeMessageId) {
          // 加载更早的消息（向前翻页）
          messages.value.set(conversationId, [...response.data.messages, ...existingMessages])
        } else {
          // 加载最新消息
          messages.value.set(conversationId, response.data.messages)
        }
        
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      messageLoading.value = false
    }
  }
  
  /**
   * 发送文本消息
   */
  const sendTextMessage = async (conversationId: string, content: string, replyToId?: string) => {
    const tempId = `temp_${Date.now()}`
    sendingMessages.value.add(tempId)
    
    // 创建临时消息对象
    const tempMessage: Message = {
      id: tempId,
      conversationId,
      senderId: '', // 会被后端填充
      type: 'text',
      content,
      replyToId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'sending',
      reactions: [],
      attachments: []
    }
    
    // 添加到消息列表
    const conversationMessages = messages.value.get(conversationId) || []
    messages.value.set(conversationId, [...conversationMessages, tempMessage])
    
    try {
      const response = await messagingAPI.sendTextMessage(conversationId, {
        content,
        replyToId
      })
      
      if (response.success) {
        // 替换临时消息
        const updatedMessages = conversationMessages.map(msg => 
          msg.id === tempId ? response.data : msg
        )
        messages.value.set(conversationId, updatedMessages)
        
        return response.data
      }
    } catch (err: any) {
      // 标记消息发送失败
      const updatedMessages = conversationMessages.map(msg => 
        msg.id === tempId ? { ...msg, status: 'failed' } : msg
      )
      messages.value.set(conversationId, updatedMessages)
      
      error.value = err.message
      throw err
    } finally {
      sendingMessages.value.delete(tempId)
    }
  }
  
  /**
   * 发送图片消息
   */
  const sendImageMessage = async (conversationId: string, file: File, caption?: string) => {
    try {
      // 先上传图片
      const uploadResponse = await messagingAPI.uploadImage(file)
      
      if (uploadResponse.success) {
        // 发送图片消息
        const response = await messagingAPI.sendImageMessage(conversationId, {
          imageUrl: uploadResponse.data.imageUrl,
          thumbnailUrl: uploadResponse.data.thumbnailUrl,
          width: uploadResponse.data.width,
          height: uploadResponse.data.height,
          caption
        })
        
        if (response.success) {
          // 添加到消息列表
          const conversationMessages = messages.value.get(conversationId) || []
          messages.value.set(conversationId, [...conversationMessages, response.data])
          
          return response.data
        }
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 标记消息为已读
   */
  const markAsRead = async (conversationId: string, messageId?: string) => {
    try {
      await messagingAPI.markAsRead(conversationId, messageId)
      
      // 更新本地未读数量
      unreadCounts.value.set(conversationId, 0)
      updateTotalUnreadCount()
      
      // 更新会话的最后读取时间
      const conversation = conversations.value.find(c => c.id === conversationId)
      if (conversation) {
        conversation.lastReadAt = new Date().toISOString()
      }
    } catch (err: any) {
      console.error('标记已读失败:', err)
    }
  }

  // ==================== WebSocket事件处理 ====================
  
  /**
   * 处理新消息（WebSocket）
   */
  const handleNewMessage = (messageData: any) => {
    const message = messageData.message
    const conversationId = message.conversationId
    
    // 添加到消息列表
    const conversationMessages = messages.value.get(conversationId) || []
    messages.value.set(conversationId, [...conversationMessages, message])
    
    // 更新会话列表中的最后消息
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.lastMessage = message
      conversation.lastMessageAt = message.createdAt
      
      // 如果不是当前会话，增加未读数量
      if (currentConversation.value?.id !== conversationId) {
        const currentUnread = unreadCounts.value.get(conversationId) || 0
        unreadCounts.value.set(conversationId, currentUnread + 1)
        updateTotalUnreadCount()
      }
      
      // 将会话移到列表顶部
      const index = conversations.value.findIndex(c => c.id === conversationId)
      if (index > 0) {
        conversations.value.splice(index, 1)
        conversations.value.unshift(conversation)
      }
    }
  }
  
  /**
   * 处理消息状态更新（WebSocket）
   */
  const handleMessageStatusUpdate = (data: any) => {
    const { conversationId, messageId, status } = data
    
    const conversationMessages = messages.value.get(conversationId)
    if (conversationMessages) {
      const messageIndex = conversationMessages.findIndex(m => m.id === messageId)
      if (messageIndex >= 0) {
        conversationMessages[messageIndex].status = status
      }
    }
  }
  
  /**
   * 更新用户在线状态
   */
  const updateUserOnlineStatus = (userId: string, isOnline: boolean, lastSeen?: string) => {
    onlineUsers.value.set(userId, isOnline)
    if (lastSeen) {
      userLastSeen.value.set(userId, lastSeen)
    }
  }
  
  /**
   * 更新输入状态
   */
  const updateTypingStatus = (conversationId: string, userId: string, isTyping: boolean) => {
    const typingSet = typingUsers.value.get(conversationId) || new Set()
    
    if (isTyping) {
      typingSet.add(userId)
    } else {
      typingSet.delete(userId)
    }
    
    typingUsers.value.set(conversationId, typingSet)
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 更新总未读数量
   */
  const updateTotalUnreadCount = () => {
    let total = 0
    unreadCounts.value.forEach(count => {
      total += count
    })
    totalUnreadCount.value = total
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    conversations.value = []
    currentConversation.value = null
    messages.value.clear()
    unreadCounts.value.clear()
    totalUnreadCount.value = 0
    onlineUsers.value.clear()
    userLastSeen.value.clear()
    typingUsers.value.clear()
    error.value = null
  }

  return {
    // 状态
    conversations,
    currentConversation,
    conversationLoading,
    messageLoading,
    sendingMessages,
    unreadCounts,
    totalUnreadCount,
    onlineUsers,
    userLastSeen,
    typingUsers,
    error,
    
    // 计算属性
    currentMessages,
    currentUnreadCount,
    currentTypingUsers,
    isUserOnline,
    
    // 动作
    fetchConversations,
    setCurrentConversation,
    createPrivateConversation,
    fetchMessages,
    sendTextMessage,
    sendImageMessage,
    markAsRead,
    
    // WebSocket事件处理
    handleNewMessage,
    handleMessageStatusUpdate,
    updateUserOnlineStatus,
    updateTypingStatus,
    
    // 辅助方法
    resetState
  }
}, {
  persist: {
    key: 'messaging-store',
    storage: sessionStorage,
    paths: ['conversations', 'unreadCounts', 'totalUnreadCount']
  }
})
