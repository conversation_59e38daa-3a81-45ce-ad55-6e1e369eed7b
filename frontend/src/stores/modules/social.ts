import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Friendship, Follow, Group, SocialActivity } from '@/types/social'
import { socialAPI } from '@/api/social'

/**
 * 社交功能状态管理模块
 * 职责：管理好友关系、关注系统、群组、社交活动等
 */
export const useSocialStore = defineStore('social', () => {
  // ==================== 状态定义 ====================
  
  // 好友相关状态
  const friends = ref<Friendship[]>([])
  const friendRequests = ref<Friendship[]>([])
  const blockedUsers = ref<any[]>([])
  const friendsLoading = ref(false)
  
  // 关注相关状态
  const following = ref<Follow[]>([])
  const followers = ref<Follow[]>([])
  const followLoading = ref(false)
  
  // 群组相关状态
  const groups = ref<Group[]>([])
  const joinedGroups = ref<Group[]>([])
  const groupInvitations = ref<any[]>([])
  const groupsLoading = ref(false)
  
  // 社交活动状态
  const activities = ref<SocialActivity[]>([])
  const activitiesLoading = ref(false)
  
  // 推荐状态
  const recommendedUsers = ref<any[]>([])
  const recommendedGroups = ref<Group[]>([])
  
  // 错误状态
  const error = ref<string | null>(null)

  // ==================== 计算属性 ====================
  
  /**
   * 好友数量
   */
  const friendsCount = computed(() => 
    friends.value.filter(f => f.status === 'accepted').length
  )
  
  /**
   * 待处理的好友请求数量
   */
  const pendingRequestsCount = computed(() => 
    friendRequests.value.filter(f => f.status === 'pending').length
  )
  
  /**
   * 关注数量
   */
  const followingCount = computed(() => following.value.length)
  
  /**
   * 粉丝数量
   */
  const followersCount = computed(() => followers.value.length)
  
  /**
   * 已加入的群组数量
   */
  const joinedGroupsCount = computed(() => joinedGroups.value.length)
  
  /**
   * 检查是否为好友
   */
  const isFriend = computed(() => (userId: string) => {
    return friends.value.some(f => 
      (f.friendId === userId || f.userId === userId) && f.status === 'accepted'
    )
  })
  
  /**
   * 检查是否已关注
   */
  const isFollowing = computed(() => (userId: string) => {
    return following.value.some(f => f.followedId === userId)
  })
  
  /**
   * 检查是否已加入群组
   */
  const isGroupMember = computed(() => (groupId: string) => {
    return joinedGroups.value.some(g => g.id === groupId)
  })

  // ==================== 好友管理 ====================
  
  /**
   * 获取好友列表
   */
  const fetchFriends = async (params?: any) => {
    friendsLoading.value = true
    error.value = null
    
    try {
      const response = await socialAPI.getFriends(params)
      
      if (response.success) {
        if (params?.status === 'pending') {
          friendRequests.value = response.data.friends
        } else {
          friends.value = response.data.friends
        }
        
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      friendsLoading.value = false
    }
  }
  
  /**
   * 发送好友请求
   */
  const sendFriendRequest = async (userId: string, message?: string) => {
    try {
      const response = await socialAPI.sendFriendRequest(userId, message)
      
      if (response.success) {
        // 添加到好友请求列表
        friendRequests.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 接受好友请求
   */
  const acceptFriendRequest = async (requestId: string) => {
    try {
      const response = await socialAPI.acceptFriendRequest(requestId)
      
      if (response.success) {
        // 从请求列表中移除
        const index = friendRequests.value.findIndex(r => r.id === requestId)
        if (index >= 0) {
          friendRequests.value.splice(index, 1)
        }
        
        // 添加到好友列表
        friends.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 拒绝好友请求
   */
  const rejectFriendRequest = async (requestId: string) => {
    try {
      await socialAPI.rejectFriendRequest(requestId)
      
      // 从请求列表中移除
      const index = friendRequests.value.findIndex(r => r.id === requestId)
      if (index >= 0) {
        friendRequests.value.splice(index, 1)
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 删除好友
   */
  const removeFriend = async (friendshipId: string) => {
    try {
      await socialAPI.removeFriend(friendshipId)
      
      // 从好友列表中移除
      const index = friends.value.findIndex(f => f.id === friendshipId)
      if (index >= 0) {
        friends.value.splice(index, 1)
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  // ==================== 关注系统 ====================
  
  /**
   * 获取关注列表
   */
  const fetchFollowing = async (params?: any) => {
    followLoading.value = true
    error.value = null
    
    try {
      const response = await socialAPI.getFollowing(params)
      
      if (response.success) {
        following.value = response.data.following
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      followLoading.value = false
    }
  }
  
  /**
   * 获取粉丝列表
   */
  const fetchFollowers = async (params?: any) => {
    followLoading.value = true
    error.value = null
    
    try {
      const response = await socialAPI.getFollowers(params)
      
      if (response.success) {
        followers.value = response.data.followers
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      followLoading.value = false
    }
  }
  
  /**
   * 关注用户
   */
  const followUser = async (userId: string) => {
    try {
      const response = await socialAPI.followUser(userId)
      
      if (response.success) {
        following.value.push(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 取消关注用户
   */
  const unfollowUser = async (userId: string) => {
    try {
      await socialAPI.unfollowUser(userId)
      
      // 从关注列表中移除
      const index = following.value.findIndex(f => f.followedId === userId)
      if (index >= 0) {
        following.value.splice(index, 1)
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  // ==================== 群组管理 ====================
  
  /**
   * 获取群组列表
   */
  const fetchGroups = async (params?: any) => {
    groupsLoading.value = true
    error.value = null
    
    try {
      const response = await socialAPI.getGroups(params)
      
      if (response.success) {
        groups.value = response.data.groups
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      groupsLoading.value = false
    }
  }
  
  /**
   * 创建群组
   */
  const createGroup = async (groupData: any) => {
    try {
      const response = await socialAPI.createGroup(groupData)
      
      if (response.success) {
        groups.value.unshift(response.data)
        joinedGroups.value.unshift(response.data)
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 加入群组
   */
  const joinGroup = async (groupId: string, message?: string) => {
    try {
      const response = await socialAPI.joinGroup(groupId, message)
      
      if (response.success) {
        // 添加到已加入群组列表
        const group = groups.value.find(g => g.id === groupId)
        if (group && !joinedGroups.value.some(g => g.id === groupId)) {
          joinedGroups.value.push(group)
        }
        
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 离开群组
   */
  const leaveGroup = async (groupId: string) => {
    try {
      await socialAPI.leaveGroup(groupId)
      
      // 从已加入群组列表中移除
      const index = joinedGroups.value.findIndex(g => g.id === groupId)
      if (index >= 0) {
        joinedGroups.value.splice(index, 1)
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  // ==================== 社交活动 ====================
  
  /**
   * 获取社交活动动态
   */
  const fetchActivities = async (params?: any) => {
    activitiesLoading.value = true
    error.value = null
    
    try {
      const response = await socialAPI.getSocialActivities(params)
      
      if (response.success) {
        if (params?.page && params.page > 1) {
          // 追加到现有列表
          activities.value.push(...response.data.activities)
        } else {
          // 替换列表
          activities.value = response.data.activities
        }
        
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      activitiesLoading.value = false
    }
  }
  
  /**
   * 获取推荐用户
   */
  const fetchRecommendedUsers = async (params?: any) => {
    try {
      const response = await socialAPI.getRecommendedUsers(params)
      
      if (response.success) {
        recommendedUsers.value = response.data
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }
  
  /**
   * 获取推荐群组
   */
  const fetchRecommendedGroups = async (params?: any) => {
    try {
      const response = await socialAPI.getRecommendedGroups(params)
      
      if (response.success) {
        recommendedGroups.value = response.data
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  // ==================== WebSocket事件处理 ====================
  
  /**
   * 处理好友请求（WebSocket）
   */
  const handleFriendRequest = (data: any) => {
    friendRequests.value.unshift(data)
  }
  
  /**
   * 处理好友请求状态更新（WebSocket）
   */
  const handleFriendRequestUpdate = (data: any) => {
    const { requestId, status } = data
    
    const index = friendRequests.value.findIndex(r => r.id === requestId)
    if (index >= 0) {
      if (status === 'accepted') {
        // 移动到好友列表
        const request = friendRequests.value.splice(index, 1)[0]
        friends.value.push({ ...request, status: 'accepted' })
      } else {
        // 更新状态或移除
        friendRequests.value.splice(index, 1)
      }
    }
  }
  
  /**
   * 处理新关注者（WebSocket）
   */
  const handleNewFollower = (data: any) => {
    followers.value.unshift(data)
  }
  
  /**
   * 处理群组邀请（WebSocket）
   */
  const handleGroupInvitation = (data: any) => {
    groupInvitations.value.unshift(data)
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 重置状态
   */
  const resetState = () => {
    friends.value = []
    friendRequests.value = []
    blockedUsers.value = []
    following.value = []
    followers.value = []
    groups.value = []
    joinedGroups.value = []
    groupInvitations.value = []
    activities.value = []
    recommendedUsers.value = []
    recommendedGroups.value = []
    error.value = null
  }

  return {
    // 状态
    friends,
    friendRequests,
    blockedUsers,
    friendsLoading,
    following,
    followers,
    followLoading,
    groups,
    joinedGroups,
    groupInvitations,
    groupsLoading,
    activities,
    activitiesLoading,
    recommendedUsers,
    recommendedGroups,
    error,
    
    // 计算属性
    friendsCount,
    pendingRequestsCount,
    followingCount,
    followersCount,
    joinedGroupsCount,
    isFriend,
    isFollowing,
    isGroupMember,
    
    // 动作
    fetchFriends,
    sendFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest,
    removeFriend,
    fetchFollowing,
    fetchFollowers,
    followUser,
    unfollowUser,
    fetchGroups,
    createGroup,
    joinGroup,
    leaveGroup,
    fetchActivities,
    fetchRecommendedUsers,
    fetchRecommendedGroups,
    
    // WebSocket事件处理
    handleFriendRequest,
    handleFriendRequestUpdate,
    handleNewFollower,
    handleGroupInvitation,
    
    // 辅助方法
    resetState
  }
}, {
  persist: {
    key: 'social-store',
    storage: sessionStorage,
    paths: ['friends', 'following', 'followers', 'joinedGroups']
  }
})
