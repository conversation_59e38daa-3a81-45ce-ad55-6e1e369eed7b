import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserProfile, Avatar, LoginCredentials, RegisterData } from '@/types/user'
import { userAPI } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'

/**
 * 用户状态管理模块 - 高内聚设计
 * 职责：管理用户认证、个人资料、虚拟形象等相关状态
 */
export const useUserStore = defineStore('user', () => {
  // 状态定义（高内聚的用户相关状态）
  const currentUser = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const userAvatar = ref<Avatar | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 用户偏好设置
  const preferences = ref({
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      push: true,
      sound: true
    },
    privacy: {
      showOnlineStatus: true,
      allowFriendRequests: true,
      showProfile: 'friends'
    }
  })

  // 计算属性（内聚的派生状态）
  const userDisplayName = computed(() => 
    userProfile.value?.nickname || currentUser.value?.username || '未知用户'
  )
  
  const userAvatarUrl = computed(() => 
    userAvatar.value?.thumbnailUrl || userProfile.value?.avatarUrl || '/default-avatar.png'
  )
  
  const hasPermission = computed(() => (permission: string) => 
    currentUser.value?.permissions?.includes(permission) || false
  )
  
  const isVerified = computed(() => 
    currentUser.value?.isVerified || false
  )

  // 动作方法（内聚的用户操作）
  
  /**
   * 初始化认证状态
   */
  const initializeAuth = async () => {
    const token = getToken()
    if (token) {
      try {
        await getCurrentUser()
      } catch (error) {
        console.error('初始化认证失败:', error)
        await logout()
      }
    }
  }

  /**
   * 用户登录
   */
  const login = async (credentials: LoginCredentials) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await userAPI.login(credentials)
      
      if (response.success) {
        // 存储令牌
        setToken(response.data.tokens.accessToken, response.data.tokens.refreshToken)
        
        // 设置用户状态
        currentUser.value = response.data.user
        userProfile.value = response.data.profile
        userAvatar.value = response.data.avatar
        isAuthenticated.value = true
        
        return response.data
      } else {
        throw new Error(response.error?.message || '登录失败')
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户注册
   */
  const register = async (registerData: RegisterData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await userAPI.register(registerData)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.error?.message || '注册失败')
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取当前用户信息
   */
  const getCurrentUser = async () => {
    try {
      const response = await userAPI.getCurrentUser()
      
      if (response.success) {
        currentUser.value = response.data.user
        userProfile.value = response.data.profile
        userAvatar.value = response.data.avatar
        isAuthenticated.value = true
        
        return response.data
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 更新用户资料
   */
  const updateProfile = async (profileData: Partial<UserProfile>) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await userAPI.updateProfile(profileData)
      
      if (response.success) {
        userProfile.value = { ...userProfile.value, ...response.data }
        return response.data
      } else {
        throw new Error(response.error?.message || '更新资料失败')
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      await userAPI.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清理状态
      currentUser.value = null
      userProfile.value = null
      userAvatar.value = null
      isAuthenticated.value = false
      error.value = null
      
      // 清理本地存储
      removeToken()
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    currentUser.value = null
    userProfile.value = null
    userAvatar.value = null
    isAuthenticated.value = false
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    currentUser,
    userProfile,
    userAvatar,
    isAuthenticated,
    loading,
    error,
    preferences,
    
    // 计算属性
    userDisplayName,
    userAvatarUrl,
    hasPermission,
    isVerified,
    
    // 动作
    initializeAuth,
    login,
    register,
    getCurrentUser,
    updateProfile,
    logout,
    resetState
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['preferences']
  }
})