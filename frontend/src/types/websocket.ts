// WebSocket通信类型定义

export interface WebSocketMessage {
  id: string
  type: string
  timestamp: string
  version: string
}

export interface ClientMessage extends WebSocketMessage {
  data: any
  requestId?: string
  ack?: boolean
  onAck?: (response: ServerMessage) => void
}

export interface ServerMessage extends WebSocketMessage {
  data: any
  requestId?: string
  success?: boolean
  error?: {
    code: string
    message: string
    details?: any
  }
}

export interface ChatMessage {
  id: string
  senderId: string
  senderName: string
  senderAvatar?: string
  content: string
  messageType: 'text' | 'image' | 'audio' | 'video' | 'file' | 'emoji' | 'system'
  target: {
    type: 'user' | 'space' | 'group'
    id: string
  }
  metadata?: Record<string, any>
  timestamp: string
  isRead: boolean
  isEdited: boolean
  replyTo?: string
}

export interface PositionUpdate {
  userId: string
  spaceId: string
  position: {
    x: number
    y: number
    z: number
  }
  rotation: {
    x: number
    y: number
    z: number
  }
  timestamp: string
}

export interface SpaceEvent {
  type: 'user_join' | 'user_leave' | 'user_move' | 'object_create' | 'object_update' | 'object_delete'
  spaceId: string
  userId: string
  data: any
  timestamp: string
}

export interface NotificationMessage {
  id: string
  type: 'friend_request' | 'space_invite' | 'message' | 'system' | 'achievement'
  title: string
  content: string
  data?: any
  isRead: boolean
  timestamp: string
}