// 用户相关类型定义 - 高内聚的用户类型

export interface User {
  id: string
  username: string
  email: string
  isActive: boolean
  isVerified: boolean
  status: 'active' | 'inactive' | 'suspended' | 'banned'
  userType: 'regular' | 'premium' | 'vip' | 'admin'
  loginCount: number
  lastLoginIp?: string
  permissions: string[]
  createdAt: string
  updatedAt: string
}

export interface UserProfile {
  id: string
  userId: string
  nickname: string
  bio?: string
  birthDate?: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  avatarUrl?: string
  location?: string
  website?: string
  socialLinks: Record<string, string>
  privacySettings: PrivacySettings
  createdAt: string
  updatedAt: string
}

export interface Avatar {
  id: string
  userId: string
  name: string
  modelUrl: string
  thumbnailUrl?: string
  configData: AvatarConfig
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface AvatarConfig {
  gender: 'male' | 'female'
  skinColor: string
  hairColor: string
  eyeColor: string
  height: number
  bodyType: 'slim' | 'normal' | 'athletic' | 'heavy'
  clothing: {
    top: string
    bottom: string
    shoes: string
    accessories: string[]
  }
  animations: {
    idle: string
    walk: string
    run: string
    jump: string
    wave: string
  }
}

export interface PrivacySettings {
  showOnlineStatus: boolean
  allowFriendRequests: boolean
  showProfile: 'public' | 'friends' | 'private'
  showEmail: boolean
  showPhone: boolean
  showLocation: boolean
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  notificationSettings: NotificationSettings
  privacySettings: PrivacySettings
  audioSettings: AudioSettings
  videoSettings: VideoSettings
  controlSettings: ControlSettings
  accessibilitySettings: AccessibilitySettings
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  sound: boolean
  vibration: boolean
  friendRequests: boolean
  messages: boolean
  spaceInvites: boolean
  systemUpdates: boolean
}

export interface AudioSettings {
  masterVolume: number
  musicVolume: number
  effectVolume: number
  voiceVolume: number
  microphoneEnabled: boolean
  speakerEnabled: boolean
}

export interface VideoSettings {
  quality: 'low' | 'medium' | 'high' | 'ultra'
  frameRate: 30 | 60 | 120
  enableVsync: boolean
  enableAntialiasing: boolean
  shadowQuality: 'low' | 'medium' | 'high'
  textureQuality: 'low' | 'medium' | 'high'
}

export interface ControlSettings {
  mouseSensitivity: number
  invertMouseY: boolean
  keyBindings: Record<string, string>
  gamepadEnabled: boolean
  vrEnabled: boolean
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large' | 'extra-large'
  highContrast: boolean
  reduceMotion: boolean
  screenReader: boolean
  colorBlindSupport: boolean
}

export interface LoginCredentials {
  identifier: string // 用户名或邮箱
  password: string
  rememberMe?: boolean
  deviceInfo?: DeviceInfo
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
  nickname?: string
  agreeToTerms: boolean
  subscribeNewsletter?: boolean
}

export interface DeviceInfo {
  type: 'web' | 'mobile' | 'desktop' | 'vr' | 'ar'
  userAgent: string
  platform: string
  screenResolution: string
  ipAddress?: string
}

export interface UserSession {
  id: string
  userId: string
  sessionId: string
  deviceType: string
  deviceInfo: DeviceInfo
  ipAddress: string
  location?: string
  isActive: boolean
  lastActivity: string
  expiresAt: string
  createdAt: string
}

export interface UserActivity {
  id: string
  userId: string
  activityType: 'login' | 'logout' | 'space_join' | 'space_leave' | 'friend_add' | 'content_upload' | 'message_send' | 'profile_update' | 'avatar_update'
  description: string
  metadata: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: string
}

export interface UserStats {
  spacesCreated: number
  spacesJoined: number
  friendsCount: number
  messagesCount: number
  contentUploaded: number
  totalOnlineTime: number
  lastActiveDate: string
  joinDate: string
}