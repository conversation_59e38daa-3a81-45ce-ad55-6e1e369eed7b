<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载进度条 -->
    <div v-if="loading" class="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80">
      <el-loading-spinner size="large" />
    </div>
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    
    <!-- 全局通知 -->
    <el-notification-container />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { useWebSocket } from '@/composables/useWebSocket'

const router = useRouter()
const userStore = useUserStore()
const { connect, isConnected } = useWebSocket()

const loading = ref(true)

onMounted(async () => {
  try {
    // 初始化用户状态
    await userStore.initializeAuth()
    
    // 如果用户已登录，建立WebSocket连接
    if (userStore.isAuthenticated) {
      connect(import.meta.env.VITE_WS_BASE_URL)
    }
    
    // 路由守卫
    router.beforeEach((to, from, next) => {
      // 检查路由权限
      if (to.meta.requiresAuth && !userStore.isAuthenticated) {
        next('/login')
      } else {
        next()
      }
    })
    
  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>