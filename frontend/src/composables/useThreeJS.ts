import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

/**
 * Three.js组合式函数 - 3D渲染功能内聚
 * 职责：管理3D场景、相机、渲染器、控制器等
 */
export function useThreeJS(containerRef: Ref<HTMLElement | null>) {
  // 核心对象
  const scene = ref<THREE.Scene | null>(null)
  const camera = ref<THREE.PerspectiveCamera | null>(null)
  const renderer = ref<THREE.WebGLRenderer | null>(null)
  const controls = ref<OrbitControls | null>(null)
  
  // 加载器
  const gltfLoader = new GLTFLoader()
  const textureLoader = new THREE.TextureLoader()
  
  // 状态
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 动画
  let animationId: number | null = null
  const clock = new THREE.Clock()

  /**
   * 初始化Three.js场景
   */
  const initScene = () => {
    if (!containerRef.value || isInitialized.value) return

    try {
      // 创建场景
      scene.value = new THREE.Scene()
      scene.value.background = new THREE.Color(0x87CEEB) // 天空蓝

      // 创建相机
      const container = containerRef.value
      const width = container.clientWidth
      const height = container.clientHeight
      
      camera.value = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
      camera.value.position.set(0, 5, 10)

      // 创建渲染器
      renderer.value = new THREE.WebGLRenderer({ 
        antialias: true,
        alpha: true 
      })
      renderer.value.setSize(width, height)
      renderer.value.setPixelRatio(window.devicePixelRatio)
      renderer.value.shadowMap.enabled = true
      renderer.value.shadowMap.type = THREE.PCFSoftShadowMap
      renderer.value.outputColorSpace = THREE.SRGBColorSpace
      
      container.appendChild(renderer.value.domElement)

      // 创建控制器
      controls.value = new OrbitControls(camera.value, renderer.value.domElement)
      controls.value.enableDamping = true
      controls.value.dampingFactor = 0.05
      controls.value.maxPolarAngle = Math.PI / 2

      // 添加基础光照
      setupLighting()

      // 添加基础几何体
      setupBasicGeometry()

      // 开始渲染循环
      startRenderLoop()

      isInitialized.value = true
      console.log('Three.js场景初始化成功')

    } catch (err: any) {
      error.value = err.message
      console.error('Three.js初始化失败:', err)
    }
  }

  /**
   * 设置光照
   */
  const setupLighting = () => {
    if (!scene.value) return

    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
    scene.value.add(ambientLight)

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 10, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.near = 0.5
    directionalLight.shadow.camera.far = 50
    scene.value.add(directionalLight)

    // 点光源
    const pointLight = new THREE.PointLight(0xffffff, 0.5, 100)
    pointLight.position.set(0, 10, 0)
    scene.value.add(pointLight)
  }

  /**
   * 设置基础几何体
   */
  const setupBasicGeometry = () => {
    if (!scene.value) return

    // 地面
    const groundGeometry = new THREE.PlaneGeometry(50, 50)
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 })
    const ground = new THREE.Mesh(groundGeometry, groundMaterial)
    ground.rotation.x = -Math.PI / 2
    ground.receiveShadow = true
    scene.value.add(ground)

    // 示例立方体
    const cubeGeometry = new THREE.BoxGeometry(2, 2, 2)
    const cubeMaterial = new THREE.MeshPhongMaterial({ color: 0x00ff00 })
    const cube = new THREE.Mesh(cubeGeometry, cubeMaterial)
    cube.position.set(0, 1, 0)
    cube.castShadow = true
    scene.value.add(cube)

    // 示例球体
    const sphereGeometry = new THREE.SphereGeometry(1, 32, 32)
    const sphereMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 })
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
    sphere.position.set(4, 1, 0)
    sphere.castShadow = true
    scene.value.add(sphere)
  }

  /**
   * 开始渲染循环
   */
  const startRenderLoop = () => {
    const animate = () => {
      animationId = requestAnimationFrame(animate)

      // 更新控制器
      if (controls.value) {
        controls.value.update()
      }

      // 渲染场景
      if (renderer.value && scene.value && camera.value) {
        renderer.value.render(scene.value, camera.value)
      }
    }

    animate()
  }

  /**
   * 停止渲染循环
   */
  const stopRenderLoop = () => {
    if (animationId) {
      cancelAnimationFrame(animationId)
      animationId = null
    }
  }

  /**
   * 加载GLTF模型
   */
  const loadGLTFModel = async (url: string, position?: THREE.Vector3, scale?: THREE.Vector3) => {
    if (!scene.value) return null

    isLoading.value = true
    error.value = null

    try {
      const gltf = await new Promise<any>((resolve, reject) => {
        gltfLoader.load(
          url,
          (gltf) => resolve(gltf),
          (progress) => {
            console.log('模型加载进度:', (progress.loaded / progress.total * 100) + '%')
          },
          (error) => reject(error)
        )
      })

      const model = gltf.scene
      
      if (position) {
        model.position.copy(position)
      }
      
      if (scale) {
        model.scale.copy(scale)
      }

      // 启用阴影
      model.traverse((child: any) => {
        if (child.isMesh) {
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      scene.value.add(model)
      console.log('GLTF模型加载成功:', url)
      
      return model

    } catch (err: any) {
      error.value = err.message
      console.error('GLTF模型加载失败:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 添加对象到场景
   */
  const addToScene = (object: THREE.Object3D) => {
    if (scene.value) {
      scene.value.add(object)
    }
  }

  /**
   * 从场景移除对象
   */
  const removeFromScene = (object: THREE.Object3D) => {
    if (scene.value) {
      scene.value.remove(object)
    }
  }

  /**
   * 设置相机位置
   */
  const setCameraPosition = (x: number, y: number, z: number) => {
    if (camera.value) {
      camera.value.position.set(x, y, z)
    }
  }

  /**
   * 设置相机目标
   */
  const setCameraTarget = (x: number, y: number, z: number) => {
    if (controls.value) {
      controls.value.target.set(x, y, z)
      controls.value.update()
    }
  }

  /**
   * 处理窗口大小变化
   */
  const handleResize = () => {
    if (!containerRef.value || !camera.value || !renderer.value) return

    const width = containerRef.value.clientWidth
    const height = containerRef.value.clientHeight

    camera.value.aspect = width / height
    camera.value.updateProjectionMatrix()

    renderer.value.setSize(width, height)
  }

  /**
   * 清理资源
   */
  const dispose = () => {
    stopRenderLoop()

    if (renderer.value) {
      renderer.value.dispose()
      if (containerRef.value && renderer.value.domElement) {
        containerRef.value.removeChild(renderer.value.domElement)
      }
    }

    if (controls.value) {
      controls.value.dispose()
    }

    // 清理场景中的所有对象
    if (scene.value) {
      scene.value.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.geometry.dispose()
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
      scene.value.clear()
    }

    isInitialized.value = false
  }

  // 监听窗口大小变化
  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    dispose()
  })

  return {
    // 状态
    scene,
    camera,
    renderer,
    controls,
    isInitialized,
    isLoading,
    error,
    
    // 方法
    initScene,
    loadGLTFModel,
    addToScene,
    removeFromScene,
    setCameraPosition,
    setCameraTarget,
    handleResize,
    dispose
  }
}