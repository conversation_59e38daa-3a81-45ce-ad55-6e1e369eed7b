import { ref, reactive, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import type { WebSocketMessage, ClientMessage, ServerMessage } from '@/types/websocket'

/**
 * WebSocket组合式函数 - 功能内聚的实时通信逻辑
 * 职责：管理WebSocket连接、消息发送接收、连接状态等
 */
export function useWebSocket() {
  const userStore = useUserStore()
  
  // 连接状态
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = ref(1000)
  
  // 消息处理器映射
  const messageHandlers = reactive<Map<string, Function>>(new Map())
  
  // 待确认消息队列
  const pendingMessages = reactive<Map<string, ClientMessage>>(new Map())
  
  // 连接统计
  const stats = reactive({
    messagesSent: 0,
    messagesReceived: 0,
    reconnectCount: 0,
    lastConnectedAt: null as string | null,
    lastDisconnectedAt: null as string | null
  })

  /**
   * 建立WebSocket连接
   */
  const connect = (url: string) => {
    if (isConnecting.value || isConnected.value) {
      return
    }

    isConnecting.value = true

    try {
      const token = localStorage.getItem('access_token')
      const wsUrl = `${url}?token=${token}`
      
      socket.value = new WebSocket(wsUrl)
      
      socket.value.onopen = handleOpen
      socket.value.onmessage = handleMessage
      socket.value.onclose = handleClose
      socket.value.onerror = handleError
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      isConnecting.value = false
      ElMessage.error('WebSocket连接失败')
    }
  }

  /**
   * 处理连接打开
   */
  const handleOpen = () => {
    console.log('WebSocket连接已建立')
    isConnected.value = true
    isConnecting.value = false
    reconnectAttempts.value = 0
    reconnectDelay.value = 1000
    stats.lastConnectedAt = new Date().toISOString()
    
    // 发送认证消息
    sendMessage({
      type: 'authenticate',
      data: {
        token: localStorage.getItem('access_token'),
        deviceInfo: {
          type: 'web',
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          screenResolution: `${screen.width}x${screen.height}`
        }
      }
    })
  }

  /**
   * 处理接收消息
   */
  const handleMessage = (event: MessageEvent) => {
    try {
      const message: ServerMessage = JSON.parse(event.data)
      stats.messagesReceived++
      
      console.log('收到WebSocket消息:', message)
      
      // 处理确认消息
      if (message.requestId && pendingMessages.has(message.requestId)) {
        const pendingMessage = pendingMessages.get(message.requestId)
        pendingMessages.delete(message.requestId)
        
        // 触发消息确认回调
        if (pendingMessage?.onAck) {
          pendingMessage.onAck(message)
        }
      }
      
      // 路由消息到对应处理器
      const handler = messageHandlers.get(message.type)
      if (handler) {
        handler(message)
      } else {
        console.warn('未找到消息处理器:', message.type)
      }
      
    } catch (error) {
      console.error('WebSocket消息解析失败:', error)
    }
  }

  /**
   * 处理连接关闭
   */
  const handleClose = (event: CloseEvent) => {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    isConnected.value = false
    isConnecting.value = false
    stats.lastDisconnectedAt = new Date().toISOString()
    
    // 清理待确认消息
    pendingMessages.clear()
    
    // 根据关闭码决定是否重连
    if (shouldReconnect(event.code)) {
      scheduleReconnect()
    }
  }

  /**
   * 处理连接错误
   */
  const handleError = (error: Event) => {
    console.error('WebSocket错误:', error)
    isConnecting.value = false
    
    ElMessage.error('WebSocket连接出现错误')
  }

  /**
   * 判断是否应该重连
   */
  const shouldReconnect = (closeCode: number): boolean => {
    // 正常关闭或服务器主动关闭不重连
    if (closeCode === 1000 || closeCode === 1001) {
      return false
    }
    
    // 认证失败不重连
    if (closeCode === 4001) {
      ElMessage.error('认证失败，请重新登录')
      userStore.logout()
      return false
    }
    
    // 其他情况尝试重连
    return reconnectAttempts.value < maxReconnectAttempts
  }

  /**
   * 安排重连
   */
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      ElMessage.error('WebSocket重连失败，请刷新页面')
      return
    }

    reconnectAttempts.value++
    stats.reconnectCount++
    
    console.log(`${reconnectDelay.value}ms后尝试第${reconnectAttempts.value}次重连`)
    
    setTimeout(() => {
      if (!isConnected.value && socket.value) {
        connect(socket.value.url.replace(/^ws/, 'ws'))
      }
    }, reconnectDelay.value)
    
    // 指数退避
    reconnectDelay.value = Math.min(reconnectDelay.value * 2, 30000)
  }

  /**
   * 发送消息
   */
  const sendMessage = (message: Partial<ClientMessage>, options?: {
    needAck?: boolean
    timeout?: number
    onAck?: (response: ServerMessage) => void
    onTimeout?: () => void
  }): boolean => {
    if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，消息发送失败')
      ElMessage.warning('连接已断开，消息发送失败')
      return false
    }

    const fullMessage: ClientMessage = {
      id: generateMessageId(),
      timestamp: new Date().toISOString(),
      version: 'v1',
      requestId: generateRequestId(),
      ...message
    }

    // 如果需要确认，添加到待确认队列
    if (options?.needAck) {
      fullMessage.ack = true
      fullMessage.onAck = options.onAck
      pendingMessages.set(fullMessage.requestId!, fullMessage)
      
      // 设置超时处理
      if (options.timeout) {
        setTimeout(() => {
          if (pendingMessages.has(fullMessage.requestId!)) {
            pendingMessages.delete(fullMessage.requestId!)
            options.onTimeout?.()
          }
        }, options.timeout)
      }
    }

    try {
      socket.value.send(JSON.stringify(fullMessage))
      stats.messagesSent++
      console.log('发送WebSocket消息:', fullMessage)
      return true
    } catch (error) {
      console.error('消息发送失败:', error)
      ElMessage.error('消息发送失败')
      return false
    }
  }

  /**
   * 订阅消息类型
   */
  const subscribe = (messageType: string, handler: Function) => {
    messageHandlers.set(messageType, handler)
  }

  /**
   * 取消订阅消息类型
   */
  const unsubscribe = (messageType: string) => {
    messageHandlers.delete(messageType)
  }

  /**
   * 断开连接
   */
  const disconnect = () => {
    if (socket.value) {
      // 发送断开连接消息
      sendMessage({
        type: 'disconnect',
        data: { reason: 'user_initiated' }
      })
      
      socket.value.close(1000, 'User initiated disconnect')
      socket.value = null
    }
    
    isConnected.value = false
    isConnecting.value = false
    reconnectAttempts.value = 0
    pendingMessages.clear()
    messageHandlers.clear()
  }

  /**
   * 生成消息ID
   */
  const generateMessageId = (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成请求ID
   */
  const generateRequestId = (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 组件卸载时自动断开连接
  onUnmounted(() => {
    disconnect()
  })

  return {
    // 状态
    isConnected,
    isConnecting,
    reconnectAttempts,
    stats,
    
    // 方法
    connect,
    disconnect,
    sendMessage,
    subscribe,
    unsubscribe,
    
    // 便捷方法
    sendChatMessage: (content: string, targetType: string, targetId: string) => {
      return sendMessage({
        type: 'chat.send',
        data: {
          content,
          messageType: 'text',
          target: { type: targetType, id: targetId }
        }
      })
    },
    
    sendPositionUpdate: (spaceId: string, position: any, rotation: any) => {
      return sendMessage({
        type: 'position.update',
        data: { spaceId, position, rotation }
      })
    },
    
    joinSpace: (spaceId: string, password?: string) => {
      return sendMessage({
        type: 'space.join',
        data: { spaceId, password }
      }, { needAck: true })
    },
    
    leaveSpace: (spaceId: string) => {
      return sendMessage({
        type: 'space.leave',
        data: { spaceId }
      })
    }
  }
}