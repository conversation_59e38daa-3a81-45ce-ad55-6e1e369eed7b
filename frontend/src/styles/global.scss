// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 响应式断点
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

// 3D场景容器样式
.scene-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  
  canvas {
    display: block;
    outline: none;
  }
}

// 加载动画
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 卡片样式
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background-color: #409eff;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #66b1ff;
    }
  }
  
  &.btn-secondary {
    background-color: #909399;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #a6a9ad;
    }
  }
  
  &.btn-success {
    background-color: #67c23a;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #85ce61;
    }
  }
  
  &.btn-warning {
    background-color: #e6a23c;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #ebb563;
    }
  }
  
  &.btn-danger {
    background-color: #f56c6c;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #f78989;
    }
  }
}

// 表单样式
.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #606266;
  }
  
  input,
  textarea,
  select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    
    &:focus {
      outline: none;
      border-color: #409eff;
    }
    
    &.error {
      border-color: #f56c6c;
    }
  }
  
  .error-message {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
  }
}

// 头像样式
.avatar {
  display: inline-block;
  border-radius: 50%;
  overflow: hidden;
  
  &.avatar-sm {
    width: 32px;
    height: 32px;
  }
  
  &.avatar-md {
    width: 48px;
    height: 48px;
  }
  
  &.avatar-lg {
    width: 64px;
    height: 64px;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 标签样式
.tag {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 12px;
  background-color: #f0f2f5;
  color: #606266;
  
  &.tag-primary {
    background-color: #ecf5ff;
    color: #409eff;
  }
  
  &.tag-success {
    background-color: #f0f9ff;
    color: #67c23a;
  }
  
  &.tag-warning {
    background-color: #fdf6ec;
    color: #e6a23c;
  }
  
  &.tag-danger {
    background-color: #fef0f0;
    color: #f56c6c;
  }
}