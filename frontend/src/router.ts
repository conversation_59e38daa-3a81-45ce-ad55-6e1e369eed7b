import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'

// 路由懒加载
const Home = () => import('@/views/HomeView.vue')
const Login = () => import('@/views/auth/LoginView.vue')
const Register = () => import('@/views/auth/RegisterView.vue')
const Profile = () => import('@/views/user/ProfileView.vue')
const Messages = () => import('@/views/messaging/MessagesView.vue')
const MessageDetail = () => import('@/views/messaging/MessageDetailView.vue')
const Social = () => import('@/views/social/SocialView.vue')
const Friends = () => import('@/views/social/FriendsView.vue')
const Groups = () => import('@/views/social/GroupsView.vue')
const Content = () => import('@/views/content/ContentView.vue')
const PostDetail = () => import('@/views/content/PostDetailView.vue')
const Economy = () => import('@/views/economy/EconomyView.vue')
const Wallet = () => import('@/views/economy/WalletView.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: false
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/profile/:id?',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/messages',
    name: 'Messages',
    component: Messages,
    meta: {
      title: '消息',
      requiresAuth: true
    }
  },
  {
    path: '/messages/:id',
    name: 'MessageDetail',
    component: MessageDetail,
    meta: {
      title: '聊天',
      requiresAuth: true
    }
  },
  {
    path: '/social',
    name: 'Social',
    component: Social,
    meta: {
      title: '社交中心',
      requiresAuth: true
    },
    children: [
      {
        path: 'friends',
        name: 'Friends',
        component: Friends,
        meta: {
          title: '好友',
          requiresAuth: true
        }
      },
      {
        path: 'groups',
        name: 'Groups',
        component: Groups,
        meta: {
          title: '群组',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/content',
    name: 'Content',
    component: Content,
    meta: {
      title: '内容中心',
      requiresAuth: true
    }
  },
  {
    path: '/posts/:id',
    name: 'PostDetail',
    component: PostDetail,
    meta: {
      title: '帖子详情',
      requiresAuth: false
    }
  },
  {
    path: '/economy',
    name: 'Economy',
    component: Economy,
    meta: {
      title: '经济中心',
      requiresAuth: true
    }
  },
  {
    path: '/wallet',
    name: 'Wallet',
    component: Wallet,
    meta: {
      title: '我的钱包',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/common/NotFoundView.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - SOIC`
  }
  
  // 检查认证状态
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 已登录用户访问登录/注册页面时重定向
  if (to.meta.hideForAuth && userStore.isAuthenticated) {
    next({ name: 'Home' })
    return
  }
  
  next()
})

export default router