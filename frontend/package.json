{"name": "soic-frontend", "version": "1.0.0", "type": "module", "description": "元宇宙社交空间前端应用 - Vue 3 + TypeScript + Three.js", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "element-plus": "^2.4.2", "three": "^0.158.0", "@tresjs/core": "^3.7.0", "@tresjs/cientos": "^3.5.0", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "lodash-es": "^4.17.21", "dayjs": "^1.11.10", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "@vueuse/core": "^10.5.0", "gsap": "^3.12.2", "cannon-es": "^0.20.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.1", "vite": "^4.5.0", "typescript": "^5.2.2", "vue-tsc": "^1.8.22", "@types/node": "^20.8.10", "@types/three": "^0.158.0", "@types/lodash-es": "^4.17.12", "@types/js-cookie": "^3.0.6", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite-plugin-pwa": "^0.17.0", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.0.3", "sass": "^1.69.5", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "vitest": "^0.34.6", "@playwright/test": "^1.40.0", "@vue/test-utils": "^2.4.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}