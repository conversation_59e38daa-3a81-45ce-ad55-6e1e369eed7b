# 前端应用 - Vue 3 + TypeScript

## 🎨 模块概述

前端应用采用Vue 3 + TypeScript技术栈，遵循**高内聚低耦合**设计原则，按业务域组织代码结构，提供现代化的3D虚拟社交体验。

## 🎯 前端职责

- **🖼️ 用户界面渲染**: 组件化UI展示和交互逻辑
- **📊 状态管理**: 客户端状态管理和数据缓存
- **🌐 3D场景渲染**: Three.js 3D场景渲染和用户交互
- **✅ 客户端验证**: 前端表单验证和输入格式化
- **🔄 实时通信**: WebSocket连接管理和消息处理

## 🏗️ 架构设计

### 高内聚组件设计

```
src/components/
├── User/           # 用户域组件（高内聚）
│   ├── UserProfile.vue      # 用户资料组件
│   ├── UserAvatar.vue       # 虚拟形象组件
│   ├── UserSettings.vue     # 用户设置组件
│   └── UserLogin.vue        # 用户登录组件
├── Space/          # 空间域组件（高内聚）
│   ├── SpaceViewer.vue      # 3D空间查看器
│   ├── SpaceEditor.vue      # 空间编辑器
│   ├── SpaceList.vue        # 空间列表
│   └── SceneRenderer.vue    # 场景渲染器
├── Social/         # 社交域组件（高内聚）
│   ├── FriendList.vue       # 好友列表
│   ├── GroupManager.vue     # 群组管理
│   └── SocialFeed.vue       # 社交动态
└── ...
```

### 状态管理模块化

```typescript
// stores/modules/user.ts - 用户状态管理（高内聚）
export const useUserStore = defineStore('user', () => {
  // 用户相关状态集中管理
  const currentUser = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const userAvatar = ref<Avatar | null>(null)
  
  // 用户相关操作集中定义
  const login = async (credentials: LoginCredentials) => { /* ... */ }
  const updateProfile = async (data: Partial<UserProfile>) => { /* ... */ }
  
  return { currentUser, userProfile, userAvatar, login, updateProfile }
})
```

### API接口抽象

```typescript
// api/user.ts - 用户API接口（功能内聚）
export const userAPI = {
  login: (credentials: LoginCredentials) => request.post('/auth/login/', credentials),
  register: (data: RegisterData) => request.post('/auth/register/', data),
  getCurrentUser: () => request.get('/users/me/'),
  updateProfile: (data: Partial<UserProfile>) => request.patch('/users/me/profile/', data),
  // ... 其他用户相关接口
}
```

## 🛠️ 技术栈

### 核心框架
- **Vue 3**: 渐进式JavaScript框架
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 快速的构建工具

### UI和样式
- **Element Plus**: Vue 3组件库
- **Tailwind CSS**: 实用优先的CSS框架
- **SCSS**: CSS预处理器

### 3D渲染
- **Three.js**: 3D图形库
- **TresJS**: Vue 3的Three.js集成

### 状态管理
- **Pinia**: Vue 3状态管理库
- **Vue Query**: 数据获取和缓存

### 工具库
- **Axios**: HTTP客户端
- **Socket.io**: WebSocket客户端
- **Lodash**: JavaScript工具库
- **Day.js**: 日期处理库

## 📁 目录结构详解

```
frontend/
├── public/                 # 公共资源
│   ├── index.html         # HTML模板
│   └── favicon.ico        # 网站图标
├── src/                   # 源代码目录
│   ├── components/        # 按业务域组织的组件
│   │   ├── User/         # 用户域组件
│   │   ├── Space/        # 空间域组件
│   │   ├── Social/       # 社交域组件
│   │   ├── Chat/         # 通信域组件
│   │   ├── Content/      # 内容域组件
│   │   ├── Economy/      # 经济域组件
│   │   └── Common/       # 通用组件
│   ├── stores/           # Pinia状态管理
│   │   ├── modules/      # 按业务域分组的状态模块
│   │   └── index.ts      # 状态管理入口
│   ├── api/              # API接口层
│   │   ├── base.ts       # 基础API配置
│   │   ├── user.ts       # 用户API
│   │   ├── space.ts      # 空间API
│   │   └── ...           # 其他API模块
│   ├── views/            # 页面视图
│   │   ├── Home/         # 首页
│   │   ├── User/         # 用户页面
│   │   └── ...           # 其他页面
│   ├── composables/      # 组合式函数
│   │   ├── useWebSocket.ts    # WebSocket通信
│   │   ├── useThreeJS.ts      # 3D渲染
│   │   └── useAuth.ts         # 认证逻辑
│   ├── utils/            # 工具函数
│   │   ├── request.ts    # HTTP请求
│   │   ├── storage.ts    # 本地存储
│   │   └── validation.ts # 数据验证
│   ├── types/            # TypeScript类型定义
│   │   ├── user.ts       # 用户类型
│   │   ├── space.ts      # 空间类型
│   │   └── common.ts     # 通用类型
│   ├── assets/           # 静态资源
│   ├── styles/           # 样式文件
│   ├── App.vue           # 根组件
│   ├── main.ts           # 应用入口
│   └── router.ts         # 路由配置
├── package.json          # 依赖配置
├── vite.config.ts        # Vite配置
├── tsconfig.json         # TypeScript配置
└── README.md             # 前端说明文档
```

## 🚀 开发指南

### 环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 组件开发规范

```vue
<template>
  <div class="component-name">
    <!-- 使用语义化的HTML结构 -->
    <header class="component-header">
      <h2>{{ title }}</h2>
    </header>
    
    <main class="component-content">
      <!-- 主要内容 -->
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/common'

// Props定义
interface Props {
  title: string
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// Emits定义
interface Emits {
  update: [value: any]
  delete: [id: string]
}

const emit = defineEmits<Emits>()

// 响应式状态
const loading = ref(false)
const error = ref<string | null>(null)

// 计算属性
const processedData = computed(() => {
  return props.data.map(item => ({
    ...item,
    processed: true
  }))
})

// 生命周期
onMounted(() => {
  // 组件挂载后的逻辑
})

// 方法定义
const handleUpdate = (value: any) => {
  emit('update', value)
}
</script>

<style scoped>
.component-name {
  @apply p-4 bg-white rounded-lg shadow;
}

.component-header {
  @apply mb-4 pb-2 border-b;
}

.component-content {
  @apply space-y-4;
}
</style>
```

## 🧪 测试

### 单元测试

```bash
# 运行单元测试
npm run test:unit

# 监听模式
npm run test:unit:watch

# 覆盖率报告
npm run test:coverage
```

### E2E测试

```bash
# 运行E2E测试
npm run test:e2e

# 交互模式
npm run test:e2e:dev
```

## 📦 构建和部署

### 开发环境

```bash
npm run dev
```

### 生产构建

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

### Docker部署

```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔧 配置说明

### Vite配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@api': resolve(__dirname, 'src/api'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types'),
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

## 🎯 性能优化

### 代码分割

```typescript
// 路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import('@/views/User/UserPage.vue')
  },
  {
    path: '/space',
    component: () => import('@/views/Space/SpacePage.vue')
  }
]
```

### 组件懒加载

```vue
<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// 异步组件
const HeavyComponent = defineAsyncComponent(() => 
  import('@/components/HeavyComponent.vue')
)
</script>
```

---

**模块负责人**: 前端开发团队  
**技术栈**: Vue 3 + TypeScript + Three.js  
**设计原则**: 高内聚低耦合组件化架构