# 元宇宙社交空间项目 - 详细设计文档

## 文档信息
- **项目名称**: 元宇宙社交空间平台 (MetaVerse Social Space Platform)
- **文档版本**: v1.0
- **创建日期**: 2025-07-30
- **文档类型**: 详细设计文档

## 目录
1. [核心功能模块详细设计](#1-核心功能模块详细设计)
2. [API接口规范](#2-api接口规范)
3. [关键算法设计](#3-关键算法设计)
4. [安全机制设计](#4-安全机制设计)
5. [数据流设计](#5-数据流设计)
6. [状态管理设计](#6-状态管理设计)
7. [错误处理设计](#7-错误处理设计)
8. [性能优化设计](#8-性能优化设计)

## 1. 前后端分离架构设计

### 1.1 架构职责边界
#### 1.1.1 前端职责（Vue 3）
- **用户界面渲染**：组件化UI展示和交互
- **状态管理**：客户端状态管理和数据缓存
- **用户体验**：路由管理、动画效果、响应式设计
- **3D渲染**：Three.js场景渲染和用户交互
- **实时通信**：WebSocket连接管理和消息处理
- **数据验证**：前端表单验证和输入格式化

#### 1.1.2 后端职责（Django）
- **业务逻辑**：核心业务规则和流程控制
- **数据持久化**：数据库操作和事务管理
- **权限控制**：用户认证、授权和安全策略
- **API服务**：RESTful接口和数据格式化
- **异步任务**：后台任务处理和消息队列
- **系统集成**：第三方服务集成和外部API调用

### 1.2 高内聚模块设计

#### 1.2.1 用户管理模块（高内聚设计）
```python
# Django后端 - 用户管理模块
# apps/users/services.py - 业务逻辑层
from typing import Dict, Any, Optional
from django.contrib.auth import get_user_model
from django.db import transaction
from .models import UserProfile, Avatar
from .serializers import UserRegistrationSerializer
from .tasks import send_verification_email, create_default_avatar
from .exceptions import UserAlreadyExistsError, InvalidVerificationCodeError

User = get_user_model()

class UserRegistrationService:
    """用户注册服务 - 高内聚业务逻辑"""

    def __init__(self):
        self.serializer_class = UserRegistrationSerializer

    @transaction.atomic
    def register_user(self, registration_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        用户注册主流程 - 原子性事务保证数据一致性
        职责：协调用户创建的完整流程
        """
        # 1. 数据验证（委托给序列化器）
        serializer = self.serializer_class(data=registration_data)
        serializer.is_valid(raise_exception=True)

        # 2. 业务规则验证
        self._validate_business_rules(serializer.validated_data)

        # 3. 创建用户实体
        user = self._create_user_entity(serializer.validated_data)

        # 4. 创建关联资源（异步处理，降低耦合）
        self._schedule_user_initialization(user.id)

        return {
            'user_id': user.id,
            'status': 'pending_verification',
            'message': '注册成功，请查收验证邮件'
        }

    def _validate_business_rules(self, data: Dict[str, Any]) -> None:
        """业务规则验证 - 内聚的验证逻辑"""
        if User.objects.filter(email=data['email']).exists():
            raise UserAlreadyExistsError('邮箱已被注册')

        if User.objects.filter(username=data['username']).exists():
            raise UserAlreadyExistsError('用户名已被使用')

    def _create_user_entity(self, data: Dict[str, Any]) -> User:
        """创建用户实体 - 内聚的数据创建逻辑"""
        return User.objects.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            is_active=False  # 需要邮箱验证激活
        )

    def _schedule_user_initialization(self, user_id: int) -> None:
        """调度用户初始化任务 - 异步解耦"""
        # 发送验证邮件（异步任务）
        send_verification_email.delay(user_id)

        # 创建默认虚拟形象（异步任务）
        create_default_avatar.delay(user_id)

# apps/users/views.py - 视图层（薄层，只负责HTTP协议处理）
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .services import UserRegistrationService
from .exceptions import UserAlreadyExistsError

@api_view(['POST'])
@permission_classes([AllowAny])
def register_user(request):
    """用户注册接口 - 薄视图层，职责单一"""
    try:
        service = UserRegistrationService()
        result = service.register_user(request.data)

        return Response({
            'success': True,
            'data': result,
            'timestamp': timezone.now().isoformat(),
            'request_id': request.META.get('HTTP_X_REQUEST_ID')
        }, status=status.HTTP_201_CREATED)

    except UserAlreadyExistsError as e:
        return Response({
            'success': False,
            'error': {
                'code': 'USER_ALREADY_EXISTS',
                'message': str(e)
            }
        }, status=status.HTTP_409_CONFLICT)
```

#### 1.2.2 Vue前端用户模块（高内聚设计）
```typescript
// 前端用户管理模块 - 高内聚组件设计
// stores/modules/user.ts - 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserRegistrationData, LoginCredentials } from '@/types/user'
import { userAPI } from '@/api/user'
import { useNotification } from '@/composables/useNotification'

export const useUserStore = defineStore('user', () => {
  // 状态（内聚的用户相关状态）
  const currentUser = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const userProfile = ref(null)
  const userPreferences = ref({})

  // 计算属性（内聚的派生状态）
  const userDisplayName = computed(() =>
    currentUser.value?.nickname || currentUser.value?.username || '未知用户'
  )

  const hasPermission = computed(() => (permission: string) =>
    currentUser.value?.permissions?.includes(permission) || false
  )

  // 动作（内聚的用户操作）
  const registerUser = async (registrationData: UserRegistrationData) => {
    try {
      const response = await userAPI.register(registrationData)

      if (response.success) {
        useNotification().success('注册成功，请查收验证邮件')
        return response.data
      }
    } catch (error) {
      throw new Error(`注册失败: ${error.message}`)
    }
  }

  const loginUser = async (credentials: LoginCredentials) => {
    try {
      const response = await userAPI.login(credentials)

      if (response.success) {
        currentUser.value = response.data.user
        isAuthenticated.value = true

        // 存储认证令牌
        localStorage.setItem('access_token', response.data.tokens.accessToken)
        localStorage.setItem('refresh_token', response.data.tokens.refreshToken)

        return response.data
      }
    } catch (error) {
      throw new Error(`登录失败: ${error.message}`)
    }
  }

  const logoutUser = async () => {
    try {
      await userAPI.logout()
    } finally {
      // 清理状态
      currentUser.value = null
      isAuthenticated.value = false
      userProfile.value = null
      userPreferences.value = {}

      // 清理本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  return {
    // 状态
    currentUser,
    isAuthenticated,
    userProfile,
    userPreferences,

    // 计算属性
    userDisplayName,
    hasPermission,

    // 动作
    registerUser,
    loginUser,
    logoutUser
  }
})

// components/User/UserRegistrationForm.vue - 高内聚UI组件
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
    @submit.prevent="handleSubmit"
  >
    <el-form-item label="用户名" prop="username">
      <el-input
        v-model="formData.username"
        placeholder="请输入用户名"
        :disabled="loading"
      />
    </el-form-item>

    <el-form-item label="邮箱" prop="email">
      <el-input
        v-model="formData.email"
        type="email"
        placeholder="请输入邮箱地址"
        :disabled="loading"
      />
    </el-form-item>

    <el-form-item label="密码" prop="password">
      <el-input
        v-model="formData.password"
        type="password"
        placeholder="请输入密码"
        :disabled="loading"
        show-password
      />
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        注册
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElForm, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import type { UserRegistrationData } from '@/types/user'

// 组件职责：用户注册表单的展示和交互
const userStore = useUserStore()
const formRef = ref<InstanceType<typeof ElForm>>()
const loading = ref(false)

// 表单数据（内聚的表单状态）
const formData = reactive<UserRegistrationData>({
  username: '',
  email: '',
  password: '',
  registrationType: 'email'
})

// 表单验证规则（内聚的验证逻辑）
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8个字符', trigger: 'blur' }
  ]
}

// 表单提交处理（内聚的交互逻辑）
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    await userStore.registerUser(formData)

    ElMessage.success('注册成功！')

    // 重置表单
    formRef.value.resetFields()

  } catch (error) {
    ElMessage.error(error.message || '注册失败')
  } finally {
    loading.value = false
  }
}
</script>
```

### 1.3 低耦合架构设计

#### 1.3.1 事件驱动解耦
```python
# Django后端 - 事件驱动架构
# apps/core/events.py - 事件系统（解耦模块间通信）
from typing import Dict, Any, List, Callable
from django.dispatch import Signal
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DomainEvent:
    """领域事件基类"""
    event_type: str
    aggregate_id: str
    data: Dict[str, Any]
    timestamp: str
    version: int = 1

# 定义领域事件
user_registered = Signal()
user_verified = Signal()
space_created = Signal()
space_joined = Signal()
message_sent = Signal()
content_uploaded = Signal()

class EventBus:
    """事件总线 - 解耦模块间通信"""

    def __init__(self):
        self._handlers: Dict[str, List[Callable]] = {}

    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)

    def publish(self, event: DomainEvent):
        """发布事件"""
        logger.info(f"Publishing event: {event.event_type}")

        handlers = self._handlers.get(event.event_type, [])
        for handler in handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"Event handler failed: {e}")

# 全局事件总线实例
event_bus = EventBus()

# apps/users/handlers.py - 用户模块事件处理器
from apps.core.events import event_bus, DomainEvent
from .tasks import send_welcome_email, create_user_wallet
from .models import User

def handle_user_registered(event: DomainEvent):
    """处理用户注册事件 - 解耦的后续处理"""
    user_id = event.aggregate_id

    # 异步发送欢迎邮件
    send_welcome_email.delay(user_id)

    # 异步创建用户钱包
    create_user_wallet.delay(user_id)

def handle_user_verified(event: DomainEvent):
    """处理用户验证事件"""
    user_id = event.aggregate_id

    # 激活用户账户
    User.objects.filter(id=user_id).update(is_active=True)

# 注册事件处理器
event_bus.subscribe('user.registered', handle_user_registered)
event_bus.subscribe('user.verified', handle_user_verified)
```

#### 1.3.2 依赖注入设计
```python
# apps/core/dependencies.py - 依赖注入容器
from abc import ABC, abstractmethod
from typing import Dict, Type, Any
from django.conf import settings

class ServiceContainer:
    """服务容器 - 管理依赖注入"""

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}

    def register(self, name: str, service_class: Type, singleton: bool = False):
        """注册服务"""
        self._services[name] = {
            'class': service_class,
            'singleton': singleton
        }

    def get(self, name: str) -> Any:
        """获取服务实例"""
        if name not in self._services:
            raise ValueError(f"Service {name} not registered")

        service_config = self._services[name]

        if service_config['singleton']:
            if name not in self._singletons:
                self._singletons[name] = service_config['class']()
            return self._singletons[name]

        return service_config['class']()

# 全局服务容器
container = ServiceContainer()

# 抽象接口定义
class EmailServiceInterface(ABC):
    @abstractmethod
    def send_email(self, to: str, subject: str, content: str) -> bool:
        pass

class StorageServiceInterface(ABC):
    @abstractmethod
    def upload_file(self, file_path: str, content: bytes) -> str:
        pass

# 具体实现
class SMTPEmailService(EmailServiceInterface):
    def send_email(self, to: str, subject: str, content: str) -> bool:
        # SMTP邮件发送实现
        pass

class AliyunOSSStorageService(StorageServiceInterface):
    def upload_file(self, file_path: str, content: bytes) -> str:
        # 阿里云OSS上传实现
        pass

# 注册服务
container.register('email_service', SMTPEmailService, singleton=True)
container.register('storage_service', AliyunOSSStorageService, singleton=True)

# apps/users/services.py - 使用依赖注入
from apps.core.dependencies import container
from apps.core.events import event_bus, DomainEvent

class UserService:
    """用户服务 - 通过依赖注入解耦"""

    def __init__(self):
        self.email_service = container.get('email_service')
        self.storage_service = container.get('storage_service')

    def register_user(self, registration_data: Dict[str, Any]) -> Dict[str, Any]:
        """用户注册 - 解耦的业务逻辑"""
        # 创建用户
        user = self._create_user(registration_data)

        # 发布事件（解耦后续处理）
        event = DomainEvent(
            event_type='user.registered',
            aggregate_id=str(user.id),
            data={'email': user.email, 'username': user.username},
            timestamp=timezone.now().isoformat()
        )
        event_bus.publish(event)

        return {'user_id': user.id, 'status': 'registered'}
```

#### 1.3.3 配置管理解耦
```python
# config/settings/base.py - 配置管理
import os
from pathlib import Path

class Config:
    """配置类 - 集中管理配置，减少硬编码"""

    # 数据库配置
    DATABASE_CONFIG = {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DB_NAME', 'metaverse_main'),
        'USER': os.getenv('DB_USER', 'root'),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '3306'),
    }

    # Redis配置
    REDIS_CONFIG = {
        'HOST': os.getenv('REDIS_HOST', 'localhost'),
        'PORT': int(os.getenv('REDIS_PORT', 6379)),
        'DB': int(os.getenv('REDIS_DB', 0)),
        'PASSWORD': os.getenv('REDIS_PASSWORD', None),
    }

    # 业务配置
    BUSINESS_CONFIG = {
        'MAX_SPACES_PER_USER': int(os.getenv('MAX_SPACES_PER_USER', 10)),
        'MAX_SPACE_CAPACITY': int(os.getenv('MAX_SPACE_CAPACITY', 500)),
        'MAX_FILE_SIZE': int(os.getenv('MAX_FILE_SIZE', 100 * 1024 * 1024)),
        'TOKEN_EXPIRE_MINUTES': int(os.getenv('TOKEN_EXPIRE_MINUTES', 15)),
    }

    # 第三方服务配置
    EXTERNAL_SERVICES = {
        'EMAIL_SERVICE_URL': os.getenv('EMAIL_SERVICE_URL', ''),
        'STORAGE_SERVICE_URL': os.getenv('STORAGE_SERVICE_URL', ''),
        'PAYMENT_SERVICE_URL': os.getenv('PAYMENT_SERVICE_URL', ''),
    }

# apps/core/config.py - 配置访问器
class ConfigManager:
    """配置管理器 - 提供类型安全的配置访问"""

    @staticmethod
    def get_database_url() -> str:
        config = Config.DATABASE_CONFIG
        return f"mysql://{config['USER']}:{config['PASSWORD']}@{config['HOST']}:{config['PORT']}/{config['NAME']}"

    @staticmethod
    def get_redis_url() -> str:
        config = Config.REDIS_CONFIG
        auth = f":{config['PASSWORD']}@" if config['PASSWORD'] else ""
        return f"redis://{auth}{config['HOST']}:{config['PORT']}/{config['DB']}"

    @staticmethod
    def get_business_config(key: str) -> Any:
        return Config.BUSINESS_CONFIG.get(key)

    @staticmethod
    def get_external_service_url(service: str) -> str:
        return Config.EXTERNAL_SERVICES.get(f'{service.upper()}_SERVICE_URL', '')
```

### 1.2 虚拟空间模块
#### 1.2.1 空间创建与管理
```typescript
interface SpaceCreationRequest {
  name: string;
  description: string;
  type: 'private' | 'public' | 'enterprise';
  template: string;
  maxCapacity: number;
  settings: SpaceSettings;
}

interface SpaceSettings {
  allowVoiceChat: boolean;
  allowTextChat: boolean;
  allowScreenShare: boolean;
  moderationLevel: 'none' | 'basic' | 'strict';
  accessControl: AccessControlSettings;
}

class SpaceManagementService {
  async createSpace(
    userId: string, 
    request: SpaceCreationRequest
  ): Promise<Space> {
    // 1. 验证用户权限
    await this.validateUserPermissions(userId, 'create_space');
    
    // 2. 检查空间数量限制
    await this.checkSpaceLimit(userId);
    
    // 3. 验证模板
    const template = await this.getSpaceTemplate(request.template);
    if (!template) {
      throw new ValidationError('无效的空间模板');
    }
    
    // 4. 创建空间记录
    const space = await this.spaceRepository.create({
      ...request,
      ownerId: userId,
      status: 'active',
      createdAt: new Date(),
      sceneData: template.defaultScene
    });
    
    // 5. 初始化空间资源
    await this.initializeSpaceResources(space.id);
    
    // 6. 设置默认权限
    await this.setupDefaultPermissions(space.id, userId);
    
    // 7. 创建空间索引
    await this.indexSpace(space);
    
    return space;
  }
  
  async updateSpaceScene(
    spaceId: string, 
    userId: string, 
    sceneData: SceneData
  ): Promise<void> {
    // 1. 验证权限
    await this.validateSpacePermission(spaceId, userId, 'edit');
    
    // 2. 验证场景数据
    await this.validateSceneData(sceneData);
    
    // 3. 备份当前场景
    await this.backupCurrentScene(spaceId);
    
    // 4. 更新场景数据
    await this.spaceRepository.updateScene(spaceId, sceneData);
    
    // 5. 通知在线用户
    await this.notifySpaceUsers(spaceId, {
      type: 'scene_updated',
      data: sceneData
    });
    
    // 6. 记录操作日志
    await this.logSpaceOperation(spaceId, userId, 'scene_update');
  }
}
```

### 1.3 实时通信模块
#### 1.3.1 WebSocket连接管理
```typescript
interface ConnectionManager {
  connections: Map<string, WebSocketConnection>;
  rooms: Map<string, Set<string>>;
  userSessions: Map<string, UserSession>;
}

class WebSocketConnectionManager {
  private connections = new Map<string, WebSocketConnection>();
  private rooms = new Map<string, Set<string>>();
  
  async handleConnection(socket: WebSocket, token: string): Promise<void> {
    try {
      // 1. 验证JWT令牌
      const payload = await this.verifyToken(token);
      const userId = payload.userId;
      
      // 2. 检查用户状态
      const user = await this.userService.getUser(userId);
      if (user.status !== 'active') {
        socket.close(4001, '用户状态异常');
        return;
      }
      
      // 3. 创建连接记录
      const connectionId = this.generateConnectionId();
      const connection = new WebSocketConnection(
        connectionId,
        socket,
        userId,
        new Date()
      );
      
      this.connections.set(connectionId, connection);
      
      // 4. 设置事件监听
      this.setupEventHandlers(connection);
      
      // 5. 发送连接确认
      await this.sendMessage(connectionId, {
        type: 'connection_established',
        data: { connectionId, userId }
      });
      
      // 6. 更新用户在线状态
      await this.updateUserOnlineStatus(userId, true);
      
    } catch (error) {
      socket.close(4000, '认证失败');
    }
  }
  
  private setupEventHandlers(connection: WebSocketConnection): void {
    const { socket, userId, connectionId } = connection;
    
    // 加入空间
    socket.on('join_space', async (data) => {
      await this.handleJoinSpace(connectionId, data.spaceId);
    });
    
    // 离开空间
    socket.on('leave_space', async (data) => {
      await this.handleLeaveSpace(connectionId, data.spaceId);
    });
    
    // 发送消息
    socket.on('send_message', async (data) => {
      await this.handleSendMessage(connectionId, data);
    });
    
    // 更新位置
    socket.on('update_position', async (data) => {
      await this.handlePositionUpdate(connectionId, data);
    });
    
    // 连接断开
    socket.on('disconnect', async () => {
      await this.handleDisconnection(connectionId);
    });
  }
}
```

### 1.4 内容管理模块
#### 1.4.1 UGC内容处理
```typescript
interface ContentUploadRequest {
  type: 'model' | 'texture' | 'audio' | 'video';
  file: File;
  metadata: ContentMetadata;
  spaceId?: string;
}

interface ContentMetadata {
  name: string;
  description: string;
  tags: string[];
  category: string;
  license: 'public' | 'private' | 'commercial';
}

class ContentManagementService {
  async uploadContent(
    userId: string,
    request: ContentUploadRequest
  ): Promise<ContentUploadResult> {
    // 1. 验证用户权限
    await this.validateUploadPermissions(userId, request.type);
    
    // 2. 文件格式验证
    await this.validateFileFormat(request.file, request.type);
    
    // 3. 文件大小检查
    await this.validateFileSize(request.file, request.type);
    
    // 4. 内容安全扫描
    const scanResult = await this.scanContentSafety(request.file);
    if (!scanResult.safe) {
      throw new ContentViolationError('内容违规', scanResult.violations);
    }
    
    // 5. 生成唯一文件名
    const fileName = this.generateUniqueFileName(request.file);
    
    // 6. 上传到对象存储
    const uploadResult = await this.storageService.upload(
      request.file,
      fileName,
      {
        contentType: request.file.type,
        metadata: request.metadata
      }
    );
    
    // 7. 创建内容记录
    const content = await this.contentRepository.create({
      id: this.generateContentId(),
      userId,
      type: request.type,
      name: request.metadata.name,
      description: request.metadata.description,
      tags: request.metadata.tags,
      category: request.metadata.category,
      license: request.metadata.license,
      fileUrl: uploadResult.url,
      fileSize: request.file.size,
      status: 'pending_review',
      createdAt: new Date()
    });
    
    // 8. 提交审核队列
    await this.submitForReview(content.id);
    
    // 9. 生成缩略图（如果适用）
    if (this.shouldGenerateThumbnail(request.type)) {
      await this.generateThumbnail(content.id, uploadResult.url);
    }
    
    return {
      contentId: content.id,
      status: 'uploaded',
      reviewStatus: 'pending'
    };
  }
}
```

## 2. 前后端分离API接口规范

### 2.1 API设计原则
#### 2.1.1 前后端职责分离原则
- **前端职责**：数据展示、用户交互、状态管理、客户端验证
- **后端职责**：业务逻辑、数据持久化、权限控制、服务端验证
- **接口职责**：数据传输、格式转换、错误传递、状态同步

#### 2.1.2 RESTful API设计规范
```
基础URL: https://api.metaverse-social.com/v1

资源命名规范:
- 使用名词复数形式表示资源集合
- 使用小写字母和连字符分隔
- 避免深层嵌套（最多3层）
- 使用语义化的动词表示操作

资源设计示例:
GET    /api/v1/users                     # 获取用户列表
POST   /api/v1/users                     # 创建用户
GET    /api/v1/users/{id}                # 获取特定用户
PUT    /api/v1/users/{id}                # 完整更新用户
PATCH  /api/v1/users/{id}                # 部分更新用户
DELETE /api/v1/users/{id}                # 删除用户

GET    /api/v1/spaces                    # 获取空间列表
POST   /api/v1/spaces                    # 创建空间
GET    /api/v1/spaces/{id}               # 获取空间详情
PUT    /api/v1/spaces/{id}               # 更新空间
DELETE /api/v1/spaces/{id}               # 删除空间

GET    /api/v1/spaces/{id}/members       # 获取空间成员
POST   /api/v1/spaces/{id}/members       # 添加空间成员
DELETE /api/v1/spaces/{id}/members/{uid} # 移除空间成员

POST   /api/v1/spaces/{id}/join          # 加入空间（业务操作）
POST   /api/v1/spaces/{id}/leave         # 离开空间（业务操作）
```

#### 2.1.3 HTTP状态码标准化
```
成功响应 (2xx):
200 OK              - 请求成功，返回数据
201 Created         - 资源创建成功
202 Accepted        - 请求已接受，异步处理中
204 No Content      - 请求成功，无返回内容（如删除操作）

客户端错误 (4xx):
400 Bad Request     - 请求参数格式错误
401 Unauthorized    - 未认证或认证失败
403 Forbidden       - 已认证但无权限访问
404 Not Found       - 请求的资源不存在
405 Method Not Allowed - HTTP方法不被允许
409 Conflict        - 资源状态冲突（如重复创建）
422 Unprocessable Entity - 请求格式正确但语义错误
429 Too Many Requests - 请求频率超限

服务器错误 (5xx):
500 Internal Server Error - 服务器内部错误
502 Bad Gateway          - 上游服务错误
503 Service Unavailable  - 服务暂时不可用
504 Gateway Timeout      - 上游服务超时
```

#### 2.1.4 标准化响应格式
```typescript
// 统一响应格式接口定义
interface BaseResponse {
  success: boolean;
  timestamp: string;
  requestId: string;
  version: string;
}

// 成功响应格式
interface SuccessResponse<T> extends BaseResponse {
  success: true;
  data: T;
  message?: string;
  meta?: {
    [key: string]: any;
  };
}

// 错误响应格式
interface ErrorResponse extends BaseResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: ValidationError[] | any;
    stack?: string; // 仅开发环境
  };
}

// 验证错误详情
interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

// 分页响应格式
interface PaginatedResponse<T> extends BaseResponse {
  success: true;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: {
    [key: string]: any;
  };
  sorting?: {
    field: string;
    order: 'asc' | 'desc';
  };
}

// 异步操作响应格式
interface AsyncResponse extends BaseResponse {
  success: true;
  data: {
    taskId: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    estimatedTime?: number; // 预估完成时间（秒）
    progress?: number; // 进度百分比
  };
  message: string;
}
```

#### 2.1.5 Django后端响应格式实现
```python
# apps/core/responses.py - 标准化响应格式
from typing import Any, Dict, List, Optional, Union
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
import uuid

class StandardResponse:
    """标准化API响应格式"""

    @staticmethod
    def success(
        data: Any = None,
        message: str = None,
        status_code: int = status.HTTP_200_OK,
        meta: Dict[str, Any] = None,
        request_id: str = None
    ) -> Response:
        """成功响应"""
        response_data = {
            'success': True,
            'data': data,
            'timestamp': timezone.now().isoformat(),
            'requestId': request_id or str(uuid.uuid4()),
            'version': 'v1'
        }

        if message:
            response_data['message'] = message

        if meta:
            response_data['meta'] = meta

        return Response(response_data, status=status_code)

    @staticmethod
    def error(
        code: str,
        message: str,
        details: Any = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        request_id: str = None
    ) -> Response:
        """错误响应"""
        response_data = {
            'success': False,
            'error': {
                'code': code,
                'message': message
            },
            'timestamp': timezone.now().isoformat(),
            'requestId': request_id or str(uuid.uuid4()),
            'version': 'v1'
        }

        if details:
            response_data['error']['details'] = details

        return Response(response_data, status=status_code)

    @staticmethod
    def paginated(
        data: List[Any],
        page: int,
        page_size: int,
        total: int,
        filters: Dict[str, Any] = None,
        sorting: Dict[str, str] = None,
        request_id: str = None
    ) -> Response:
        """分页响应"""
        total_pages = (total + page_size - 1) // page_size

        response_data = {
            'success': True,
            'data': data,
            'pagination': {
                'page': page,
                'pageSize': page_size,
                'total': total,
                'totalPages': total_pages,
                'hasNext': page < total_pages,
                'hasPrev': page > 1
            },
            'timestamp': timezone.now().isoformat(),
            'requestId': request_id or str(uuid.uuid4()),
            'version': 'v1'
        }

        if filters:
            response_data['filters'] = filters

        if sorting:
            response_data['sorting'] = sorting

        return Response(response_data, status=status.HTTP_200_OK)

# apps/core/middleware.py - 请求ID中间件
class RequestIdMiddleware:
    """为每个请求生成唯一ID"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        request.request_id = request.META.get('HTTP_X_REQUEST_ID', str(uuid.uuid4()))
        response = self.get_response(request)
        response['X-Request-ID'] = request.request_id
        return response
```

### 2.2 核心API接口定义
#### 2.2.1 用户相关API
```typescript
// 用户注册
POST /auth/register
Content-Type: application/json

Request Body:
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "username": "uniqueUsername",
  "registrationType": "email"
}

Response:
{
  "success": true,
  "data": {
    "userId": "usr_1234567890",
    "status": "pending_verification"
  },
  "message": "注册成功，请查收验证邮件",
  "timestamp": "2025-07-30T10:00:00Z",
  "requestId": "req_abcdef123456"
}

// 用户登录
POST /auth/login
Content-Type: application/json

Request Body:
{
  "identifier": "<EMAIL>",
  "password": "securePassword123",
  "deviceInfo": {
    "deviceType": "web",
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********"
  }
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": "usr_1234567890",
      "username": "uniqueUsername",
      "email": "<EMAIL>",
      "avatar": "https://cdn.example.com/avatars/user.jpg",
      "status": "active"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  },
  "timestamp": "2025-07-30T10:00:00Z",
  "requestId": "req_abcdef123456"
}
```

#### 2.2.2 空间相关API
```typescript
// 创建空间
POST /spaces
Authorization: Bearer {accessToken}
Content-Type: application/json

Request Body:
{
  "name": "我的虚拟办公室",
  "description": "团队协作空间",
  "type": "private",
  "template": "office_template_01",
  "maxCapacity": 50,
  "settings": {
    "allowVoiceChat": true,
    "allowTextChat": true,
    "allowScreenShare": true,
    "moderationLevel": "basic"
  }
}

Response:
{
  "success": true,
  "data": {
    "id": "spc_1234567890",
    "name": "我的虚拟办公室",
    "description": "团队协作空间",
    "type": "private",
    "ownerId": "usr_1234567890",
    "status": "active",
    "currentUsers": 0,
    "maxCapacity": 50,
    "createdAt": "2025-07-30T10:00:00Z",
    "inviteCode": "ABC123"
  },
  "timestamp": "2025-07-30T10:00:00Z",
  "requestId": "req_abcdef123456"
}

// 获取空间列表
GET /spaces?page=1&limit=20&type=public&category=social
Authorization: Bearer {accessToken}

Response:
{
  "success": true,
  "data": [
    {
      "id": "spc_1234567890",
      "name": "公共聊天室",
      "description": "大家一起聊天的地方",
      "type": "public",
      "category": "social",
      "currentUsers": 25,
      "maxCapacity": 100,
      "thumbnail": "https://cdn.example.com/spaces/thumb.jpg",
      "rating": 4.5,
      "tags": ["聊天", "社交", "娱乐"]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  },
  "timestamp": "2025-07-30T10:00:00Z",
  "requestId": "req_abcdef123456"
}
```

## 3. 关键算法设计

### 3.1 空间推荐算法
#### 3.1.1 协同过滤推荐
```typescript
interface UserBehavior {
  userId: string;
  spaceId: string;
  action: 'visit' | 'stay' | 'interact' | 'favorite';
  duration: number;
  timestamp: Date;
  rating?: number;
}

class SpaceRecommendationEngine {
  // 基于用户行为的协同过滤
  async getRecommendations(userId: string, limit: number = 10): Promise<Space[]> {
    // 1. 获取用户历史行为
    const userBehaviors = await this.getUserBehaviors(userId);

    // 2. 计算用户相似度
    const similarUsers = await this.findSimilarUsers(userId, userBehaviors);

    // 3. 获取相似用户喜欢的空间
    const candidateSpaces = await this.getCandidateSpaces(similarUsers);

    // 4. 计算空间推荐分数
    const scoredSpaces = await this.calculateRecommendationScores(
      userId,
      candidateSpaces
    );

    // 5. 排序并返回推荐结果
    return scoredSpaces
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.space);
  }

  private async calculateRecommendationScores(
    userId: string,
    candidateSpaces: Space[]
  ): Promise<ScoredSpace[]> {
    const scores = await Promise.all(
      candidateSpaces.map(async (space) => {
        let score = 0;

        // 基础分数：空间热度
        score += space.visitCount * 0.1;

        // 用户偏好分数
        const userPreferences = await this.getUserPreferences(userId);
        score += this.calculatePreferenceMatch(space, userPreferences) * 0.3;

        // 社交关系分数
        const friendsInSpace = await this.getFriendsInSpace(userId, space.id);
        score += friendsInSpace.length * 0.2;

        // 时间衰减因子
        const daysSinceCreated = this.getDaysSince(space.createdAt);
        score *= Math.exp(-daysSinceCreated / 30); // 30天衰减

        return { space, score };
      })
    );

    return scores;
  }
}
```

#### 3.1.2 内容相似度算法
```typescript
interface SpaceFeatures {
  category: string;
  tags: string[];
  description: string;
  averageRating: number;
  userDemographics: UserDemographics;
}

class ContentSimilarityCalculator {
  calculateSimilarity(space1: SpaceFeatures, space2: SpaceFeatures): number {
    let similarity = 0;

    // 类别相似度 (权重: 0.3)
    if (space1.category === space2.category) {
      similarity += 0.3;
    }

    // 标签相似度 (权重: 0.25)
    const tagSimilarity = this.calculateTagSimilarity(space1.tags, space2.tags);
    similarity += tagSimilarity * 0.25;

    // 描述文本相似度 (权重: 0.2)
    const textSimilarity = this.calculateTextSimilarity(
      space1.description,
      space2.description
    );
    similarity += textSimilarity * 0.2;

    // 评分相似度 (权重: 0.15)
    const ratingSimilarity = 1 - Math.abs(
      space1.averageRating - space2.averageRating
    ) / 5;
    similarity += ratingSimilarity * 0.15;

    // 用户群体相似度 (权重: 0.1)
    const demographicSimilarity = this.calculateDemographicSimilarity(
      space1.userDemographics,
      space2.userDemographics
    );
    similarity += demographicSimilarity * 0.1;

    return similarity;
  }

  private calculateTagSimilarity(tags1: string[], tags2: string[]): number {
    const set1 = new Set(tags1);
    const set2 = new Set(tags2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size; // Jaccard相似度
  }
}
```

### 3.2 实时同步算法
#### 3.2.1 状态同步机制
```typescript
interface UserState {
  userId: string;
  position: Vector3;
  rotation: Vector3;
  animation: string;
  timestamp: number;
  sequenceNumber: number;
}

class StateSync {
  private stateBuffer = new Map<string, UserState[]>();
  private lastSyncTime = new Map<string, number>();

  // 客户端状态预测
  predictState(userId: string, deltaTime: number): UserState | null {
    const states = this.stateBuffer.get(userId);
    if (!states || states.length < 2) return null;

    // 获取最近两个状态
    const current = states[states.length - 1];
    const previous = states[states.length - 2];

    // 计算速度
    const timeDiff = current.timestamp - previous.timestamp;
    const velocity = {
      x: (current.position.x - previous.position.x) / timeDiff,
      y: (current.position.y - previous.position.y) / timeDiff,
      z: (current.position.z - previous.position.z) / timeDiff
    };

    // 预测新位置
    const predictedPosition = {
      x: current.position.x + velocity.x * deltaTime,
      y: current.position.y + velocity.y * deltaTime,
      z: current.position.z + velocity.z * deltaTime
    };

    return {
      ...current,
      position: predictedPosition,
      timestamp: current.timestamp + deltaTime
    };
  }

  // 服务端状态协调
  reconcileState(
    userId: string,
    clientState: UserState,
    serverState: UserState
  ): UserState {
    const positionDiff = this.calculateDistance(
      clientState.position,
      serverState.position
    );

    // 如果位置差异过大，使用服务端状态
    if (positionDiff > 5.0) {
      return serverState;
    }

    // 否则使用插值平滑过渡
    const alpha = 0.1; // 插值系数
    return {
      userId,
      position: this.lerp(clientState.position, serverState.position, alpha),
      rotation: this.slerp(clientState.rotation, serverState.rotation, alpha),
      animation: serverState.animation, // 动画以服务端为准
      timestamp: Date.now(),
      sequenceNumber: Math.max(clientState.sequenceNumber, serverState.sequenceNumber) + 1
    };
  }
}
```

### 3.3 负载均衡算法
#### 3.3.1 一致性哈希算法
```typescript
interface ServerNode {
  id: string;
  address: string;
  weight: number;
  currentLoad: number;
  maxCapacity: number;
}

class ConsistentHashLoadBalancer {
  private ring = new Map<number, ServerNode>();
  private virtualNodes = 150; // 每个物理节点的虚拟节点数

  addServer(server: ServerNode): void {
    for (let i = 0; i < this.virtualNodes; i++) {
      const hash = this.hash(`${server.id}:${i}`);
      this.ring.set(hash, server);
    }

    // 按哈希值排序
    this.ring = new Map([...this.ring.entries()].sort((a, b) => a[0] - b[0]));
  }

  removeServer(serverId: string): void {
    const keysToRemove: number[] = [];

    for (const [hash, server] of this.ring.entries()) {
      if (server.id === serverId) {
        keysToRemove.push(hash);
      }
    }

    keysToRemove.forEach(key => this.ring.delete(key));
  }

  getServer(key: string): ServerNode | null {
    if (this.ring.size === 0) return null;

    const hash = this.hash(key);
    const ringKeys = Array.from(this.ring.keys());

    // 找到第一个大于等于hash的节点
    let targetKey = ringKeys.find(k => k >= hash);

    // 如果没找到，使用第一个节点（环形结构）
    if (targetKey === undefined) {
      targetKey = ringKeys[0];
    }

    const server = this.ring.get(targetKey)!;

    // 检查服务器负载
    if (server.currentLoad >= server.maxCapacity) {
      return this.findAlternativeServer(server);
    }

    return server;
  }

  private findAlternativeServer(excludeServer: ServerNode): ServerNode | null {
    const availableServers = Array.from(this.ring.values())
      .filter(server =>
        server.id !== excludeServer.id &&
        server.currentLoad < server.maxCapacity
      );

    if (availableServers.length === 0) return null;

    // 选择负载最低的服务器
    return availableServers.reduce((min, server) =>
      server.currentLoad < min.currentLoad ? server : min
    );
  }

  private hash(key: string): number {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
}
```

## 4. 安全机制设计

### 4.1 认证与授权机制
#### 4.1.1 JWT令牌设计
```typescript
interface JWTPayload {
  userId: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  deviceId: string;
  sessionId: string;
  iat: number; // 签发时间
  exp: number; // 过期时间
  iss: string; // 签发者
  aud: string; // 受众
}

class JWTService {
  private readonly accessTokenExpiry = 15 * 60; // 15分钟
  private readonly refreshTokenExpiry = 7 * 24 * 60 * 60; // 7天

  generateTokenPair(user: User, deviceInfo: DeviceInfo): TokenPair {
    const sessionId = this.generateSessionId();

    // 访问令牌
    const accessTokenPayload: JWTPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      deviceId: deviceInfo.deviceId,
      sessionId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.accessTokenExpiry,
      iss: 'metaverse-social',
      aud: 'metaverse-client'
    };

    // 刷新令牌
    const refreshTokenPayload = {
      userId: user.id,
      sessionId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.refreshTokenExpiry,
      iss: 'metaverse-social',
      aud: 'metaverse-client'
    };

    const accessToken = jwt.sign(accessTokenPayload, this.getPrivateKey(), {
      algorithm: 'RS256'
    });

    const refreshToken = jwt.sign(refreshTokenPayload, this.getPrivateKey(), {
      algorithm: 'RS256'
    });

    // 存储会话信息
    this.storeSession(sessionId, {
      userId: user.id,
      deviceId: deviceInfo.deviceId,
      createdAt: new Date(),
      lastActivity: new Date()
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenExpiry
    };
  }

  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      const payload = jwt.verify(token, this.getPublicKey(), {
        algorithms: ['RS256'],
        issuer: 'metaverse-social',
        audience: 'metaverse-client'
      }) as JWTPayload;

      // 检查会话是否有效
      const session = await this.getSession(payload.sessionId);
      if (!session || session.userId !== payload.userId) {
        throw new Error('会话无效');
      }

      // 更新最后活动时间
      await this.updateSessionActivity(payload.sessionId);

      return payload;
    } catch (error) {
      throw new AuthenticationError('令牌验证失败');
    }
  }
}
```

#### 4.1.2 权限控制系统
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  inherits?: string[]; // 继承的角色
}

class PermissionService {
  async checkPermission(
    userId: string,
    resource: string,
    action: string,
    context?: any
  ): Promise<boolean> {
    // 1. 获取用户角色
    const userRoles = await this.getUserRoles(userId);

    // 2. 获取所有权限（包括继承的）
    const allPermissions = await this.expandRolePermissions(userRoles);

    // 3. 查找匹配的权限
    const matchingPermissions = allPermissions.filter(permission =>
      permission.resource === resource && permission.action === action
    );

    if (matchingPermissions.length === 0) {
      return false;
    }

    // 4. 检查权限条件
    for (const permission of matchingPermissions) {
      if (await this.evaluateConditions(permission.conditions, context)) {
        return true;
      }
    }

    return false;
  }

  private async evaluateConditions(
    conditions: PermissionCondition[] | undefined,
    context: any
  ): Promise<boolean> {
    if (!conditions || conditions.length === 0) {
      return true;
    }

    for (const condition of conditions) {
      switch (condition.type) {
        case 'owner':
          if (context.ownerId !== context.userId) {
            return false;
          }
          break;

        case 'time_range':
          const now = new Date();
          if (now < condition.startTime || now > condition.endTime) {
            return false;
          }
          break;

        case 'ip_whitelist':
          if (!condition.allowedIPs.includes(context.clientIP)) {
            return false;
          }
          break;

        case 'custom':
          const result = await this.evaluateCustomCondition(condition, context);
          if (!result) {
            return false;
          }
          break;
      }
    }

    return true;
  }
}
```

### 4.2 数据加密与保护
#### 4.2.1 敏感数据加密
```typescript
class DataEncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyDerivationIterations = 100000;

  // 加密敏感数据
  async encryptSensitiveData(data: string, userKey?: string): Promise<EncryptedData> {
    // 生成随机盐和IV
    const salt = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);

    // 派生加密密钥
    const key = userKey
      ? await this.deriveUserKey(userKey, salt)
      : await this.getMasterKey();

    // 创建加密器
    const cipher = crypto.createCipher(this.algorithm, key);
    cipher.setAAD(salt); // 附加认证数据

    // 加密数据
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // 获取认证标签
    const authTag = cipher.getAuthTag();

    return {
      encryptedData: encrypted,
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      algorithm: this.algorithm
    };
  }

  // 解密敏感数据
  async decryptSensitiveData(
    encryptedData: EncryptedData,
    userKey?: string
  ): Promise<string> {
    // 重建加密密钥
    const salt = Buffer.from(encryptedData.salt, 'hex');
    const key = userKey
      ? await this.deriveUserKey(userKey, salt)
      : await this.getMasterKey();

    // 创建解密器
    const decipher = crypto.createDecipher(encryptedData.algorithm, key);
    decipher.setAAD(salt);
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));

    // 解密数据
    let decrypted = decipher.update(encryptedData.encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  // 密钥派生
  private async deriveUserKey(userKey: string, salt: Buffer): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(
        userKey,
        salt,
        this.keyDerivationIterations,
        32,
        'sha256',
        (err, derivedKey) => {
          if (err) reject(err);
          else resolve(derivedKey);
        }
      );
    });
  }
}
```

## 5. 数据流设计

### 5.1 用户注册登录数据流
```python
# Django视图处理用户注册
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .serializers import UserRegistrationSerializer
from .models import UserProfile, Avatar

@api_view(['POST'])
def register_user(request):
    """用户注册接口"""
    serializer = UserRegistrationSerializer(data=request.data)

    if serializer.is_valid():
        try:
            # 1. 创建用户
            user = User.objects.create_user(
                username=serializer.validated_data['username'],
                email=serializer.validated_data['email'],
                password=serializer.validated_data['password']
            )

            # 2. 创建用户资料
            UserProfile.objects.create(
                user=user,
                nickname=serializer.validated_data.get('nickname', user.username)
            )

            # 3. 创建默认虚拟形象
            Avatar.objects.create(
                user=user,
                name='默认形象',
                model_url='https://cdn.example.com/default_avatar.glb',
                is_default=True
            )

            # 4. 发送验证邮件
            send_verification_email.delay(user.id)

            return Response({
                'success': True,
                'message': '注册成功，请查收验证邮件',
                'user_id': user.id
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)
```

### 5.2 实时消息数据流
```python
# Django Channels消费者
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Message, Space, User

class SpaceChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.space_id = self.scope['url_route']['kwargs']['space_id']
        self.space_group_name = f'space_{self.space_id}'

        # 验证用户权限
        user = self.scope['user']
        if not await self.check_space_permission(user, self.space_id):
            await self.close()
            return

        # 加入空间群组
        await self.channel_layer.group_add(
            self.space_group_name,
            self.channel_name
        )

        await self.accept()

        # 通知其他用户有新用户加入
        await self.channel_layer.group_send(
            self.space_group_name,
            {
                'type': 'user_joined',
                'user_id': user.id,
                'username': user.username
            }
        )

    async def disconnect(self, close_code):
        # 离开空间群组
        await self.channel_layer.group_discard(
            self.space_group_name,
            self.channel_name
        )

        # 通知其他用户有用户离开
        user = self.scope['user']
        await self.channel_layer.group_send(
            self.space_group_name,
            {
                'type': 'user_left',
                'user_id': user.id,
                'username': user.username
            }
        )

    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')

        if message_type == 'chat_message':
            await self.handle_chat_message(data)
        elif message_type == 'position_update':
            await self.handle_position_update(data)
        elif message_type == 'gesture':
            await self.handle_gesture(data)

    async def handle_chat_message(self, data):
        user = self.scope['user']
        content = data['content']

        # 保存消息到数据库
        message = await self.save_message(
            sender=user,
            space_id=self.space_id,
            content=content,
            message_type='text'
        )

        # 广播消息给空间内所有用户
        await self.channel_layer.group_send(
            self.space_group_name,
            {
                'type': 'chat_message',
                'message_id': message.id,
                'sender_id': user.id,
                'sender_name': user.username,
                'content': content,
                'timestamp': message.created_at.isoformat()
            }
        )

    @database_sync_to_async
    def save_message(self, sender, space_id, content, message_type):
        return Message.objects.create(
            sender=sender,
            space_id=space_id,
            content=content,
            message_type=message_type
        )

    @database_sync_to_async
    def check_space_permission(self, user, space_id):
        try:
            space = Space.objects.get(id=space_id)
            # 检查用户是否有权限访问该空间
            return space.type == 'public' or space.members.filter(user=user).exists()
        except Space.DoesNotExist:
            return False
```

### 5.3 3D场景数据流
```python
# Vue 3 + Three.js场景管理
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { io } from 'socket.io-client'

export default {
  name: 'VirtualSpace',
  setup() {
    const scene = ref(null)
    const camera = ref(null)
    const renderer = ref(null)
    const socket = ref(null)

    const spaceData = reactive({
      id: null,
      name: '',
      users: new Map(),
      objects: new Map()
    })

    const initializeScene = () => {
      // 创建Three.js场景
      scene.value = new THREE.Scene()

      // 创建相机
      camera.value = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
      )

      // 创建渲染器
      renderer.value = new THREE.WebGLRenderer({ antialias: true })
      renderer.value.setSize(window.innerWidth, window.innerHeight)
      renderer.value.shadowMap.enabled = true
      renderer.value.shadowMap.type = THREE.PCFSoftShadowMap

      // 添加到DOM
      document.getElementById('scene-container').appendChild(renderer.value.domElement)

      // 添加光照
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      scene.value.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(50, 50, 50)
      directionalLight.castShadow = true
      scene.value.add(directionalLight)
    }

    const connectWebSocket = (spaceId) => {
      socket.value = io(`/spaces/${spaceId}`, {
        auth: {
          token: localStorage.getItem('access_token')
        }
      })

      socket.value.on('connect', () => {
        console.log('Connected to space:', spaceId)
      })

      socket.value.on('user_joined', (data) => {
        addUserAvatar(data.user_id, data.username)
      })

      socket.value.on('user_left', (data) => {
        removeUserAvatar(data.user_id)
      })

      socket.value.on('position_update', (data) => {
        updateUserPosition(data.user_id, data.position, data.rotation)
      })

      socket.value.on('scene_updated', (data) => {
        loadSceneData(data.scene_data)
      })
    }

    const addUserAvatar = async (userId, username) => {
      try {
        // 加载用户虚拟形象
        const avatarData = await fetch(`/api/users/${userId}/avatar`).then(r => r.json())

        const loader = new THREE.GLTFLoader()
        loader.load(avatarData.model_url, (gltf) => {
          const avatar = gltf.scene
          avatar.userData = { userId, username }

          // 添加用户名标签
          const nameTag = createNameTag(username)
          avatar.add(nameTag)

          scene.value.add(avatar)
          spaceData.users.set(userId, avatar)
        })
      } catch (error) {
        console.error('Failed to load user avatar:', error)
      }
    }

    const updateUserPosition = (userId, position, rotation) => {
      const avatar = spaceData.users.get(userId)
      if (avatar) {
        // 平滑移动到新位置
        const targetPosition = new THREE.Vector3(position.x, position.y, position.z)
        const targetRotation = new THREE.Euler(rotation.x, rotation.y, rotation.z)

        // 使用Tween.js进行平滑动画
        new TWEEN.Tween(avatar.position)
          .to(targetPosition, 100)
          .easing(TWEEN.Easing.Quadratic.Out)
          .start()

        new TWEEN.Tween(avatar.rotation)
          .to(targetRotation, 100)
          .easing(TWEEN.Easing.Quadratic.Out)
          .start()
      }
    }

    const sendPositionUpdate = () => {
      if (socket.value && camera.value) {
        socket.value.emit('position_update', {
          position: {
            x: camera.value.position.x,
            y: camera.value.position.y,
            z: camera.value.position.z
          },
          rotation: {
            x: camera.value.rotation.x,
            y: camera.value.rotation.y,
            z: camera.value.rotation.z
          }
        })
      }
    }

    const animate = () => {
      requestAnimationFrame(animate)

      // 更新Tween动画
      TWEEN.update()

      // 渲染场景
      renderer.value.render(scene.value, camera.value)

      // 定期发送位置更新
      if (Date.now() % 100 === 0) { // 每100ms发送一次
        sendPositionUpdate()
      }
    }

    onMounted(() => {
      initializeScene()
      connectWebSocket(spaceData.id)
      animate()
    })

    onUnmounted(() => {
      if (socket.value) {
        socket.value.disconnect()
      }
      if (renderer.value) {
        renderer.value.dispose()
      }
    })

    return {
      spaceData,
      initializeScene,
      connectWebSocket
    }
  }
}
```

## 6. 状态管理设计

### 6.1 Vue 3 + Pinia状态管理
```javascript
// stores/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userAPI } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref(null)
  const isAuthenticated = ref(false)
  const avatar = ref(null)
  const friends = ref([])
  const onlineStatus = ref('offline')

  // 计算属性
  const userDisplayName = computed(() => {
    return currentUser.value?.nickname || currentUser.value?.username || '未知用户'
  })

  const isVIP = computed(() => {
    return currentUser.value?.is_verified || false
  })

  // 动作
  const login = async (credentials) => {
    try {
      const response = await userAPI.login(credentials)

      if (response.success) {
        currentUser.value = response.data.user
        isAuthenticated.value = true

        // 存储令牌
        localStorage.setItem('access_token', response.data.tokens.accessToken)
        localStorage.setItem('refresh_token', response.data.tokens.refreshToken)

        // 加载用户头像
        await loadUserAvatar()

        // 加载好友列表
        await loadFriends()

        // 设置在线状态
        setOnlineStatus('online')

        return response
      }
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await userAPI.logout()
    } finally {
      // 清理状态
      currentUser.value = null
      isAuthenticated.value = false
      avatar.value = null
      friends.value = []
      onlineStatus.value = 'offline'

      // 清理本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  const loadUserAvatar = async () => {
    if (currentUser.value) {
      try {
        const response = await userAPI.getAvatar(currentUser.value.id)
        avatar.value = response.data
      } catch (error) {
        console.error('Failed to load avatar:', error)
      }
    }
  }

  const loadFriends = async () => {
    if (currentUser.value) {
      try {
        const response = await userAPI.getFriends()
        friends.value = response.data
      } catch (error) {
        console.error('Failed to load friends:', error)
      }
    }
  }

  const setOnlineStatus = (status) => {
    onlineStatus.value = status
    // 通过WebSocket通知服务器状态变化
    if (window.socket) {
      window.socket.emit('status_update', { status })
    }
  }

  const updateProfile = async (profileData) => {
    try {
      const response = await userAPI.updateProfile(profileData)
      if (response.success) {
        currentUser.value = { ...currentUser.value, ...response.data }
      }
      return response
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw error
    }
  }

  return {
    // 状态
    currentUser,
    isAuthenticated,
    avatar,
    friends,
    onlineStatus,

    // 计算属性
    userDisplayName,
    isVIP,

    // 动作
    login,
    logout,
    loadUserAvatar,
    loadFriends,
    setOnlineStatus,
    updateProfile
  }
})
```

### 6.2 空间状态管理
```javascript
// stores/space.js
import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { spaceAPI } from '@/api/space'

export const useSpaceStore = defineStore('space', () => {
  // 状态
  const currentSpace = ref(null)
  const spaceMembers = ref([])
  const spaceMessages = ref([])
  const userPositions = reactive(new Map())
  const sceneObjects = reactive(new Map())

  // 动作
  const joinSpace = async (spaceId) => {
    try {
      const response = await spaceAPI.getSpace(spaceId)
      currentSpace.value = response.data

      // 加载空间成员
      await loadSpaceMembers(spaceId)

      // 加载最近消息
      await loadRecentMessages(spaceId)

      return response
    } catch (error) {
      console.error('Failed to join space:', error)
      throw error
    }
  }

  const leaveSpace = () => {
    currentSpace.value = null
    spaceMembers.value = []
    spaceMessages.value = []
    userPositions.clear()
    sceneObjects.clear()
  }

  const loadSpaceMembers = async (spaceId) => {
    try {
      const response = await spaceAPI.getSpaceMembers(spaceId)
      spaceMembers.value = response.data
    } catch (error) {
      console.error('Failed to load space members:', error)
    }
  }

  const loadRecentMessages = async (spaceId, limit = 50) => {
    try {
      const response = await spaceAPI.getSpaceMessages(spaceId, { limit })
      spaceMessages.value = response.data.reverse() // 按时间正序
    } catch (error) {
      console.error('Failed to load messages:', error)
    }
  }

  const addMessage = (message) => {
    spaceMessages.value.push(message)

    // 保持消息数量限制
    if (spaceMessages.value.length > 100) {
      spaceMessages.value.shift()
    }
  }

  const updateUserPosition = (userId, position, rotation) => {
    userPositions.set(userId, {
      position,
      rotation,
      timestamp: Date.now()
    })
  }

  const addSceneObject = (objectId, objectData) => {
    sceneObjects.set(objectId, objectData)
  }

  const removeSceneObject = (objectId) => {
    sceneObjects.delete(objectId)
  }

  const updateSceneObject = (objectId, updates) => {
    const existing = sceneObjects.get(objectId)
    if (existing) {
      sceneObjects.set(objectId, { ...existing, ...updates })
    }
  }

  return {
    // 状态
    currentSpace,
    spaceMembers,
    spaceMessages,
    userPositions,
    sceneObjects,

    // 动作
    joinSpace,
    leaveSpace,
    loadSpaceMembers,
    loadRecentMessages,
    addMessage,
    updateUserPosition,
    addSceneObject,
    removeSceneObject,
    updateSceneObject
  }
})
```

## 7. 错误处理设计

### 7.1 Django全局异常处理
```python
# utils/exceptions.py
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)

    if response is not None:
        custom_response_data = {
            'success': False,
            'error': {
                'code': getattr(exc, 'default_code', 'unknown_error'),
                'message': str(exc),
                'details': response.data if hasattr(response, 'data') else None
            },
            'timestamp': timezone.now().isoformat(),
            'request_id': context['request'].META.get('HTTP_X_REQUEST_ID', 'unknown')
        }

        # 记录错误日志
        logger.error(
            f"API Error: {exc.__class__.__name__} - {str(exc)}",
            extra={
                'request_id': custom_response_data['request_id'],
                'user_id': getattr(context['request'].user, 'id', None),
                'path': context['request'].path,
                'method': context['request'].method
            }
        )

        response.data = custom_response_data

    return response

# 自定义异常类
class BusinessException(Exception):
    """业务异常基类"""
    def __init__(self, message, code=None, details=None):
        self.message = message
        self.code = code or 'business_error'
        self.details = details
        super().__init__(self.message)

class SpacePermissionDenied(BusinessException):
    """空间权限不足异常"""
    def __init__(self, space_id):
        super().__init__(
            message=f"没有权限访问空间 {space_id}",
            code='space_permission_denied',
            details={'space_id': space_id}
        )

class UserNotFound(BusinessException):
    """用户不存在异常"""
    def __init__(self, user_id):
        super().__init__(
            message=f"用户 {user_id} 不存在",
            code='user_not_found',
            details={'user_id': user_id}
        )
```

### 7.2 Vue前端错误处理
```javascript
// utils/errorHandler.js
import { ElMessage, ElNotification } from 'element-plus'

class ErrorHandler {
  static handle(error, context = {}) {
    console.error('Error occurred:', error, context)

    // 网络错误
    if (error.code === 'NETWORK_ERROR') {
      ElMessage.error('网络连接失败，请检查网络设置')
      return
    }

    // API错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          this.handleUnauthorized()
          break
        case 403:
          ElMessage.error('权限不足，无法执行此操作')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          this.handleValidationError(data)
          break
        case 500:
          ElMessage.error('服务器内部错误，请稍后重试')
          break
        default:
          ElMessage.error(data?.error?.message || '未知错误')
      }
    } else {
      // 其他错误
      ElMessage.error(error.message || '发生未知错误')
    }
  }

  static handleUnauthorized() {
    ElNotification({
      title: '登录已过期',
      message: '请重新登录',
      type: 'warning'
    })

    // 清除本地存储的令牌
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')

    // 跳转到登录页
    router.push('/login')
  }

  static handleValidationError(data) {
    if (data?.error?.details) {
      const errors = data.error.details
      Object.keys(errors).forEach(field => {
        const messages = Array.isArray(errors[field]) ? errors[field] : [errors[field]]
        messages.forEach(message => {
          ElMessage.error(`${field}: ${message}`)
        })
      })
    } else {
      ElMessage.error('输入数据验证失败')
    }
  }
}

// 全局错误处理
window.addEventListener('error', (event) => {
  ErrorHandler.handle(event.error, { type: 'global' })
})

window.addEventListener('unhandledrejection', (event) => {
  ErrorHandler.handle(event.reason, { type: 'promise' })
})

export default ErrorHandler
```

---

**文档状态**: 完成
**下一步**: 开始项目实施和开发
**负责人**: 开发团队
**审核人**: 技术架构师
