# 元宇宙社交空间项目 - 数据库设计文档

## 文档信息
- **项目名称**: 元宇宙社交空间平台 (MetaVerse Social Space Platform)
- **文档版本**: v1.0
- **创建日期**: 2025-07-30
- **文档类型**: 数据库设计文档
- **数据库**: MySQL 8.0
- **ORM框架**: Django ORM

## 目录
1. [数据库概述](#1-数据库概述)
2. [概念模型设计](#2-概念模型设计)
3. [逻辑模型设计](#3-逻辑模型设计)
4. [物理模型设计](#4-物理模型设计)
5. [数据字典](#5-数据字典)
6. [索引设计](#6-索引设计)
7. [分库分表策略](#7-分库分表策略)
8. [数据安全设计](#8-数据安全设计)

## 1. 数据库概述

### 1.1 设计原则
#### 1.1.1 高内聚低耦合原则
- **领域内聚**: 按业务领域划分数据模型，相关数据集中管理
- **功能内聚**: 每个表承担单一职责，避免混合不同业务概念
- **数据内聚**: 相关字段组织在同一表中，减少跨表查询
- **模块解耦**: 不同业务模块的表通过外键松散关联，避免强依赖

#### 1.1.2 数据库设计原则
- **规范化**: 遵循第三范式，避免数据冗余和更新异常
- **性能优化**: 合理使用索引，优化查询性能和并发访问
- **扩展性**: 支持水平分片和垂直分库，应对大规模数据
- **一致性**: 保证数据的完整性约束和事务一致性
- **安全性**: 敏感数据加密存储，实现数据访问控制

### 1.2 高内聚低耦合数据库架构
#### 1.2.1 按业务域分库设计（低耦合）
```
用户域数据库 (metaverse_user)
├── users                    # 用户基础信息（高内聚）
├── user_profiles            # 用户扩展资料
├── avatars                  # 虚拟形象数据
├── user_sessions            # 用户会话管理
└── user_preferences         # 用户偏好设置

空间域数据库 (metaverse_space)
├── spaces                   # 虚拟空间信息（高内聚）
├── space_categories         # 空间分类
├── scenes                   # 3D场景数据
├── space_members            # 空间成员关系
└── space_permissions        # 空间权限配置

社交域数据库 (metaverse_social)
├── friendships              # 好友关系（高内聚）
├── groups                   # 用户群组
├── group_members            # 群组成员
├── social_activities        # 社交活动记录
└── recommendations          # 社交推荐数据

通信域数据库 (metaverse_message)
├── messages                 # 消息记录（高内聚）
├── message_threads          # 消息会话
├── message_attachments      # 消息附件
├── notifications            # 通知消息
└── push_tokens              # 推送令牌

内容域数据库 (metaverse_content)
├── contents                 # 用户内容（高内聚）
├── content_categories       # 内容分类
├── content_tags             # 内容标签
├── content_reviews          # 内容审核
└── content_statistics       # 内容统计

经济域数据库 (metaverse_economy)
├── virtual_items            # 虚拟物品（高内聚）
├── transactions             # 交易记录
├── user_wallets             # 用户钱包
├── payment_methods          # 支付方式
└── financial_records        # 财务记录
```

#### 1.2.2 缓存架构设计（性能优化）
```
Redis集群架构
├── 会话缓存集群 (Session Cache)
│   ├── 用户会话数据
│   ├── JWT令牌缓存
│   └── 登录状态管理
├── 业务缓存集群 (Business Cache)
│   ├── 热点空间数据
│   ├── 用户资料缓存
│   ├── 好友关系缓存
│   └── 内容元数据缓存
├── 实时数据集群 (Realtime Cache)
│   ├── 在线用户状态
│   ├── 空间实时数据
│   ├── 消息队列缓存
│   └── WebSocket连接池
└── 计数器集群 (Counter Cache)
    ├── 访问计数器
    ├── 点赞计数器
    ├── 统计数据缓存
    └── 排行榜数据
```

#### 1.2.3 数据一致性策略
```python
# 跨库事务处理 - 最终一致性
from django.db import transaction
from django_mysql.models import Model

class CrossDomainTransaction:
    """跨域事务管理 - 保证最终一致性"""

    def __init__(self):
        self.operations = []
        self.compensations = []

    def add_operation(self, operation, compensation=None):
        """添加操作和补偿动作"""
        self.operations.append(operation)
        if compensation:
            self.compensations.append(compensation)

    @transaction.atomic
    def execute(self):
        """执行事务操作"""
        try:
            # 执行所有操作
            for operation in self.operations:
                operation()

            # 发布领域事件确保最终一致性
            self.publish_domain_events()

        except Exception as e:
            # 执行补偿操作
            self.execute_compensations()
            raise e

    def execute_compensations(self):
        """执行补偿操作"""
        for compensation in reversed(self.compensations):
            try:
                compensation()
            except Exception as e:
                logger.error(f"Compensation failed: {e}")

# 示例：用户注册跨域事务
def register_user_cross_domain(user_data):
    transaction_manager = CrossDomainTransaction()

    # 用户域操作
    def create_user():
        return User.objects.create(**user_data)

    def delete_user(user_id):
        User.objects.filter(id=user_id).delete()

    # 经济域操作
    def create_wallet(user_id):
        return UserWallet.objects.create(user_id=user_id, balance=0)

    def delete_wallet(user_id):
        UserWallet.objects.filter(user_id=user_id).delete()

    # 添加操作和补偿
    user = None
    transaction_manager.add_operation(
        lambda: create_user(),
        lambda: delete_user(user.id) if user else None
    )

    transaction_manager.add_operation(
        lambda: create_wallet(user.id),
        lambda: delete_wallet(user.id)
    )

    return transaction_manager.execute()
```

### 1.3 Django模型设计规范
```python
# 基础模型类
class BaseModel(models.Model):
    """所有模型的基类"""
    id = models.BigAutoField(primary_key=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    
    class Meta:
        abstract = True
        
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.save()
```

## 2. 概念模型设计

### 2.1 核心实体
- **用户 (User)**: 系统用户基本信息
- **虚拟形象 (Avatar)**: 用户的3D虚拟形象
- **空间 (Space)**: 虚拟3D空间
- **场景 (Scene)**: 空间内的3D场景数据
- **好友关系 (Friendship)**: 用户间的好友关系
- **群组 (Group)**: 用户群组
- **消息 (Message)**: 聊天消息
- **内容 (Content)**: 用户生成内容
- **虚拟物品 (VirtualItem)**: 虚拟商品
- **交易 (Transaction)**: 交易记录

### 2.2 实体关系
```
用户 1:1 虚拟形象
用户 1:N 空间 (拥有)
用户 M:N 空间 (访问)
用户 M:N 用户 (好友关系)
用户 M:N 群组
用户 1:N 消息
用户 1:N 内容
用户 1:N 交易
空间 1:1 场景
空间 1:N 消息
内容 1:N 虚拟物品
```

## 3. 逻辑模型设计

### 3.1 用户相关模型
```python
# users/models.py
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator

class User(AbstractUser):
    """用户模型"""
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="手机号格式不正确"
    )
    
    email = models.EmailField(unique=True, verbose_name='邮箱')
    phone = models.CharField(
        validators=[phone_regex], 
        max_length=17, 
        blank=True, 
        verbose_name='手机号'
    )
    nickname = models.CharField(max_length=50, verbose_name='昵称')
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    birth_date = models.DateField(null=True, blank=True, verbose_name='生日')
    gender = models.CharField(
        max_length=10,
        choices=[('male', '男'), ('female', '女'), ('other', '其他')],
        blank=True,
        verbose_name='性别'
    )
    avatar_url = models.URLField(blank=True, verbose_name='头像URL')
    is_verified = models.BooleanField(default=False, verbose_name='是否认证')
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name='最后登录IP')
    login_count = models.PositiveIntegerField(default=0, verbose_name='登录次数')
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', '活跃'),
            ('inactive', '非活跃'),
            ('suspended', '暂停'),
            ('banned', '封禁')
        ],
        default='active',
        verbose_name='状态'
    )
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['status']),
            models.Index(fields=['is_verified']),
        ]

class UserProfile(BaseModel):
    """用户扩展资料"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    real_name = models.CharField(max_length=50, blank=True, verbose_name='真实姓名')
    id_card = models.CharField(max_length=18, blank=True, verbose_name='身份证号')
    address = models.TextField(blank=True, verbose_name='地址')
    occupation = models.CharField(max_length=100, blank=True, verbose_name='职业')
    company = models.CharField(max_length=100, blank=True, verbose_name='公司')
    website = models.URLField(blank=True, verbose_name='个人网站')
    social_links = models.JSONField(default=dict, verbose_name='社交链接')
    preferences = models.JSONField(default=dict, verbose_name='用户偏好')
    privacy_settings = models.JSONField(default=dict, verbose_name='隐私设置')
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

class Avatar(BaseModel):
    """虚拟形象"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    name = models.CharField(max_length=50, verbose_name='形象名称')
    model_url = models.URLField(verbose_name='3D模型URL')
    thumbnail_url = models.URLField(verbose_name='缩略图URL')
    config_data = models.JSONField(default=dict, verbose_name='配置数据')
    customization = models.JSONField(default=dict, verbose_name='自定义数据')
    is_default = models.BooleanField(default=True, verbose_name='是否默认')
    
    class Meta:
        db_table = 'avatars'
        verbose_name = '虚拟形象'
        verbose_name_plural = '虚拟形象'
```

### 3.2 空间相关模型
```python
# spaces/models.py
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class SpaceCategory(BaseModel):
    """空间分类"""
    name = models.CharField(max_length=50, unique=True, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    icon = models.URLField(blank=True, verbose_name='分类图标')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    class Meta:
        db_table = 'space_categories'
        verbose_name = '空间分类'
        verbose_name_plural = '空间分类'
        ordering = ['sort_order', 'name']

class Space(BaseModel):
    """虚拟空间"""
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='拥有者')
    category = models.ForeignKey(SpaceCategory, on_delete=models.SET_NULL, null=True, verbose_name='分类')
    name = models.CharField(max_length=100, verbose_name='空间名称')
    description = models.TextField(verbose_name='空间描述')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图')
    
    type = models.CharField(
        max_length=20,
        choices=[
            ('private', '私人'),
            ('public', '公开'),
            ('enterprise', '企业'),
            ('educational', '教育')
        ],
        default='private',
        verbose_name='空间类型'
    )
    
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', '活跃'),
            ('inactive', '非活跃'),
            ('maintenance', '维护中'),
            ('suspended', '暂停')
        ],
        default='active',
        verbose_name='状态'
    )
    
    max_capacity = models.PositiveIntegerField(default=50, verbose_name='最大容量')
    current_users = models.PositiveIntegerField(default=0, verbose_name='当前用户数')
    visit_count = models.PositiveIntegerField(default=0, verbose_name='访问次数')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    
    # 空间设置
    settings = models.JSONField(default=dict, verbose_name='空间设置')
    
    # 访问控制
    is_password_protected = models.BooleanField(default=False, verbose_name='是否密码保护')
    password = models.CharField(max_length=128, blank=True, verbose_name='访问密码')
    invite_code = models.CharField(max_length=20, unique=True, blank=True, verbose_name='邀请码')
    
    # 地理位置（可选）
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True, verbose_name='纬度')
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True, verbose_name='经度')
    
    # 标签
    tags = models.JSONField(default=list, verbose_name='标签')
    
    class Meta:
        db_table = 'spaces'
        verbose_name = '虚拟空间'
        verbose_name_plural = '虚拟空间'
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['category']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['visit_count']),
            models.Index(fields=['like_count']),
            models.Index(fields=['created_at']),
        ]

class Scene(BaseModel):
    """3D场景数据"""
    space = models.OneToOneField(Space, on_delete=models.CASCADE, verbose_name='空间')
    scene_data = models.JSONField(verbose_name='场景数据')
    lighting_config = models.JSONField(default=dict, verbose_name='光照配置')
    physics_config = models.JSONField(default=dict, verbose_name='物理配置')
    audio_config = models.JSONField(default=dict, verbose_name='音频配置')
    version = models.PositiveIntegerField(default=1, verbose_name='版本号')
    file_size = models.PositiveIntegerField(default=0, verbose_name='文件大小(字节)')
    
    class Meta:
        db_table = 'scenes'
        verbose_name = '3D场景'
        verbose_name_plural = '3D场景'

class SpaceMember(BaseModel):
    """空间成员"""
    space = models.ForeignKey(Space, on_delete=models.CASCADE, verbose_name='空间')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(
        max_length=20,
        choices=[
            ('owner', '拥有者'),
            ('admin', '管理员'),
            ('moderator', '版主'),
            ('member', '成员'),
            ('visitor', '访客')
        ],
        default='member',
        verbose_name='角色'
    )
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name='加入时间')
    last_visit = models.DateTimeField(null=True, blank=True, verbose_name='最后访问')
    visit_count = models.PositiveIntegerField(default=0, verbose_name='访问次数')
    total_time = models.PositiveIntegerField(default=0, verbose_name='总停留时间(秒)')
    
    class Meta:
        db_table = 'space_members'
        verbose_name = '空间成员'
        verbose_name_plural = '空间成员'
        unique_together = ['space', 'user']
        indexes = [
            models.Index(fields=['space', 'role']),
            models.Index(fields=['user', 'joined_at']),
        ]
```

### 3.3 社交相关模型
```python
# social/models.py
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Friendship(BaseModel):
    """好友关系"""
    from_user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='sent_friend_requests',
        verbose_name='发起用户'
    )
    to_user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_friend_requests',
        verbose_name='接收用户'
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待确认'),
            ('accepted', '已接受'),
            ('rejected', '已拒绝'),
            ('blocked', '已屏蔽')
        ],
        default='pending',
        verbose_name='状态'
    )
    message = models.TextField(blank=True, verbose_name='申请消息')
    accepted_at = models.DateTimeField(null=True, blank=True, verbose_name='接受时间')
    
    class Meta:
        db_table = 'friendships'
        verbose_name = '好友关系'
        verbose_name_plural = '好友关系'
        unique_together = ['from_user', 'to_user']
        indexes = [
            models.Index(fields=['from_user', 'status']),
            models.Index(fields=['to_user', 'status']),
        ]

class Group(BaseModel):
    """用户群组"""
    name = models.CharField(max_length=100, verbose_name='群组名称')
    description = models.TextField(blank=True, verbose_name='群组描述')
    avatar_url = models.URLField(blank=True, verbose_name='群组头像')
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='群主')
    
    type = models.CharField(
        max_length=20,
        choices=[
            ('public', '公开'),
            ('private', '私有'),
            ('secret', '秘密')
        ],
        default='public',
        verbose_name='群组类型'
    )
    
    max_members = models.PositiveIntegerField(default=500, verbose_name='最大成员数')
    member_count = models.PositiveIntegerField(default=1, verbose_name='当前成员数')
    
    # 群组设置
    settings = models.JSONField(default=dict, verbose_name='群组设置')
    
    class Meta:
        db_table = 'groups'
        verbose_name = '用户群组'
        verbose_name_plural = '用户群组'
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['type']),
            models.Index(fields=['member_count']),
        ]

class GroupMember(BaseModel):
    """群组成员"""
    group = models.ForeignKey(Group, on_delete=models.CASCADE, verbose_name='群组')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(
        max_length=20,
        choices=[
            ('owner', '群主'),
            ('admin', '管理员'),
            ('member', '成员')
        ],
        default='member',
        verbose_name='角色'
    )
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name='加入时间')
    is_muted = models.BooleanField(default=False, verbose_name='是否禁言')
    
    class Meta:
        db_table = 'group_members'
        verbose_name = '群组成员'
        verbose_name_plural = '群组成员'
        unique_together = ['group', 'user']
        indexes = [
            models.Index(fields=['group', 'role']),
            models.Index(fields=['user', 'joined_at']),
        ]
```

### 3.4 消息相关模型
```python
# messaging/models.py
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Message(BaseModel):
    """消息模型"""
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name='发送者'
    )

    # 消息接收目标（三选一）
    receiver = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_messages',
        null=True,
        blank=True,
        verbose_name='接收者'
    )
    space = models.ForeignKey(
        'spaces.Space',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='空间'
    )
    group = models.ForeignKey(
        'social.Group',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='群组'
    )

    message_type = models.CharField(
        max_length=20,
        choices=[
            ('text', '文本'),
            ('image', '图片'),
            ('audio', '语音'),
            ('video', '视频'),
            ('file', '文件'),
            ('system', '系统消息'),
            ('gesture', '手势'),
            ('location', '位置')
        ],
        default='text',
        verbose_name='消息类型'
    )

    content = models.TextField(verbose_name='消息内容')
    metadata = models.JSONField(default=dict, verbose_name='元数据')

    # 消息状态
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')

    # 回复相关
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='回复消息'
    )

    class Meta:
        db_table = 'messages'
        verbose_name = '消息'
        verbose_name_plural = '消息'
        indexes = [
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['receiver', 'is_read']),
            models.Index(fields=['space', 'created_at']),
            models.Index(fields=['group', 'created_at']),
            models.Index(fields=['message_type']),
        ]

class MessageRead(BaseModel):
    """消息阅读记录"""
    message = models.ForeignKey(Message, on_delete=models.CASCADE, verbose_name='消息')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    read_at = models.DateTimeField(auto_now_add=True, verbose_name='阅读时间')

    class Meta:
        db_table = 'message_reads'
        verbose_name = '消息阅读记录'
        verbose_name_plural = '消息阅读记录'
        unique_together = ['message', 'user']
```

### 3.5 内容相关模型
```python
# content/models.py
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Content(BaseModel):
    """用户生成内容"""
    creator = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    space = models.ForeignKey(
        'spaces.Space',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='关联空间'
    )

    name = models.CharField(max_length=200, verbose_name='内容名称')
    description = models.TextField(verbose_name='内容描述')

    content_type = models.CharField(
        max_length=20,
        choices=[
            ('model_3d', '3D模型'),
            ('texture', '纹理'),
            ('audio', '音频'),
            ('video', '视频'),
            ('image', '图片'),
            ('animation', '动画'),
            ('script', '脚本'),
            ('scene', '场景')
        ],
        verbose_name='内容类型'
    )

    # 文件信息
    file_url = models.URLField(verbose_name='文件URL')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    file_size = models.PositiveIntegerField(verbose_name='文件大小(字节)')
    file_format = models.CharField(max_length=20, verbose_name='文件格式')

    # 分类和标签
    category = models.CharField(max_length=50, verbose_name='分类')
    tags = models.JSONField(default=list, verbose_name='标签')

    # 版权信息
    license = models.CharField(
        max_length=20,
        choices=[
            ('public', '公开'),
            ('private', '私有'),
            ('commercial', '商业'),
            ('cc_by', 'CC BY'),
            ('cc_by_sa', 'CC BY-SA'),
            ('cc_by_nc', 'CC BY-NC')
        ],
        default='private',
        verbose_name='许可证'
    )

    # 状态
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', '草稿'),
            ('pending', '待审核'),
            ('approved', '已通过'),
            ('rejected', '已拒绝'),
            ('suspended', '已暂停')
        ],
        default='draft',
        verbose_name='状态'
    )

    # 统计信息
    download_count = models.PositiveIntegerField(default=0, verbose_name='下载次数')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    view_count = models.PositiveIntegerField(default=0, verbose_name='查看次数')

    # 技术参数
    technical_specs = models.JSONField(default=dict, verbose_name='技术规格')

    class Meta:
        db_table = 'contents'
        verbose_name = '用户内容'
        verbose_name_plural = '用户内容'
        indexes = [
            models.Index(fields=['creator']),
            models.Index(fields=['content_type']),
            models.Index(fields=['category']),
            models.Index(fields=['status']),
            models.Index(fields=['like_count']),
            models.Index(fields=['download_count']),
            models.Index(fields=['created_at']),
        ]

class ContentLike(BaseModel):
    """内容点赞"""
    content = models.ForeignKey(Content, on_delete=models.CASCADE, verbose_name='内容')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')

    class Meta:
        db_table = 'content_likes'
        verbose_name = '内容点赞'
        verbose_name_plural = '内容点赞'
        unique_together = ['content', 'user']

class ContentComment(BaseModel):
    """内容评论"""
    content = models.ForeignKey(Content, on_delete=models.CASCADE, verbose_name='内容')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='父评论'
    )
    comment = models.TextField(verbose_name='评论内容')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')

    class Meta:
        db_table = 'content_comments'
        verbose_name = '内容评论'
        verbose_name_plural = '内容评论'
        indexes = [
            models.Index(fields=['content', 'created_at']),
            models.Index(fields=['user']),
            models.Index(fields=['parent']),
        ]
```

### 3.6 经济相关模型
```python
# economy/models.py
from django.db import models
from django.contrib.auth import get_user_model
from decimal import Decimal

User = get_user_model()

class VirtualItem(BaseModel):
    """虚拟物品"""
    content = models.ForeignKey(
        'content.Content',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='关联内容'
    )
    creator = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')

    name = models.CharField(max_length=200, verbose_name='物品名称')
    description = models.TextField(verbose_name='物品描述')

    item_type = models.CharField(
        max_length=20,
        choices=[
            ('avatar_item', '虚拟形象道具'),
            ('space_decoration', '空间装饰'),
            ('functional_item', '功能道具'),
            ('collectible', '收藏品'),
            ('nft', 'NFT'),
            ('currency', '虚拟货币')
        ],
        verbose_name='物品类型'
    )

    # 价格信息
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')
    currency = models.CharField(
        max_length=10,
        choices=[
            ('CNY', '人民币'),
            ('USD', '美元'),
            ('COIN', '平台币'),
            ('ETH', '以太币')
        ],
        default='CNY',
        verbose_name='货币类型'
    )

    # 库存信息
    stock_quantity = models.PositiveIntegerField(default=1, verbose_name='库存数量')
    sold_count = models.PositiveIntegerField(default=0, verbose_name='销售数量')
    is_unlimited = models.BooleanField(default=False, verbose_name='是否无限量')

    # 物品属性
    attributes = models.JSONField(default=dict, verbose_name='物品属性')
    rarity = models.CharField(
        max_length=20,
        choices=[
            ('common', '普通'),
            ('uncommon', '不常见'),
            ('rare', '稀有'),
            ('epic', '史诗'),
            ('legendary', '传说')
        ],
        default='common',
        verbose_name='稀有度'
    )

    # 状态
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', '草稿'),
            ('active', '上架'),
            ('inactive', '下架'),
            ('sold_out', '售罄')
        ],
        default='draft',
        verbose_name='状态'
    )

    # NFT相关
    nft_contract = models.CharField(max_length=100, blank=True, verbose_name='NFT合约地址')
    nft_token_id = models.CharField(max_length=100, blank=True, verbose_name='NFT Token ID')

    class Meta:
        db_table = 'virtual_items'
        verbose_name = '虚拟物品'
        verbose_name_plural = '虚拟物品'
        indexes = [
            models.Index(fields=['creator']),
            models.Index(fields=['item_type']),
            models.Index(fields=['status']),
            models.Index(fields=['price']),
            models.Index(fields=['rarity']),
        ]

class UserWallet(BaseModel):
    """用户钱包"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    currency = models.CharField(
        max_length=10,
        choices=[
            ('CNY', '人民币'),
            ('USD', '美元'),
            ('COIN', '平台币'),
            ('ETH', '以太币')
        ],
        verbose_name='货币类型'
    )
    balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='可用余额'
    )
    frozen_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='冻结余额'
    )

    class Meta:
        db_table = 'user_wallets'
        verbose_name = '用户钱包'
        verbose_name_plural = '用户钱包'
        unique_together = ['user', 'currency']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['currency']),
        ]

class Transaction(BaseModel):
    """交易记录"""
    buyer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='purchases',
        verbose_name='买家'
    )
    seller = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sales',
        null=True,
        blank=True,
        verbose_name='卖家'
    )
    item = models.ForeignKey(VirtualItem, on_delete=models.CASCADE, verbose_name='商品')

    transaction_type = models.CharField(
        max_length=20,
        choices=[
            ('purchase', '购买'),
            ('gift', '赠送'),
            ('refund', '退款'),
            ('transfer', '转账')
        ],
        default='purchase',
        verbose_name='交易类型'
    )

    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='交易金额')
    currency = models.CharField(max_length=10, verbose_name='货币类型')

    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待支付'),
            ('processing', '处理中'),
            ('completed', '已完成'),
            ('failed', '失败'),
            ('cancelled', '已取消'),
            ('refunded', '已退款')
        ],
        default='pending',
        verbose_name='交易状态'
    )

    # 支付信息
    payment_method = models.CharField(max_length=50, blank=True, verbose_name='支付方式')
    payment_info = models.JSONField(default=dict, verbose_name='支付信息')

    # 时间信息
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    # 交易备注
    notes = models.TextField(blank=True, verbose_name='交易备注')

    class Meta:
        db_table = 'transactions'
        verbose_name = '交易记录'
        verbose_name_plural = '交易记录'
        indexes = [
            models.Index(fields=['buyer', 'created_at']),
            models.Index(fields=['seller', 'created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['transaction_type']),
            models.Index(fields=['completed_at']),
        ]
```

## 4. 物理模型设计

### 4.1 表结构设计
#### 4.1.1 MySQL建表语句示例
```sql
-- 用户表
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(150) NOT NULL UNIQUE,
  `email` varchar(254) NOT NULL UNIQUE,
  `phone` varchar(17) DEFAULT NULL,
  `nickname` varchar(50) NOT NULL,
  `bio` text,
  `birth_date` date DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `avatar_url` varchar(200) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_ip` varchar(39) DEFAULT NULL,
  `login_count` int unsigned NOT NULL DEFAULT '0',
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `password` varchar(128) NOT NULL,
  `is_staff` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `date_joined` datetime(6) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_phone` (`phone`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_is_verified` (`is_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 虚拟空间表
CREATE TABLE `spaces` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `owner_id` bigint NOT NULL,
  `category_id` bigint DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `thumbnail_url` varchar(200) DEFAULT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'private',
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `max_capacity` int unsigned NOT NULL DEFAULT '50',
  `current_users` int unsigned NOT NULL DEFAULT '0',
  `visit_count` int unsigned NOT NULL DEFAULT '0',
  `like_count` int unsigned NOT NULL DEFAULT '0',
  `settings` json DEFAULT NULL,
  `is_password_protected` tinyint(1) NOT NULL DEFAULT '0',
  `password` varchar(128) DEFAULT NULL,
  `invite_code` varchar(20) DEFAULT NULL UNIQUE,
  `latitude` decimal(9,6) DEFAULT NULL,
  `longitude` decimal(9,6) DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `idx_spaces_owner` (`owner_id`),
  KEY `idx_spaces_category` (`category_id`),
  KEY `idx_spaces_type` (`type`),
  KEY `idx_spaces_status` (`status`),
  KEY `idx_spaces_visit_count` (`visit_count`),
  KEY `idx_spaces_like_count` (`like_count`),
  KEY `idx_spaces_created_at` (`created_at`),
  CONSTRAINT `fk_spaces_owner` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_spaces_category` FOREIGN KEY (`category_id`) REFERENCES `space_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4.2 分区策略
#### 4.2.1 按时间分区
```sql
-- 消息表按月分区
CREATE TABLE `messages` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sender_id` bigint NOT NULL,
  `receiver_id` bigint DEFAULT NULL,
  `space_id` bigint DEFAULT NULL,
  `group_id` bigint DEFAULT NULL,
  `message_type` varchar(20) NOT NULL DEFAULT 'text',
  `content` text NOT NULL,
  `metadata` json DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` datetime(6) DEFAULT NULL,
  `reply_to_id` bigint DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`, `created_at`),
  KEY `idx_messages_sender_created` (`sender_id`, `created_at`),
  KEY `idx_messages_receiver_read` (`receiver_id`, `is_read`),
  KEY `idx_messages_space_created` (`space_id`, `created_at`),
  KEY `idx_messages_group_created` (`group_id`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202501 VALUES LESS THAN (202502),
  PARTITION p202502 VALUES LESS THAN (202503),
  PARTITION p202503 VALUES LESS THAN (202504),
  PARTITION p202504 VALUES LESS THAN (202505),
  PARTITION p202505 VALUES LESS THAN (202506),
  PARTITION p202506 VALUES LESS THAN (202507),
  PARTITION p202507 VALUES LESS THAN (202508),
  PARTITION p202508 VALUES LESS THAN (202509),
  PARTITION p202509 VALUES LESS THAN (202510),
  PARTITION p202510 VALUES LESS THAN (202511),
  PARTITION p202511 VALUES LESS THAN (202512),
  PARTITION p202512 VALUES LESS THAN (202601),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 4.2.2 按用户ID分区
```sql
-- 用户行为日志表按用户ID哈希分区
CREATE TABLE `user_activity_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `activity_data` json DEFAULT NULL,
  `ip_address` varchar(39) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`, `user_id`),
  KEY `idx_activity_user_type` (`user_id`, `activity_type`),
  KEY `idx_activity_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY HASH(`user_id`) PARTITIONS 16;
```

## 5. 数据字典

### 5.1 核心表字段说明
#### 5.1.1 用户表 (users)
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | bigint | - | NO | AUTO_INCREMENT | 主键ID |
| username | varchar | 150 | NO | - | 用户名，唯一 |
| email | varchar | 254 | NO | - | 邮箱地址，唯一 |
| phone | varchar | 17 | YES | NULL | 手机号码 |
| nickname | varchar | 50 | NO | - | 用户昵称 |
| bio | text | - | YES | NULL | 个人简介 |
| birth_date | date | - | YES | NULL | 出生日期 |
| gender | varchar | 10 | YES | NULL | 性别：male/female/other |
| avatar_url | varchar | 200 | YES | NULL | 头像URL |
| is_verified | tinyint | 1 | NO | 0 | 是否认证用户 |
| last_login_ip | varchar | 39 | YES | NULL | 最后登录IP |
| login_count | int | - | NO | 0 | 登录次数 |
| status | varchar | 20 | NO | active | 用户状态 |
| created_at | datetime | 6 | NO | - | 创建时间 |
| updated_at | datetime | 6 | NO | - | 更新时间 |

#### 5.1.2 虚拟空间表 (spaces)
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | bigint | - | NO | AUTO_INCREMENT | 主键ID |
| owner_id | bigint | - | NO | - | 空间拥有者ID |
| category_id | bigint | - | YES | NULL | 空间分类ID |
| name | varchar | 100 | NO | - | 空间名称 |
| description | text | - | NO | - | 空间描述 |
| type | varchar | 20 | NO | private | 空间类型 |
| status | varchar | 20 | NO | active | 空间状态 |
| max_capacity | int | - | NO | 50 | 最大容量 |
| current_users | int | - | NO | 0 | 当前用户数 |
| visit_count | int | - | NO | 0 | 访问次数 |
| like_count | int | - | NO | 0 | 点赞数 |
| settings | json | - | YES | NULL | 空间设置 |
| invite_code | varchar | 20 | YES | NULL | 邀请码，唯一 |
| tags | json | - | YES | NULL | 标签数组 |

### 5.2 枚举值说明
#### 5.2.1 用户状态 (user.status)
- `active`: 活跃用户，正常使用
- `inactive`: 非活跃用户，长期未登录
- `suspended`: 暂停用户，临时限制使用
- `banned`: 封禁用户，永久限制使用

#### 5.2.2 空间类型 (space.type)
- `private`: 私人空间，仅拥有者和邀请用户可访问
- `public`: 公开空间，所有用户可访问
- `enterprise`: 企业空间，企业用户专用
- `educational`: 教育空间，教育机构专用

#### 5.2.3 消息类型 (message.message_type)
- `text`: 文本消息
- `image`: 图片消息
- `audio`: 语音消息
- `video`: 视频消息
- `file`: 文件消息
- `system`: 系统消息
- `gesture`: 手势消息
- `location`: 位置消息

## 6. 索引设计

### 6.1 主键索引
所有表都使用`bigint`类型的自增主键，确保唯一性和查询性能。

### 6.2 唯一索引
```sql
-- 用户表唯一索引
ALTER TABLE users ADD UNIQUE INDEX uk_users_username (username);
ALTER TABLE users ADD UNIQUE INDEX uk_users_email (email);

-- 空间表唯一索引
ALTER TABLE spaces ADD UNIQUE INDEX uk_spaces_invite_code (invite_code);

-- 好友关系唯一索引
ALTER TABLE friendships ADD UNIQUE INDEX uk_friendships_users (from_user_id, to_user_id);
```

### 6.3 复合索引
```sql
-- 消息表复合索引
ALTER TABLE messages ADD INDEX idx_messages_sender_created (sender_id, created_at);
ALTER TABLE messages ADD INDEX idx_messages_receiver_read (receiver_id, is_read);
ALTER TABLE messages ADD INDEX idx_messages_space_created (space_id, created_at);

-- 空间成员复合索引
ALTER TABLE space_members ADD INDEX idx_space_members_space_role (space_id, role);
ALTER TABLE space_members ADD INDEX idx_space_members_user_joined (user_id, joined_at);

-- 内容表复合索引
ALTER TABLE contents ADD INDEX idx_contents_creator_type (creator_id, content_type);
ALTER TABLE contents ADD INDEX idx_contents_status_created (status, created_at);
```

### 6.4 全文索引
```sql
-- 空间搜索全文索引
ALTER TABLE spaces ADD FULLTEXT INDEX ft_spaces_search (name, description);

-- 内容搜索全文索引
ALTER TABLE contents ADD FULLTEXT INDEX ft_contents_search (name, description);

-- 用户搜索全文索引
ALTER TABLE users ADD FULLTEXT INDEX ft_users_search (username, nickname, bio);
```

## 7. 分库分表策略

### 7.1 垂直分库
```
用户数据库 (metaverse_user)
├── users
├── user_profiles
├── avatars
└── user_wallets

空间数据库 (metaverse_space)
├── spaces
├── space_categories
├── scenes
└── space_members

社交数据库 (metaverse_social)
├── friendships
├── groups
├── group_members
└── messages

内容数据库 (metaverse_content)
├── contents
├── content_likes
├── content_comments
└── virtual_items

交易数据库 (metaverse_transaction)
├── transactions
├── user_wallets
└── payment_records
```

### 7.2 水平分表
#### 7.2.1 按用户ID分表
```python
# Django数据库路由配置
class DatabaseRouter:
    def db_for_read(self, model, **hints):
        if model._meta.app_label == 'messaging':
            if hints.get('instance'):
                user_id = getattr(hints['instance'], 'sender_id', None)
                if user_id:
                    return f'messages_db_{user_id % 4}'
        return None

    def db_for_write(self, model, **hints):
        return self.db_for_read(model, **hints)
```

#### 7.2.2 按时间分表
```python
# 按月分表的消息模型
class MessageManager(models.Manager):
    def get_table_name(self, date=None):
        if date is None:
            date = timezone.now()
        return f"messages_{date.year}_{date.month:02d}"

    def create_monthly_table(self, year, month):
        table_name = f"messages_{year}_{month:02d}"
        with connection.cursor() as cursor:
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name} LIKE messages_template
            """)
```

## 8. 数据安全设计

### 8.1 敏感数据加密
```python
# Django模型中的敏感字段加密
from django_cryptography.fields import encrypt

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    real_name = encrypt(models.CharField(max_length=50, blank=True))
    id_card = encrypt(models.CharField(max_length=18, blank=True))
    phone = encrypt(models.CharField(max_length=17, blank=True))
```

### 8.2 数据备份策略
```bash
#!/bin/bash
# 数据库备份脚本
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 全量备份
mysqldump --single-transaction --routines --triggers \
  --all-databases > $BACKUP_DIR/full_backup_$DATE.sql

# 增量备份（binlog）
mysqlbinlog --start-datetime="$(date -d '1 hour ago' '+%Y-%m-%d %H:%M:%S')" \
  /var/log/mysql/mysql-bin.* > $BACKUP_DIR/incremental_backup_$DATE.sql
```

### 8.3 访问控制
```sql
-- 创建只读用户
CREATE USER 'readonly'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT ON metaverse_main.* TO 'readonly'@'%';

-- 创建应用用户
CREATE USER 'app_user'@'%' IDENTIFIED BY 'app_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON metaverse_main.* TO 'app_user'@'%';

-- 创建备份用户
CREATE USER 'backup_user'@'localhost' IDENTIFIED BY 'backup_password';
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON *.* TO 'backup_user'@'localhost';
```

---

**文档状态**: 完成
**下一步**: 开始详细设计文档的完善
**负责人**: 数据库设计团队
**审核人**: 技术架构师
