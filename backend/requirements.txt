# Django核心框架
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3

# 认证和权限
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==1.7.1

# 数据库
mysqlclient==2.2.0
django-redis==5.4.0
redis==5.0.1

# 异步和WebSocket
channels==4.0.0
channels-redis==4.1.0
daphne==4.0.0

# 异步任务队列
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# 文件处理
Pillow==10.1.0
python-magic==0.4.27
django-storages==1.14.2
boto3==1.34.0  # AWS S3支持

# API文档
drf-spectacular==0.26.5

# 工具库
python-decouple==3.8
python-dotenv==1.0.0
requests==2.31.0
httpx==0.25.2

# 数据验证
pydantic==2.5.0
marshmallow==3.20.1

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 加密和安全
cryptography==41.0.8
bcrypt==4.1.2
PyJWT==2.8.0

# 图像处理
opencv-python==********
numpy==1.25.2

# 3D模型处理
trimesh==4.0.5
pygltflib==1.16.1

# 音频处理
pydub==0.25.1
librosa==0.10.1

# 机器学习（推荐系统）
scikit-learn==1.3.2
pandas==2.1.4
scipy==1.11.4

# 监控和日志
sentry-sdk==1.38.0
structlog==23.2.0
django-extensions==3.2.3

# 测试
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# 代码质量
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1

# 性能分析
django-debug-toolbar==4.2.0
django-silk==5.0.4

# 国际化
django-modeltranslation==0.18.11

# 缓存
django-cachalot==2.6.1

# 限流
django-ratelimit==4.1.0

# 健康检查
django-health-check==3.17.0

# 环境变量
python-environ==0.11.2

# WebRTC支持
aiortc==1.6.0

# 区块链集成
web3==6.12.0
eth-account==0.9.0

# 支付集成
stripe==7.8.0
alipay-sdk-python==3.7.1

# 邮件服务
django-anymail==10.2

# 短信服务
twilio==8.11.0

# 地理位置
geopy==2.4.1
django-location-field==2.7.3

# 搜索引擎
elasticsearch==8.11.0
django-elasticsearch-dsl==7.3

# 消息队列
kombu==5.3.4
billiard==4.2.0

# 配置管理
dynaconf==3.2.4

# 开发工具（仅开发环境）
ipython==8.17.2
jupyter==1.0.0
django-shell-plus==1.1.7