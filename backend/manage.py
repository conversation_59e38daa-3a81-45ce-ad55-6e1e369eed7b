#!/usr/bin/env python
"""
Django 命令行管理工具
用于执行各种管理任务，如数据库迁移、创建用户、运行服务器等

常用命令：
- python manage.py runserver          # 启动开发服务器
- python manage.py makemigrations     # 创建数据库迁移文件
- python manage.py migrate            # 执行数据库迁移
- python manage.py createsuperuser    # 创建超级用户
- python manage.py collectstatic      # 收集静态文件
- python manage.py shell              # 进入Django交互式shell
- python manage.py test               # 运行测试
"""
import os
import sys


def main():
    """
    执行管理任务的主函数

    该函数负责：
    1. 设置Django配置模块
    2. 导入Django管理命令执行器
    3. 执行命令行传入的管理命令
    """
    # 设置默认的Django配置模块为本地开发环境
    # 如果环境变量中没有设置DJANGO_SETTINGS_MODULE，则使用本地开发配置
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

    try:
        # 导入Django的命令行执行函数
        # 这个函数负责解析命令行参数并执行相应的管理命令
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 如果导入失败，说明Django没有正确安装或虚拟环境没有激活
        raise ImportError(
            "无法导入Django。请确保Django已正确安装并且在PYTHONPATH环境变量中可用。"
            "您是否忘记激活虚拟环境了？"
        ) from exc

    # 执行命令行参数指定的管理命令
    # sys.argv包含了所有命令行参数，第一个是脚本名，后面是具体的命令和参数
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    # 当直接运行此脚本时，调用main函数
    # 这是Python脚本的标准入口点模式
    main()