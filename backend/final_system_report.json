{"timestamp": "2025-07-31T01:30:00.634235", "system_name": "SOIC Django Backend", "version": "1.0.0", "checks": {"project_structure": {"missing_dirs": [], "missing_files": [], "status": "ok"}, "dependencies": {"installed": ["Django", "drf-spectacular", "celery", "redis", "channels", "requests"], "missing": ["djangorestframework", "djangorestframework-simplejwt", "mysqlclient", "Pillow"], "status": "warning"}, "configuration": {"config/settings/base.py": {"exists": true, "size": 11600, "status": "ok"}, "config/settings/local.py": {"exists": true, "size": 2975, "status": "ok"}, "config/settings/minimal.py": {"exists": true, "size": 6189, "status": "ok"}, "config/settings/production.py": {"exists": true, "size": 4234, "status": "ok"}}, "business_domains": {"users": {"description": "用户认证系统", "path_exists": true, "missing_files": [], "status": "ok"}, "social": {"description": "社交功能", "path_exists": true, "missing_files": [], "status": "ok"}, "messaging": {"description": "消息系统", "path_exists": true, "missing_files": [], "status": "ok"}, "content": {"description": "内容管理", "path_exists": true, "missing_files": [], "status": "ok"}, "economy": {"description": "经济系统", "path_exists": true, "missing_files": [], "status": "ok"}, "core": {"description": "系统核心", "path_exists": true, "missing_files": [], "status": "ok"}}, "database": {"migration_dirs": [{"domain": "users", "migration_count": 1, "has_migrations": true}, {"domain": "social", "migration_count": 1, "has_migrations": true}, {"domain": "messaging", "migration_count": 1, "has_migrations": true}, {"domain": "content", "migration_count": 1, "has_migrations": true}, {"domain": "economy", "migration_count": 1, "has_migrations": true}], "status": "ok"}, "api_documentation": {"checks": {"drf_spectacular_installed": true, "spectacular_settings": true, "chinese_title": true, "chinese_tags": true, "swagger_ui_settings": true}, "status": "ok"}}, "readiness": {"readiness_score": 75.0, "status": "MOSTLY_READY", "total_checks": 4, "passed_checks": 3, "warnings": 1, "errors": 0}}