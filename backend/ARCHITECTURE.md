# SOIC 后端架构文档

## 项目概述

SOIC (Social Innovation Community) 是一个基于 Django 的现代化社交平台后端系统，采用领域驱动设计 (DDD) 和微服务架构思想，实现了高内聚、低耦合的模块化设计。

## 核心架构特点

### 1. 领域驱动设计 (DDD)
- **高内聚业务域**：每个应用专注于特定业务领域
- **清晰的边界上下文**：明确定义各域之间的交互接口
- **丰富的领域模型**：业务逻辑封装在领域对象中
- **事件驱动架构**：通过领域事件实现松耦合通信

### 2. 分层架构
```
┌─────────────────────────────────────┐
│           表现层 (Views)             │  HTTP请求处理、数据序列化
├─────────────────────────────────────┤
│          应用层 (Services)           │  业务流程编排、事务管理
├─────────────────────────────────────┤
│           领域层 (Models)            │  业务规则、领域逻辑
├─────────────────────────────────────┤
│          基础设施层 (Core)           │  数据持久化、外部服务
└─────────────────────────────────────┘
```

### 3. 微服务就绪
- **独立部署能力**：每个业务域可独立部署
- **API网关支持**：统一的路由和认证
- **服务发现**：通过依赖注入容器管理服务
- **异步通信**：基于事件总线的服务间通信

## 业务域划分

### 核心域 (apps/core/)
**职责**：提供基础设施和共享组件
- **基础模型**：BaseModel, AuditModel, StatusModel, MetadataModel
- **事件系统**：DomainEvent, EventBus, EventHandler
- **异常处理**：统一异常类型和处理机制
- **响应格式**：标准化API响应格式
- **依赖注入**：服务容器和依赖管理
- **权限系统**：基础权限类和装饰器

### 用户域 (apps/users/)
**职责**：用户身份认证和个人资料管理
- **用户管理**：注册、登录、个人资料
- **认证授权**：JWT令牌、权限验证
- **用户偏好**：个性化设置、通知偏好
- **安全功能**：密码重置、账户锁定

### 社交域 (apps/social/)
**职责**：社交关系和互动功能
- **好友系统**：好友申请、关系管理
- **关注系统**：关注/取消关注
- **群组管理**：群组创建、成员管理
- **空间系统**：个人/团队空间

### 消息域 (apps/messaging/)
**职责**：实时通信和消息传递
- **会话管理**：私聊、群聊、系统消息
- **消息传递**：文本、媒体、文件消息
- **在线状态**：用户在线状态管理
- **消息同步**：跨设备消息同步
- **WebSocket支持**：实时消息推送

### 内容域 (apps/content/)
**职责**：用户生成内容管理
- **帖子系统**：发布、编辑、删除帖子
- **评论系统**：多级评论、回复
- **内容审核**：自动/人工审核流程
- **推荐算法**：个性化内容推荐
- **搜索功能**：全文搜索、标签搜索

### 经济域 (apps/economy/)
**职责**：虚拟经济和交易系统
- **钱包系统**：多币种钱包管理
- **商品管理**：虚拟商品、订单处理
- **支付系统**：多种支付方式集成
- **交易记录**：完整的交易审计
- **优惠券系统**：营销工具支持

## 技术栈

### 后端框架
- **Django 4.2+**：Web框架
- **Django REST Framework**：API开发
- **Celery**：异步任务处理
- **Redis**：缓存和消息队列
- **PostgreSQL**：主数据库

### 开发工具
- **Docker**：容器化部署
- **pytest**：单元测试
- **Black**：代码格式化
- **flake8**：代码质量检查

## 核心设计模式

### 1. 服务层模式
每个业务域都有独立的服务层，封装复杂的业务逻辑：

```python
class UserService:
    @transaction.atomic
    def register_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        # 业务逻辑实现
        pass
```

### 2. 事件驱动模式
通过领域事件实现松耦合的服务间通信：

```python
# 发布事件
event = DomainEvent(
    event_type='user.registered',
    aggregate_id=str(user.uuid),
    data={'user_id': str(user.uuid)}
)
event_bus.publish(event)

# 处理事件
@event_handler('user.registered')
def handle_user_registered(event):
    # 处理逻辑
    pass
```

### 3. 依赖注入模式
通过容器管理服务依赖：

```python
# 注册服务
container.register_singleton(UserService, UserService)

# 使用服务
@inject
def some_function(user_service: UserService):
    return user_service.get_user(user_id)
```

### 4. 仓储模式
通过模型方法封装数据访问逻辑：

```python
class User(BaseModel):
    def activate(self):
        self.status = 'active'
        self.activated_at = timezone.now()
        self.save()
```

## 数据模型设计

### 基础模型继承体系
```python
BaseModel          # UUID主键、时间戳
├── AuditModel     # 创建者、更新者
├── StatusModel    # 状态管理
└── MetadataModel  # 元数据存储
```

### 关系设计原则
- **聚合根**：每个域有明确的聚合根实体
- **值对象**：使用JSON字段存储复杂值对象
- **外键约束**：合理使用外键和索引
- **软删除**：重要数据使用状态标记删除

## API设计规范

### RESTful API
- **资源导向**：URL表示资源，HTTP方法表示操作
- **统一响应格式**：标准化的成功/错误响应
- **版本控制**：通过URL路径进行版本管理
- **分页支持**：统一的分页参数和响应格式

### 响应格式示例
```json
{
    "success": true,
    "message": "操作成功",
    "data": {...},
    "pagination": {...},
    "timestamp": "2024-01-01T00:00:00Z"
}
```

## 安全设计

### 认证授权
- **JWT令牌**：无状态认证
- **权限系统**：基于角色的访问控制
- **API限流**：防止恶意请求
- **数据验证**：输入数据严格验证

### 数据保护
- **敏感数据加密**：密码、支付信息加密存储
- **SQL注入防护**：使用ORM参数化查询
- **XSS防护**：输出数据转义
- **CSRF防护**：跨站请求伪造防护

## 性能优化

### 数据库优化
- **索引策略**：合理创建数据库索引
- **查询优化**：使用select_related和prefetch_related
- **连接池**：数据库连接池管理
- **读写分离**：支持主从数据库配置

### 缓存策略
- **多层缓存**：Redis缓存 + 应用缓存
- **缓存失效**：基于事件的缓存失效
- **热点数据**：预热常用数据
- **缓存穿透防护**：空值缓存

### 异步处理
- **任务队列**：Celery异步任务处理
- **事件异步**：事件处理异步化
- **批量操作**：数据批量处理优化

## 监控和日志

### 日志系统
- **结构化日志**：JSON格式日志
- **日志级别**：DEBUG、INFO、WARNING、ERROR
- **业务日志**：关键业务操作记录
- **性能日志**：API响应时间监控

### 监控指标
- **系统指标**：CPU、内存、磁盘使用率
- **应用指标**：API响应时间、错误率
- **业务指标**：用户活跃度、交易量
- **告警机制**：异常情况自动告警

## 部署架构

### 容器化部署
```yaml
services:
  web:
    build: .
    ports:
      - "8000:8000"
  
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: soic
  
  redis:
    image: redis:7
  
  celery:
    build: .
    command: celery -A soic worker
```

### 微服务部署
- **服务拆分**：按业务域拆分服务
- **API网关**：统一入口和路由
- **服务发现**：动态服务注册发现
- **负载均衡**：多实例负载均衡

## 测试策略

### 测试金字塔
```
    ┌─────────────┐
    │   E2E Tests │  端到端测试
    ├─────────────┤
    │ Integration │  集成测试
    │    Tests    │
    ├─────────────┤
    │  Unit Tests │  单元测试
    └─────────────┘
```

### 测试覆盖
- **单元测试**：业务逻辑单元测试
- **集成测试**：API接口测试
- **性能测试**：负载和压力测试
- **安全测试**：安全漏洞扫描

## 开发规范

### 代码规范
- **PEP 8**：Python代码风格规范
- **类型提示**：使用类型注解
- **文档字符串**：完整的函数文档
- **代码审查**：Pull Request审查流程

### Git工作流
- **功能分支**：feature/xxx分支开发
- **代码审查**：合并前代码审查
- **自动化测试**：CI/CD流水线
- **版本标签**：语义化版本管理

## 扩展性设计

### 水平扩展
- **无状态设计**：应用服务无状态
- **数据库分片**：支持数据库水平分片
- **缓存集群**：Redis集群支持
- **消息队列集群**：Celery集群部署

### 功能扩展
- **插件系统**：支持功能插件扩展
- **主题系统**：支持多主题切换
- **多语言支持**：国际化和本地化
- **第三方集成**：开放API接口

## 总结

SOIC后端系统采用现代化的架构设计，具有以下优势：

1. **高可维护性**：清晰的分层和模块化设计
2. **高可扩展性**：支持水平扩展和功能扩展
3. **高可靠性**：完善的错误处理和监控机制
4. **高性能**：多层缓存和异步处理优化
5. **高安全性**：全面的安全防护措施

该架构为构建大规模社交平台提供了坚实的技术基础，支持快速迭代和持续演进。
