{"timestamp": "1753896297.4814224", "business_domains": {"users": {"status": "ok", "description": "用户认证系统", "path": "F:\\lib_code\\soic\\backend\\apps\\users", "models_count": 6}, "social": {"status": "ok", "description": "社交功能", "path": "F:\\lib_code\\soic\\backend\\apps\\social", "models_count": 8}, "messaging": {"status": "ok", "description": "消息系统", "path": "F:\\lib_code\\soic\\backend\\apps\\messaging", "models_count": 6}, "content": {"status": "ok", "description": "内容管理", "path": "F:\\lib_code\\soic\\backend\\apps\\content", "models_count": 8}, "economy": {"status": "ok", "description": "经济系统", "path": "F:\\lib_code\\soic\\backend\\apps\\economy", "models_count": 11}, "core": {"status": "ok", "description": "系统核心", "path": "F:\\lib_code\\soic\\backend\\apps\\core", "models_count": 0}}, "database_models": {"core": 0, "users": 6, "social": 8, "messaging": 6, "content": 8, "economy": 11}, "database_migrations": {"applied": 23, "unapplied": 0, "status": "ok"}, "api_endpoints": {"api_root": {"url": "/", "status_code": 400, "status": "error"}, "api_docs": {"url": "/api/docs/", "status_code": 400, "status": "error"}, "api_redoc": {"url": "/api/redoc/", "status_code": 400, "status": "error"}, "api_schema": {"url": "/api/schema/", "status_code": 400, "status": "error"}, "health_check": {"url": "/api/v1/core/health/", "status_code": 400, "status": "error"}, "admin": {"url": "/admin/", "status_code": 400, "status": "error"}}, "business_domain_urls": {"users": {"url": "/api/v1/auth/", "status_code": 400, "status": "warning"}, "social": {"url": "/api/v1/social/", "status_code": 400, "status": "warning"}, "messaging": {"url": "/api/v1/messaging/", "status_code": 400, "status": "warning"}, "content": {"url": "/api/v1/content/", "status_code": 400, "status": "warning"}, "economy": {"url": "/api/v1/economy/", "status_code": 400, "status": "warning"}, "core": {"url": "/api/v1/core/", "status_code": 400, "status": "warning"}}, "database_connection": {"status": "ok", "result": [1]}, "static_media_config": {"STATIC_URL": "/static/", "STATIC_ROOT": "F:\\lib_code\\soic\\backend\\staticfiles", "MEDIA_URL": "/media/", "MEDIA_ROOT": "F:\\lib_code\\soic\\backend\\media", "STATICFILES_DIRS": ["F:\\lib_code\\soic\\backend\\static"]}}