# SOIC 环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# Django基础配置
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置 - MySQL
DB_NAME=soic
DB_USER=root
DB_PASSWORD=xiaoxiao123
DB_HOST=localhost
DB_PORT=3306

# Redis配置
REDIS_URL=redis://127.0.0.1:6379/1

# Celery配置
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# 邮件配置
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=<EMAIL>

# 前端URL
FRONTEND_URL=http://localhost:3000

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 安全配置（生产环境）
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0

# 云存储配置（可选）
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_STORAGE_BUCKET_NAME=
# AWS_S3_REGION_NAME=us-east-1

# 监控配置（可选）
# SENTRY_DSN=

# 第三方服务配置
# STRIPE_PUBLIC_KEY=
# STRIPE_SECRET_KEY=
# ALIPAY_APP_ID=
# WECHAT_APP_ID=
