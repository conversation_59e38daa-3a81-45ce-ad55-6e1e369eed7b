"""
Celery配置文件 - 简化版本
"""

import os
from celery import Celery

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

# 创建Celery应用
app = Celery('soic')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务（暂时禁用以避免导入错误）
# app.autodiscover_tasks()

# 定时任务配置（暂时禁用）
# from celery.schedules import crontab

# app.conf.beat_schedule = {
#     # 定时任务将在后续添加
# }

app.conf.timezone = 'Asia/Shanghai'

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
