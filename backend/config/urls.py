"""
SOIC项目主URL配置
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from apps.core.views import APIInfoView

# 主URL模式
urlpatterns = [
    # 根路径 - API信息着陆页
    path('', APIInfoView.as_view(), name='api-root'),

    # Django管理后台
    path('admin/', admin.site.urls),

    # API文档
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # 业务域API路由
    path('api/v1/auth/', include('apps.users.urls')),
    path('api/v1/social/', include('apps.social.urls')),
    path('api/v1/messaging/', include('apps.messaging.urls')),
    path('api/v1/content/', include('apps.content.urls')),
    path('api/v1/economy/', include('apps.economy.urls')),

    # 核心API路由
    path('api/v1/core/', include('apps.core.urls')),
]

# 开发环境静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # Debug Toolbar
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns

# 自定义管理后台配置
admin.site.site_header = 'SOIC 管理后台'
admin.site.site_title = 'SOIC Admin'
admin.site.index_title = '欢迎使用 SOIC 管理系统'
