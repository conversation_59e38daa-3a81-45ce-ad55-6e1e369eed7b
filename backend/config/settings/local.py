"""
本地开发环境配置
"""

from .base import *

# 调试模式
DEBUG = True

# 允许的主机
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# 开发环境数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'soic',
        'USER': 'root',
        'PASSWORD': 'xiaoxiao123',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 开发环境邮件后端
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 开发环境日志配置
import os

# 确保日志目录存在
LOGS_DIR = BASE_DIR / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console'],  # 开发环境主要使用控制台输出
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# 开发环境CORS设置
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# 开发环境缓存（使用本地内存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 开发工具（暂时禁用debug_toolbar以避免依赖问题）
if DEBUG:
    INSTALLED_APPS += [
        # 'debug_toolbar',  # 暂时禁用
        # 'django_extensions',  # 暂时禁用
    ]

    # MIDDLEWARE += [
    #     'debug_toolbar.middleware.DebugToolbarMiddleware',
    # ]

    # Debug Toolbar配置（暂时禁用）
    # INTERNAL_IPS = [
    #     '127.0.0.1',
    #     'localhost',
    # ]

    # DEBUG_TOOLBAR_CONFIG = {
    #     'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
    # }

# 开发环境Celery配置（同步执行）
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# 静态文件开发服务器
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 媒体文件开发服务器
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
