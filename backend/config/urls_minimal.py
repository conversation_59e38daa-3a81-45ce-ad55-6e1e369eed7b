"""
简化的URL配置 - 用于快速启动
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse

def health_check(request):
    """简单的健康检查"""
    return JsonResponse({
        'status': 'ok',
        'message': 'SOIC Backend is running!',
        'version': '1.0.0'
    })

def api_info(request):
    """API信息"""
    return JsonResponse({
        'name': 'SOIC API',
        'version': '1.0.0',
        'description': 'Social Innovation Community API',
        'endpoints': {
            'admin': '/admin/',
            'health': '/health/',
            'api': '/api/v1/',
        }
    })

# 主URL模式
urlpatterns = [
    # Django管理后台
    path('admin/', admin.site.urls),
    
    # 健康检查
    path('health/', health_check, name='health-check'),
    path('', api_info, name='api-info'),
    
    # 用户认证API
    path('api/v1/auth/', include('apps.users.urls')),
    
    # 核心API
    path('api/v1/core/', include('apps.core.urls')),
]

# 开发环境静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 自定义管理后台配置
admin.site.site_header = 'SOIC 管理后台'
admin.site.site_title = 'SOIC Admin'
admin.site.index_title = '欢迎使用 SOIC 管理系统'
