# SOIC 环境变量配置

# Django基础配置
SECRET_KEY=django-insecure-soic-development-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置 - MySQL
DB_NAME=soic
DB_USER=root
DB_PASSWORD=xiaoxiao123
DB_HOST=localhost
DB_PORT=3306

# Redis配置（可选，开发环境使用内存缓存）
REDIS_URL=redis://127.0.0.1:6379/1

# Celery配置（可选）
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# 邮件配置（开发环境使用控制台输出）
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=<EMAIL>

# 前端URL
FRONTEND_URL=http://localhost:3000

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 安全配置（开发环境）
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
