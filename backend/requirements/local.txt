# 本地开发环境依赖

-r base.txt

# 开发工具
django-debug-toolbar==4.2.0
django-extensions==3.2.3

# 测试
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
pytest-mock==3.12.0
factory-boy==3.3.0

# 代码质量
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1
django-stubs==4.2.7

# 文档生成
Sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 性能测试
locust==2.17.0

# 数据库迁移工具
django-migration-testcase==1.0.0

# 调试工具
ipython==8.17.2
ipdb==0.13.13

# 环境管理
python-dotenv==1.0.0

# 代码覆盖率
coverage==7.3.2

# 安全检查
bandit==1.7.5
safety==2.3.5

# API测试
httpie==3.2.2
