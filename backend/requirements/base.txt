# 基础依赖包

# Django核心
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3

# 数据库
mysqlclient==2.2.0
django-redis==5.4.0

# 认证和权限
djangorestframework-simplejwt==5.3.0
PyJWT==2.8.0

# API文档
drf-spectacular==0.26.5

# 异步任务
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1
redis==5.0.1

# 配置管理
python-decouple==3.8

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 图像处理
Pillow==10.1.0

# HTTP客户端
requests==2.31.0

# 数据验证
marshmallow==3.20.1

# 加密
cryptography==41.0.7

# 工具库
python-slugify==8.0.1
shortuuid==1.0.11

# 邮件
django-anymail==10.2

# 文件处理
python-magic==0.4.27

# JSON处理
orjson==3.9.10

# 缓存
django-cachalot==2.6.1

# 监控
sentry-sdk==1.38.0

# 性能分析
django-silk==5.0.4
