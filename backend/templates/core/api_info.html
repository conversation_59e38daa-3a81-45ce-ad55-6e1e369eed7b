<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ api_info.name }} - API Information</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .status-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .nav-section {
            background: #f8f9fa;
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .quick-link {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .quick-link h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .endpoints-section {
            padding: 30px;
        }
        
        .endpoints-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .endpoint-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .endpoint-card h3 {
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .endpoint-url {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
        }
        
        .methods {
            margin: 10px 0;
        }
        
        .method-tag {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 5px;
        }
        
        .method-tag.get { background: #28a745; }
        .method-tag.post { background: #007bff; }
        .method-tag.put { background: #ffc107; color: #333; }
        .method-tag.delete { background: #dc3545; }
        
        .features {
            margin-top: 10px;
        }
        
        .feature-tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin: 2px;
        }
        
        .json-section {
            background: #f8f9fa;
            padding: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .json-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .quick-links {
                grid-template-columns: 1fr;
            }
            
            .endpoints-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ api_info.name }}</h1>
            <p>{{ api_info.description }}</p>
            <div class="status-badge">{{ api_info.status|title }}</div>
            <p style="margin-top: 10px; font-size: 1rem;">
                Version {{ api_info.version }} | Environment: {{ api_info.environment|title }}
            </p>
        </div>
        
        <div class="main-content">
            <div class="nav-section">
                <h2>🚀 快速导航</h2>
                <div class="quick-links">
                    <a href="{{ api_info.documentation.swagger }}" class="quick-link">
                        <h3>📚 API文档 (Swagger)</h3>
                        <p>交互式API文档，可以直接测试API端点</p>
                    </a>
                    
                    <a href="{{ api_info.documentation.redoc }}" class="quick-link">
                        <h3>📖 API文档 (ReDoc)</h3>
                        <p>美观的API文档阅读界面</p>
                    </a>
                    
                    <a href="{{ api_info.admin }}" class="quick-link">
                        <h3>⚙️ 管理后台</h3>
                        <p>系统管理和数据管理界面</p>
                    </a>
                    
                    <a href="{{ api_info.health_check }}" class="quick-link">
                        <h3>💚 健康检查</h3>
                        <p>检查系统运行状态和各组件健康状况</p>
                    </a>
                </div>
            </div>
            
            <div class="endpoints-section">
                <h2>🔗 API端点</h2>
                <div class="endpoints-grid">
                    {% for endpoint_name, endpoint_info in api_info.endpoints.items %}
                    <div class="endpoint-card">
                        <h3>{{ endpoint_info.description }}</h3>
                        <div class="endpoint-url">{{ endpoint_info.url }}</div>
                        
                        <div class="methods">
                            <strong>支持方法:</strong>
                            {% for method in endpoint_info.methods %}
                            <span class="method-tag {{ method|lower }}">{{ method }}</span>
                            {% endfor %}
                        </div>
                        
                        <div class="features">
                            <strong>功能特性:</strong><br>
                            {% for feature in endpoint_info.features %}
                            <span class="feature-tag">{{ feature }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="json-section">
                <h2>📄 JSON格式API信息</h2>
                <p>以下是API信息的JSON格式，可用于程序化访问：</p>
                <div class="json-container">
                    <pre>{{ api_info_json }}</pre>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 SOIC - Social Innovation Community. All rights reserved.</p>
            <p>Built with Django {{ api_info.django_version }} | Generated at {{ api_info.timestamp }}</p>
        </div>
    </div>
</body>
</html>
