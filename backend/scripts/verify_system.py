#!/usr/bin/env python
"""
SOIC系统验证脚本
验证系统启动状态，确保无错误无警告
"""

import os
import sys
import subprocess
import time
import urllib.request
import urllib.error
import json
from pathlib import Path

def run_command(command, description="", capture_output=True):
    """运行命令并返回结果"""
    print(f"🔍 {description or command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=capture_output,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print(f"✅ 成功")
            return True, result.stdout
        else:
            print(f"❌ 失败: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 超时")
        return False, "命令执行超时"
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False, str(e)

def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 9):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return False
    
    # 检查Django
    success, output = run_command("python -c \"import django; print(django.get_version())\"", "检查Django")
    if not success:
        return False
    
    # 检查数据库连接
    success, output = run_command("python manage.py check --database default", "检查数据库连接")
    if not success:
        return False
    
    return True

def check_system_configuration():
    """检查系统配置"""
    print("\n⚙️ 检查系统配置...")
    
    # 检查配置文件
    config_files = [
        '.env',
        'config/settings/minimal.py',
        'config/settings/local.py',
        'config/settings/production.py'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ 配置文件存在: {config_file}")
        else:
            print(f"❌ 配置文件缺失: {config_file}")
            return False
    
    # 检查必要目录
    directories = ['logs', 'media', 'staticfiles', 'static']
    for directory in directories:
        if Path(directory).exists():
            print(f"✅ 目录存在: {directory}")
        else:
            print(f"⚠️ 目录不存在，将创建: {directory}")
            Path(directory).mkdir(exist_ok=True)
    
    return True

def start_server_and_test():
    """启动服务器并测试"""
    print("\n🚀 启动服务器并测试...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = 'config.settings.minimal'
    env['PYTHONWARNINGS'] = 'ignore::RuntimeWarning'
    
    # 启动服务器
    print("启动Django开发服务器...")
    server_process = subprocess.Popen(
        [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8001', '--noreload'],
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(5)
    
    try:
        # 测试健康检查端点
        print("测试健康检查端点...")

        with urllib.request.urlopen('http://127.0.0.1:8001/health/', timeout=10) as response:
            if response.status == 200:
                health_data = json.loads(response.read().decode())
                print(f"✅ 健康检查通过: {health_data['status']}")

                # 检查各个组件状态
                checks = health_data.get('checks', {})
                for component, status in checks.items():
                    if status == 'ok' or (isinstance(status, dict) and all(v == 'ok' for v in status.values())):
                        print(f"✅ {component}: 正常")
                    else:
                        print(f"⚠️ {component}: {status}")

                success = True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status}")
                success = False

    except urllib.error.URLError as e:
        print(f"❌ 无法连接到服务器: {e}")
        success = False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        success = False
    
    finally:
        # 停止服务器
        print("停止服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
    
    return success

def check_startup_logs():
    """检查启动日志中的错误和警告"""
    print("\n📋 检查启动日志...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = 'config.settings.minimal'
    env['PYTHONWARNINGS'] = 'ignore::RuntimeWarning'
    
    # 运行Django检查
    result = subprocess.run(
        [sys.executable, 'manage.py', 'check', '--deploy'],
        env=env,
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print("✅ Django系统检查通过")
        if result.stdout.strip():
            print(f"输出: {result.stdout}")
    else:
        print(f"❌ Django系统检查失败: {result.stderr}")
        return False
    
    # 测试应用导入
    print("测试应用导入...")
    test_imports = [
        "from apps.users.models import User",
        "from apps.social.models import Friendship",
        "from apps.messaging.models import Conversation",
        "from apps.content.models import Post",
        "from apps.economy.models import Currency",
    ]
    
    for import_statement in test_imports:
        result = subprocess.run(
            [sys.executable, '-c', f'import django; django.setup(); {import_statement}'],
            env=env,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ 导入成功: {import_statement.split('import')[1].strip()}")
        else:
            print(f"❌ 导入失败: {import_statement}")
            print(f"错误: {result.stderr}")
            return False
    
    return True

def main():
    """主函数"""
    print("🔍 SOIC系统验证开始")
    print("=" * 50)
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    print(f"项目目录: {project_dir}")
    
    # 执行检查步骤
    checks = [
        ("依赖检查", check_dependencies),
        ("系统配置检查", check_system_configuration),
        ("启动日志检查", check_startup_logs),
        ("服务器测试", start_server_and_test),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        if not check_func():
            print(f"❌ {check_name}失败")
            all_passed = False
        else:
            print(f"✅ {check_name}通过")
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过！系统状态良好，无错误无警告！")
        print("✅ SOIC后端系统已准备就绪")
    else:
        print("❌ 部分检查失败，请查看上述错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
