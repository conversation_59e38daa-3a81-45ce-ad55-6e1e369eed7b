#!/usr/bin/env python
"""
SOIC项目设置脚本
自动化项目初始化过程
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n{'='*50}")
    print(f"执行: {description or command}")
    print(f"{'='*50}")
    
    try:
        if platform.system() == "Windows":
            result = subprocess.run(command, shell=True, check=True, 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=True, 
                                  capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stderr:
            print(f"错误详情: {e.stderr}")
        return False

def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 9):
        print("错误: 需要Python 3.9或更高版本")
        return False
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查pip
    try:
        import pip
        print(f"✓ pip已安装")
    except ImportError:
        print("错误: pip未安装")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    print("\n设置环境...")
    
    # 检查.env文件
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        print("复制环境变量配置文件...")
        import shutil
        shutil.copy(env_example, env_file)
        print("✓ 已创建.env文件，请根据需要修改配置")
    
    # 创建必要的目录
    directories = ['logs', 'media', 'staticfiles', 'static']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {directory}")

def install_dependencies():
    """安装依赖"""
    print("\n安装Python依赖...")
    
    # 检查是否在虚拟环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("警告: 建议在虚拟环境中运行")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            return False
    
    # 安装依赖
    requirements_file = "requirements/local.txt"
    if not Path(requirements_file).exists():
        requirements_file = "requirements.txt"
    
    if Path(requirements_file).exists():
        return run_command(f"pip install -r {requirements_file}", "安装Python依赖")
    else:
        print("错误: 找不到requirements文件")
        return False

def setup_database():
    """设置数据库"""
    print("\n设置数据库...")
    
    # 运行迁移
    if not run_command("python manage.py makemigrations", "创建数据库迁移"):
        return False
    
    if not run_command("python manage.py migrate", "执行数据库迁移"):
        return False
    
    # 初始化数据
    if not run_command("python manage.py init_data", "初始化基础数据"):
        print("警告: 初始化基础数据失败，可能需要手动执行")
    
    return True

def create_superuser():
    """创建超级用户"""
    print("\n创建超级用户...")
    
    print("请输入超级用户信息:")
    username = input("用户名 (默认: admin): ") or "admin"
    email = input("邮箱 (默认: <EMAIL>): ") or "<EMAIL>"
    
    # 使用环境变量传递信息，避免交互式输入
    env = os.environ.copy()
    env.update({
        'DJANGO_SUPERUSER_USERNAME': username,
        'DJANGO_SUPERUSER_EMAIL': email,
        'DJANGO_SUPERUSER_PASSWORD': 'admin123'  # 默认密码
    })
    
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'createsuperuser', '--noinput'
        ], env=env, check=True)
        print(f"✓ 超级用户创建成功")
        print(f"  用户名: {username}")
        print(f"  邮箱: {email}")
        print(f"  密码: admin123 (请登录后修改)")
        return True
    except subprocess.CalledProcessError:
        print("超级用户可能已存在或创建失败")
        return True  # 不阻止继续执行

def collect_static():
    """收集静态文件"""
    print("\n收集静态文件...")
    return run_command("python manage.py collectstatic --noinput", "收集静态文件")

def main():
    """主函数"""
    print("SOIC项目自动设置脚本")
    print("="*50)
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    print(f"项目目录: {project_dir}")
    
    # 执行设置步骤
    steps = [
        ("检查系统要求", check_requirements),
        ("设置环境", setup_environment),
        ("安装依赖", install_dependencies),
        ("设置数据库", setup_database),
        ("创建超级用户", create_superuser),
        ("收集静态文件", collect_static),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"错误: {step_name}失败")
            sys.exit(1)
    
    print("\n" + "="*50)
    print("🎉 项目设置完成!")
    print("="*50)
    print("\n下一步:")
    print("1. 检查并修改 .env 文件中的配置")
    print("2. 确保MySQL数据库服务正在运行")
    print("3. 运行开发服务器: python manage.py runserver")
    print("4. 访问 http://localhost:8000/admin/ 登录管理后台")
    print("5. 访问 http://localhost:8000/api/docs/ 查看API文档")
    print("\n管理员账户:")
    print("  用户名: admin")
    print("  密码: admin123")
    print("\n注意: 请在生产环境中修改默认密码!")

if __name__ == "__main__":
    main()
