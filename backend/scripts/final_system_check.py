#!/usr/bin/env python
"""
SOIC Django后端系统最终完整性检查报告
生成系统就绪状态的综合评估
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from datetime import datetime

def check_project_structure():
    """检查项目结构完整性"""
    print("🔍 检查项目结构...")
    
    base_dir = Path(__file__).parent.parent
    required_dirs = [
        'apps/users',
        'apps/social', 
        'apps/messaging',
        'apps/content',
        'apps/economy',
        'apps/core',
        'config/settings',
        'static',
        'media',
        'locale',
        'scripts',
    ]
    
    required_files = [
        'manage.py',
        'requirements.txt',
        'config/__init__.py',
        'config/urls.py',
        'config/wsgi.py',
        'config/asgi.py',
        'config/celery.py',
    ]
    
    missing_dirs = []
    missing_files = []
    
    for dir_path in required_dirs:
        if not (base_dir / dir_path).exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✅ {dir_path}")
    
    for file_path in required_files:
        if not (base_dir / file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    return {
        'missing_dirs': missing_dirs,
        'missing_files': missing_files,
        'status': 'ok' if not missing_dirs and not missing_files else 'error'
    }

def check_dependencies_status():
    """检查依赖包状态"""
    print("\n🔍 检查关键依赖包...")
    
    critical_packages = [
        'Django',
        'djangorestframework', 
        'drf-spectacular',
        'djangorestframework-simplejwt',
        'celery',
        'redis',
        'mysqlclient',
        'channels',
        'Pillow',
        'requests',
    ]
    
    installed = []
    missing = []
    
    for package in critical_packages:
        try:
            result = subprocess.run([sys.executable, '-c', f'import {package.lower().replace("-", "_")}'], 
                                  capture_output=True)
            if result.returncode == 0:
                installed.append(package)
                print(f"✅ {package}")
            else:
                missing.append(package)
                print(f"❌ {package}")
        except:
            missing.append(package)
            print(f"❌ {package}")
    
    return {
        'installed': installed,
        'missing': missing,
        'status': 'ok' if not missing else 'warning'
    }

def check_configuration_files():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    base_dir = Path(__file__).parent.parent
    config_files = [
        'config/settings/base.py',
        'config/settings/local.py', 
        'config/settings/minimal.py',
        'config/settings/production.py',
    ]
    
    results = {}
    
    for config_file in config_files:
        file_path = base_dir / config_file
        if file_path.exists():
            # 检查文件大小，确保不是空文件
            size = file_path.stat().st_size
            results[config_file] = {
                'exists': True,
                'size': size,
                'status': 'ok' if size > 100 else 'warning'
            }
            print(f"✅ {config_file} ({size} bytes)")
        else:
            results[config_file] = {
                'exists': False,
                'status': 'error'
            }
            print(f"❌ {config_file}")
    
    return results

def check_business_domains():
    """检查业务域完整性"""
    print("\n🔍 检查业务域...")
    
    base_dir = Path(__file__).parent.parent
    domains = {
        'users': '用户认证系统',
        'social': '社交功能',
        'messaging': '消息系统', 
        'content': '内容管理',
        'economy': '经济系统',
        'core': '系统核心'
    }
    
    results = {}
    
    for domain, description in domains.items():
        domain_path = base_dir / f'apps/{domain}'
        required_files = ['__init__.py', 'models.py', 'views.py', 'urls.py', 'apps.py']
        
        if domain_path.exists():
            missing_files = []
            for file_name in required_files:
                if not (domain_path / file_name).exists():
                    missing_files.append(file_name)
            
            results[domain] = {
                'description': description,
                'path_exists': True,
                'missing_files': missing_files,
                'status': 'ok' if not missing_files else 'warning'
            }
            
            status_icon = "✅" if not missing_files else "⚠️"
            print(f"{status_icon} {domain} ({description})")
            
            if missing_files:
                print(f"   缺失文件: {', '.join(missing_files)}")
        else:
            results[domain] = {
                'description': description,
                'path_exists': False,
                'status': 'error'
            }
            print(f"❌ {domain} ({description}) - 目录不存在")
    
    return results

def check_database_readiness():
    """检查数据库就绪状态"""
    print("\n🔍 检查数据库就绪状态...")
    
    try:
        # 检查迁移文件
        base_dir = Path(__file__).parent.parent
        migration_dirs = []
        
        for domain in ['users', 'social', 'messaging', 'content', 'economy', 'core']:
            migration_path = base_dir / f'apps/{domain}/migrations'
            if migration_path.exists():
                migration_files = list(migration_path.glob('*.py'))
                migration_files = [f for f in migration_files if f.name != '__init__.py']
                migration_dirs.append({
                    'domain': domain,
                    'migration_count': len(migration_files),
                    'has_migrations': len(migration_files) > 0
                })
                print(f"✅ {domain}: {len(migration_files)} 个迁移文件")
        
        return {
            'migration_dirs': migration_dirs,
            'status': 'ok'
        }
        
    except Exception as e:
        print(f"❌ 检查数据库迁移失败: {e}")
        return {'status': 'error', 'error': str(e)}

def check_api_documentation():
    """检查API文档配置"""
    print("\n🔍 检查API文档配置...")
    
    base_dir = Path(__file__).parent.parent
    
    # 检查DRF Spectacular配置
    settings_file = base_dir / 'config/settings/base.py'
    
    if settings_file.exists():
        content = settings_file.read_text(encoding='utf-8')
        
        checks = {
            'drf_spectacular_installed': 'drf_spectacular' in content,
            'spectacular_settings': 'SPECTACULAR_SETTINGS' in content,
            'chinese_title': 'SOIC API - 社交创新社区' in content,
            'chinese_tags': '用户认证' in content,
            'swagger_ui_settings': 'SWAGGER_UI_SETTINGS' in content,
        }
        
        for check, result in checks.items():
            status_icon = "✅" if result else "❌"
            print(f"{status_icon} {check}: {result}")
        
        return {
            'checks': checks,
            'status': 'ok' if all(checks.values()) else 'warning'
        }
    else:
        print("❌ 配置文件不存在")
        return {'status': 'error'}

def generate_final_report():
    """生成最终检查报告"""
    print("\n📋 生成最终检查报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'system_name': 'SOIC Django Backend',
        'version': '1.0.0',
        'checks': {
            'project_structure': check_project_structure(),
            'dependencies': check_dependencies_status(),
            'configuration': check_configuration_files(),
            'business_domains': check_business_domains(),
            'database': check_database_readiness(),
            'api_documentation': check_api_documentation(),
        }
    }
    
    # 保存报告
    report_file = Path(__file__).parent.parent / 'final_system_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 最终检查报告已生成: {report_file}")
    return report

def analyze_system_readiness(report):
    """分析系统就绪状态"""
    print("\n" + "="*60)
    print("📊 SOIC Django后端系统就绪状态分析")
    print("="*60)
    
    total_checks = 0
    passed_checks = 0
    warnings = 0
    errors = 0
    
    for category, result in report['checks'].items():
        if isinstance(result, dict) and 'status' in result:
            total_checks += 1
            if result['status'] == 'ok':
                passed_checks += 1
                print(f"✅ {category}: 正常")
            elif result['status'] == 'warning':
                warnings += 1
                print(f"⚠️ {category}: 有警告")
            else:
                errors += 1
                print(f"❌ {category}: 有错误")
    
    print(f"\n📈 统计结果:")
    print(f"   总检查项: {total_checks}")
    print(f"   通过: {passed_checks}")
    print(f"   警告: {warnings}")
    print(f"   错误: {errors}")
    
    # 计算就绪度
    readiness_score = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"\n🎯 系统就绪度: {readiness_score:.1f}%")
    
    if readiness_score >= 90:
        print("🎉 系统已完全就绪，可以投入生产使用！")
        status = "READY"
    elif readiness_score >= 75:
        print("✅ 系统基本就绪，建议解决警告后投入使用。")
        status = "MOSTLY_READY"
    elif readiness_score >= 50:
        print("⚠️ 系统部分就绪，需要解决关键问题后才能使用。")
        status = "PARTIALLY_READY"
    else:
        print("❌ 系统未就绪，需要解决重大问题。")
        status = "NOT_READY"
    
    return {
        'readiness_score': readiness_score,
        'status': status,
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'warnings': warnings,
        'errors': errors
    }

def main():
    """主函数"""
    print("🔍 SOIC Django后端系统最终完整性检查")
    print("=" * 60)
    
    try:
        # 生成检查报告
        report = generate_final_report()
        
        # 分析系统就绪状态
        readiness = analyze_system_readiness(report)
        
        # 添加就绪状态到报告
        report['readiness'] = readiness
        
        # 更新报告文件
        report_file = Path(__file__).parent.parent / 'final_system_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 完整报告已保存: {report_file}")
        
        return readiness['status'] in ['READY', 'MOSTLY_READY']
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
