#!/bin/bash
# SOIC开发环境启动脚本 (Linux/Mac)

echo "========================================"
echo "SOIC 开发环境启动脚本"
echo "========================================"

# 检查虚拟环境
if [ ! -f "venv/bin/activate" ]; then
    echo "错误: 找不到虚拟环境，请先运行 setup.sh"
    exit 1
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "警告: 找不到.env文件，使用默认配置"
    cp .env.example .env
fi

# 设置Django设置模块
export DJANGO_SETTINGS_MODULE=config.settings.local

# 运行数据库迁移
echo "检查数据库迁移..."
python manage.py migrate

# 启动开发服务器
echo "启动Django开发服务器..."
echo "服务器地址: http://localhost:8000"
echo "管理后台: http://localhost:8000/admin/"
echo "API文档: http://localhost:8000/api/docs/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "========================================"

python manage.py runserver 0.0.0.0:8000
