@echo off
REM SOIC生产环境启动脚本 (Windows)

echo ========================================
echo SOIC 生产环境启动脚本
echo ========================================

REM 激活conda环境
echo 激活conda环境: soic
call conda activate soic

REM 设置生产环境配置
set DJANGO_SETTINGS_MODULE=config.settings.production

REM 检查环境变量文件
if not exist ".env" (
    echo 错误: 找不到.env文件，请先配置生产环境变量
    pause
    exit /b 1
)

REM 运行数据库迁移
echo 运行数据库迁移...
C:\Users\<USER>\.conda\envs\soic\python.exe manage.py migrate
if errorlevel 1 (
    echo 错误: 数据库迁移失败
    pause
    exit /b 1
)

REM 收集静态文件
echo 收集静态文件...
C:\Users\<USER>\.conda\envs\soic\python.exe manage.py collectstatic --noinput
if errorlevel 1 (
    echo 警告: 收集静态文件失败
)

REM 启动Gunicorn服务器
echo 启动Gunicorn生产服务器...
echo 服务器地址: http://localhost:8000
echo 配置文件: gunicorn.conf.py
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================

C:\Users\<USER>\.conda\envs\soic\python.exe -m gunicorn config.wsgi:application -c gunicorn.conf.py

pause
