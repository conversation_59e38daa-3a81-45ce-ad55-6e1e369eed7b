#!/usr/bin/env python
"""
依赖包检查脚本
检查requirements.txt中的依赖包版本兼容性和完整性
"""

import subprocess
import sys
import pkg_resources
from pathlib import Path

def check_installed_packages():
    """检查已安装的包"""
    print("🔍 检查已安装的包...")
    
    requirements_file = Path(__file__).parent.parent / 'requirements.txt'
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = f.read().splitlines()
    
    # 过滤掉注释和空行
    packages = []
    for line in requirements:
        line = line.strip()
        if line and not line.startswith('#'):
            packages.append(line)
    
    print(f"📦 总共 {len(packages)} 个依赖包")
    
    missing_packages = []
    outdated_packages = []
    
    for package in packages:
        try:
            # 解析包名和版本
            if '==' in package:
                pkg_name, version = package.split('==')
            else:
                pkg_name = package
                version = None
            
            # 检查是否已安装
            try:
                installed_version = pkg_resources.get_distribution(pkg_name).version
                if version and installed_version != version:
                    outdated_packages.append(f"{pkg_name}: 需要 {version}, 已安装 {installed_version}")
                else:
                    print(f"✅ {pkg_name}: {installed_version}")
            except pkg_resources.DistributionNotFound:
                missing_packages.append(pkg_name)
                print(f"❌ {pkg_name}: 未安装")
                
        except Exception as e:
            print(f"⚠️ 解析包 {package} 时出错: {e}")
    
    # 报告结果
    if missing_packages:
        print(f"\n❌ 缺失的包 ({len(missing_packages)}):")
        for pkg in missing_packages:
            print(f"  - {pkg}")
    
    if outdated_packages:
        print(f"\n⚠️ 版本不匹配的包 ({len(outdated_packages)}):")
        for pkg in outdated_packages:
            print(f"  - {pkg}")
    
    if not missing_packages and not outdated_packages:
        print("\n🎉 所有依赖包都已正确安装！")
        return True
    
    return False

def check_critical_dependencies():
    """检查关键依赖包"""
    print("\n🔍 检查关键依赖包...")
    
    critical_packages = {
        'Django': '核心框架',
        'djangorestframework': 'REST API框架',
        'mysqlclient': 'MySQL数据库驱动',
        'redis': 'Redis缓存',
        'celery': '异步任务队列',
        'channels': 'WebSocket支持',
        'drf-spectacular': 'API文档生成',
        'djangorestframework-simplejwt': 'JWT认证',
        'Pillow': '图像处理',
        'requests': 'HTTP客户端',
        'cryptography': '加密库',
        'pytest': '测试框架',
    }
    
    missing_critical = []
    
    for package, description in critical_packages.items():
        try:
            version = pkg_resources.get_distribution(package).version
            print(f"✅ {package} ({description}): {version}")
        except pkg_resources.DistributionNotFound:
            missing_critical.append(f"{package} ({description})")
            print(f"❌ {package} ({description}): 未安装")
    
    if missing_critical:
        print(f"\n❌ 缺失的关键依赖 ({len(missing_critical)}):")
        for pkg in missing_critical:
            print(f"  - {pkg}")
        return False
    
    print("\n🎉 所有关键依赖都已安装！")
    return True

def check_version_conflicts():
    """检查版本冲突"""
    print("\n🔍 检查版本冲突...")
    
    try:
        # 使用pip check命令检查依赖冲突
        result = subprocess.run([sys.executable, '-m', 'pip', 'check'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 没有发现版本冲突")
            return True
        else:
            print("❌ 发现版本冲突:")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"⚠️ 检查版本冲突时出错: {e}")
        return False

def check_security_vulnerabilities():
    """检查安全漏洞"""
    print("\n🔍 检查安全漏洞...")
    
    try:
        # 使用pip-audit检查安全漏洞（如果安装了的话）
        result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--format=json'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包列表获取成功")
            # 这里可以集成更多的安全检查工具
            return True
        else:
            print("⚠️ 无法获取依赖包列表")
            return False
            
    except Exception as e:
        print(f"⚠️ 检查安全漏洞时出错: {e}")
        return False

def generate_requirements_report():
    """生成依赖报告"""
    print("\n📋 生成依赖报告...")
    
    try:
        # 生成当前环境的requirements
        result = subprocess.run([sys.executable, '-m', 'pip', 'freeze'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            report_file = Path(__file__).parent.parent / 'requirements_current.txt'
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 当前环境的依赖包版本\n")
                f.write("# 生成时间: " + str(Path(__file__).stat().st_mtime) + "\n\n")
                f.write(result.stdout)
            
            print(f"✅ 依赖报告已生成: {report_file}")
            return True
        else:
            print("❌ 生成依赖报告失败")
            return False
            
    except Exception as e:
        print(f"⚠️ 生成依赖报告时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔍 SOIC 依赖包检查工具")
    print("=" * 50)
    
    checks = [
        ("检查已安装包", check_installed_packages),
        ("检查关键依赖", check_critical_dependencies),
        ("检查版本冲突", check_version_conflicts),
        ("检查安全漏洞", check_security_vulnerabilities),
        ("生成依赖报告", generate_requirements_report),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}失败: {e}")
            results.append((check_name, False))
    
    # 总结报告
    print("\n" + "="*50)
    print("📊 检查结果总结")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查都通过！依赖环境状态良好。")
        return True
    else:
        print("⚠️ 部分检查未通过，请查看上述详细信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
