@echo off
REM SOIC开发环境无警告启动脚本 (Windows)

echo ========================================
echo SOIC 开发环境启动脚本 (无警告版本)
echo ========================================

REM 激活conda环境
echo 激活conda环境: soic
call conda activate soic

REM 设置开发环境配置
set DJANGO_SETTINGS_MODULE=config.settings.minimal

REM 设置环境变量以隐藏开发服务器警告
set PYTHONWARNINGS=ignore::RuntimeWarning

REM 检查环境变量文件
if not exist ".env" (
    echo 创建.env文件...
    copy .env.example .env
)

REM 检查数据库连接
echo 检查系统状态...
C:\Users\<USER>\.conda\envs\soic\python.exe manage.py check --deploy
if errorlevel 1 (
    echo 警告: 系统检查发现问题，但继续启动
)

echo ========================================
echo 启动Django开发服务器 (无警告模式)
echo ========================================
echo 服务器地址: http://localhost:8000
echo 管理后台: http://localhost:8000/admin/
echo 健康检查: http://localhost:8000/health/
echo API信息: http://localhost:8000/
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================

C:\Users\<USER>\.conda\envs\soic\python.exe manage.py runserver 0.0.0.0:8000 --noreload

pause
