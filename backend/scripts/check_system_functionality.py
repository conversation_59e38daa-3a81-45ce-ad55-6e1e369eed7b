#!/usr/bin/env python
"""
系统功能完整性检查脚本
验证所有业务域的核心功能是否完整实现
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.minimal')
django.setup()

from django.apps import apps
from django.urls import reverse, NoReverseMatch
from django.core.management import call_command
from django.db import connection
from django.test import Client
import json

def check_business_domains():
    """检查业务域应用"""
    print("🔍 检查业务域应用...")
    
    expected_apps = {
        'users': '用户认证系统',
        'social': '社交功能',
        'messaging': '消息系统',
        'content': '内容管理',
        'economy': '经济系统',
        'core': '系统核心'
    }
    
    results = {}
    
    for app_label, description in expected_apps.items():
        try:
            app_config = apps.get_app_config(app_label)
            models = list(app_config.get_models())
            results[app_label] = {
                'status': 'ok',
                'description': description,
                'path': app_config.path,
                'models_count': len(models)
            }
            print(f"✅ {app_label} ({description}): {len(models)} 个模型")
        except Exception as e:
            results[app_label] = {
                'status': 'error',
                'description': description,
                'error': str(e)
            }
            print(f"❌ {app_label} ({description}): {e}")
    
    return results

def check_database_models():
    """检查数据库模型"""
    print("\n🔍 检查数据库模型...")
    
    model_counts = {}
    total_models = 0
    
    for app_config in apps.get_app_configs():
        if app_config.label in ['users', 'social', 'messaging', 'content', 'economy', 'core']:
            models = list(app_config.get_models())
            model_counts[app_config.label] = len(models)
            total_models += len(models)
            
            print(f"📊 {app_config.label}: {len(models)} 个模型")
            for model in models:
                print(f"   - {model.__name__}")
    
    print(f"\n📈 总计: {total_models} 个业务模型")
    return model_counts

def check_database_migrations():
    """检查数据库迁移"""
    print("\n🔍 检查数据库迁移...")
    
    try:
        # 检查迁移状态
        from django.core.management.commands.showmigrations import Command
        from io import StringIO
        
        output = StringIO()
        call_command('showmigrations', stdout=output)
        migrations_output = output.getvalue()
        
        # 分析迁移状态
        applied_count = migrations_output.count('[X]')
        unapplied_count = migrations_output.count('[ ]')
        
        print(f"✅ 已应用迁移: {applied_count}")
        print(f"⏳ 未应用迁移: {unapplied_count}")
        
        if unapplied_count > 0:
            print("⚠️ 有未应用的迁移，建议运行 python manage.py migrate")
        
        return {
            'applied': applied_count,
            'unapplied': unapplied_count,
            'status': 'ok' if unapplied_count == 0 else 'warning'
        }
        
    except Exception as e:
        print(f"❌ 检查迁移失败: {e}")
        return {'status': 'error', 'error': str(e)}

def check_api_endpoints():
    """检查API端点"""
    print("\n🔍 检查API端点...")
    
    client = Client()
    endpoints = {
        'api_root': '/',
        'api_docs': '/api/docs/',
        'api_redoc': '/api/redoc/',
        'api_schema': '/api/schema/',
        'health_check': '/api/v1/core/health/',
        'admin': '/admin/',
    }
    
    results = {}
    
    for name, url in endpoints.items():
        try:
            response = client.get(url)
            status = 'ok' if response.status_code < 400 else 'error'
            results[name] = {
                'url': url,
                'status_code': response.status_code,
                'status': status
            }
            
            status_icon = "✅" if status == 'ok' else "❌"
            print(f"{status_icon} {name}: {url} -> {response.status_code}")
            
        except Exception as e:
            results[name] = {
                'url': url,
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ {name}: {url} -> 错误: {e}")
    
    return results

def check_business_domain_urls():
    """检查业务域URL"""
    print("\n🔍 检查业务域URL...")
    
    client = Client()
    domain_urls = {
        'users': '/api/v1/auth/',
        'social': '/api/v1/social/',
        'messaging': '/api/v1/messaging/',
        'content': '/api/v1/content/',
        'economy': '/api/v1/economy/',
        'core': '/api/v1/core/',
    }
    
    results = {}
    
    for domain, base_url in domain_urls.items():
        try:
            response = client.get(base_url)
            # 对于API端点，404可能是正常的（如果没有根视图）
            # 200, 301, 302, 405 都是可接受的状态
            acceptable_codes = [200, 301, 302, 405]
            status = 'ok' if response.status_code in acceptable_codes else 'warning'
            
            results[domain] = {
                'url': base_url,
                'status_code': response.status_code,
                'status': status
            }
            
            if status == 'ok':
                print(f"✅ {domain}: {base_url} -> {response.status_code}")
            else:
                print(f"⚠️ {domain}: {base_url} -> {response.status_code}")
                
        except Exception as e:
            results[domain] = {
                'url': base_url,
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ {domain}: {base_url} -> 错误: {e}")
    
    return results

def check_database_connection():
    """检查数据库连接"""
    print("\n🔍 检查数据库连接...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        print("✅ 数据库连接正常")
        return {'status': 'ok', 'result': result}
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return {'status': 'error', 'error': str(e)}

def check_static_and_media_config():
    """检查静态文件和媒体文件配置"""
    print("\n🔍 检查静态文件和媒体文件配置...")
    
    from django.conf import settings
    
    configs = {
        'STATIC_URL': getattr(settings, 'STATIC_URL', None),
        'STATIC_ROOT': getattr(settings, 'STATIC_ROOT', None),
        'MEDIA_URL': getattr(settings, 'MEDIA_URL', None),
        'MEDIA_ROOT': getattr(settings, 'MEDIA_ROOT', None),
        'STATICFILES_DIRS': getattr(settings, 'STATICFILES_DIRS', []),
    }
    
    for key, value in configs.items():
        if value:
            print(f"✅ {key}: {value}")
        else:
            print(f"⚠️ {key}: 未配置")
    
    return configs

def generate_functionality_report():
    """生成功能完整性报告"""
    print("\n📋 生成功能完整性报告...")
    
    report = {
        'timestamp': str(Path(__file__).stat().st_mtime),
        'business_domains': check_business_domains(),
        'database_models': check_database_models(),
        'database_migrations': check_database_migrations(),
        'api_endpoints': check_api_endpoints(),
        'business_domain_urls': check_business_domain_urls(),
        'database_connection': check_database_connection(),
        'static_media_config': check_static_and_media_config(),
    }
    
    # 保存报告
    report_file = Path(__file__).parent.parent / 'functionality_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 功能完整性报告已生成: {report_file}")
    return report

def main():
    """主函数"""
    print("🔍 SOIC 系统功能完整性检查")
    print("=" * 60)
    
    try:
        report = generate_functionality_report()
        
        # 分析结果
        print("\n" + "="*60)
        print("📊 检查结果总结")
        print("="*60)
        
        # 统计各项检查结果
        total_checks = 0
        passed_checks = 0
        
        # 业务域检查
        domain_ok = sum(1 for d in report['business_domains'].values() if d['status'] == 'ok')
        domain_total = len(report['business_domains'])
        print(f"业务域应用: {domain_ok}/{domain_total} 正常")
        total_checks += domain_total
        passed_checks += domain_ok
        
        # API端点检查
        api_ok = sum(1 for e in report['api_endpoints'].values() if e['status'] == 'ok')
        api_total = len(report['api_endpoints'])
        print(f"API端点: {api_ok}/{api_total} 正常")
        total_checks += api_total
        passed_checks += api_ok
        
        # 数据库检查
        db_status = report['database_connection']['status']
        print(f"数据库连接: {'✅' if db_status == 'ok' else '❌'}")
        total_checks += 1
        if db_status == 'ok':
            passed_checks += 1
        
        # 迁移检查
        migration_status = report['database_migrations']['status']
        print(f"数据库迁移: {'✅' if migration_status == 'ok' else '⚠️'}")
        total_checks += 1
        if migration_status == 'ok':
            passed_checks += 1
        
        print(f"\n总体结果: {passed_checks}/{total_checks} 项检查通过")
        
        if passed_checks == total_checks:
            print("🎉 所有功能检查都通过！系统功能完整。")
            return True
        else:
            print("⚠️ 部分功能检查未通过，请查看详细报告。")
            return False
            
    except Exception as e:
        print(f"❌ 功能检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
