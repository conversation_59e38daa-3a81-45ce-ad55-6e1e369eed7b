"""
空间数据序列化器 - 数据转换和验证
职责：处理空间相关API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import (
    Space, SpaceCategory, SpaceMember, SpaceScene, SpaceObject,
    SpaceInvitation, SpaceLike, SpaceVisit
)


class SpaceCategorySerializer(serializers.ModelSerializer):
    """空间分类序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    space_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SpaceCategory
        fields = [
            'id', 'name', 'description', 'icon_url', 'color',
            'sort_order', 'is_active', 'space_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'space_count', 'created_at', 'updated_at']
    
    def get_space_count(self, obj):
        """获取分类下的空间数量"""
        return obj.spaces.filter(status='active').count()


class SpaceCreateSerializer(serializers.Serializer):
    """空间创建序列化器"""
    
    name = serializers.CharField(
        max_length=100,
        min_length=2,
        help_text="空间名称，2-100个字符"
    )
    description = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="空间描述，最多1000个字符"
    )
    category_id = serializers.CharField(
        required=False,
        help_text="空间分类ID"
    )
    space_type = serializers.ChoiceField(
        choices=Space.SPACE_TYPE_CHOICES,
        default='social',
        help_text="空间类型"
    )
    visibility = serializers.ChoiceField(
        choices=Space.VISIBILITY_CHOICES,
        default='public',
        help_text="可见性"
    )
    max_capacity = serializers.IntegerField(
        min_value=1,
        max_value=1000,
        default=50,
        help_text="最大容量，1-1000"
    )
    
    # 空间设置
    allow_voice_chat = serializers.BooleanField(default=True)
    allow_video_chat = serializers.BooleanField(default=True)
    allow_screen_share = serializers.BooleanField(default=True)
    allow_file_upload = serializers.BooleanField(default=True)
    allow_guest_access = serializers.BooleanField(default=True)
    
    # 密码保护
    password = serializers.CharField(
        max_length=128,
        required=False,
        allow_blank=True,
        help_text="访问密码，可选"
    )
    
    # 媒体资源
    thumbnail_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="缩略图URL"
    )
    background_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="背景图URL"
    )
    
    def validate_name(self, value):
        """验证空间名称"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("空间名称不能为空")
        
        # 检查是否包含敏感词
        sensitive_words = ['admin', 'system', 'test']
        if any(word in value.lower() for word in sensitive_words):
            raise serializers.ValidationError("空间名称包含敏感词")
        
        return value.strip()
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                SpaceCategory.objects.get(uuid=value, is_active=True)
            except SpaceCategory.DoesNotExist:
                raise serializers.ValidationError("分类不存在或已禁用")
        
        return value
    
    def validate(self, attrs):
        """交叉验证"""
        # 私有空间必须设置密码
        if attrs.get('visibility') == 'private' and not attrs.get('password'):
            raise serializers.ValidationError({
                'password': '私有空间必须设置访问密码'
            })
        
        return attrs


class SpaceUpdateSerializer(serializers.Serializer):
    """空间更新序列化器"""
    
    name = serializers.CharField(
        max_length=100,
        min_length=2,
        required=False,
        help_text="空间名称"
    )
    description = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="空间描述"
    )
    category_id = serializers.CharField(
        required=False,
        help_text="空间分类ID"
    )
    visibility = serializers.ChoiceField(
        choices=Space.VISIBILITY_CHOICES,
        required=False,
        help_text="可见性"
    )
    max_capacity = serializers.IntegerField(
        min_value=1,
        max_value=1000,
        required=False,
        help_text="最大容量"
    )
    
    # 空间设置
    allow_voice_chat = serializers.BooleanField(required=False)
    allow_video_chat = serializers.BooleanField(required=False)
    allow_screen_share = serializers.BooleanField(required=False)
    allow_file_upload = serializers.BooleanField(required=False)
    allow_guest_access = serializers.BooleanField(required=False)
    
    # 密码保护
    password = serializers.CharField(
        max_length=128,
        required=False,
        allow_blank=True,
        help_text="访问密码"
    )
    
    # 媒体资源
    thumbnail_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="缩略图URL"
    )
    background_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="背景图URL"
    )
    
    def validate_name(self, value):
        """验证空间名称"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("空间名称不能为空")
        
        return value.strip()
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                SpaceCategory.objects.get(uuid=value, is_active=True)
            except SpaceCategory.DoesNotExist:
                raise serializers.ValidationError("分类不存在或已禁用")
        
        return value


class SpaceSerializer(serializers.ModelSerializer):
    """空间序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    owner = serializers.SerializerMethodField()
    category = SpaceCategorySerializer(read_only=True)
    occupancy_rate = serializers.ReadOnlyField()
    is_full = serializers.ReadOnlyField()
    member_count = serializers.SerializerMethodField()
    user_membership = serializers.SerializerMethodField()
    user_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Space
        fields = [
            'id', 'name', 'description', 'owner', 'category',
            'space_type', 'visibility', 'status',
            'max_capacity', 'current_users', 'occupancy_rate', 'is_full',
            'allow_voice_chat', 'allow_video_chat', 'allow_screen_share',
            'allow_file_upload', 'allow_guest_access',
            'visit_count', 'like_count', 'member_count',
            'thumbnail_url', 'background_url',
            'virtual_x', 'virtual_y', 'virtual_z',
            'user_membership', 'user_liked',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'owner', 'current_users', 'occupancy_rate', 'is_full',
            'visit_count', 'like_count', 'member_count',
            'user_membership', 'user_liked',
            'created_at', 'updated_at'
        ]
    
    def get_owner(self, obj):
        """获取空间所有者信息"""
        return {
            'id': str(obj.owner.uuid),
            'username': obj.owner.username,
            'display_name': obj.owner.display_name
        }
    
    def get_member_count(self, obj):
        """获取成员数量"""
        return obj.members.filter(status='active').count()
    
    def get_user_membership(self, obj):
        """获取当前用户的成员信息"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                member = SpaceMember.objects.get(
                    space=obj,
                    user=request.user,
                    status='active'
                )
                return SpaceMemberSerializer(member).data
            except SpaceMember.DoesNotExist:
                pass
        return None
    
    def get_user_liked(self, obj):
        """获取当前用户是否点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return SpaceLike.objects.filter(
                space=obj,
                user=request.user
            ).exists()
        return False


class SpaceSummarySerializer(serializers.ModelSerializer):
    """空间摘要序列化器（用于列表显示）"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    owner = serializers.SerializerMethodField()
    category_name = serializers.CharField(source='category.name', read_only=True)
    occupancy_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = Space
        fields = [
            'id', 'name', 'description', 'owner', 'category_name',
            'space_type', 'visibility', 'current_users', 'max_capacity',
            'occupancy_rate', 'thumbnail_url', 'like_count', 'visit_count',
            'created_at'
        ]
        read_only_fields = fields
    
    def get_owner(self, obj):
        """获取空间所有者信息"""
        return {
            'id': str(obj.owner.uuid),
            'username': obj.owner.username,
            'display_name': obj.owner.display_name
        }


class SpaceMemberSerializer(serializers.ModelSerializer):
    """空间成员序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user = serializers.SerializerMethodField()
    space_name = serializers.CharField(source='space.name', read_only=True)
    
    class Meta:
        model = SpaceMember
        fields = [
            'id', 'user', 'space_name', 'role', 'status',
            'can_invite', 'can_kick', 'can_mute', 'can_edit_space',
            'join_count', 'total_time_spent', 'last_active_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'space_name', 'join_count', 'total_time_spent',
            'last_active_at', 'created_at', 'updated_at'
        ]
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj.user.uuid),
            'username': obj.user.username,
            'display_name': obj.user.display_name
        }


class SpaceJoinSerializer(serializers.Serializer):
    """加入空间序列化器"""
    
    password = serializers.CharField(
        max_length=128,
        required=False,
        allow_blank=True,
        help_text="空间密码（如果需要）"
    )


class SpaceInvitationSerializer(serializers.Serializer):
    """空间邀请序列化器"""
    
    invitee_id = serializers.CharField(
        help_text="被邀请用户ID"
    )
    message = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="邀请消息，最多500个字符"
    )
    
    def validate_invitee_id(self, value):
        """验证被邀请用户ID"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            User.objects.get(uuid=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")
        
        return value


class SpaceInvitationDetailSerializer(serializers.ModelSerializer):
    """空间邀请详情序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    space = serializers.SerializerMethodField()
    inviter = serializers.SerializerMethodField()
    invitee = serializers.SerializerMethodField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = SpaceInvitation
        fields = [
            'id', 'space', 'inviter', 'invitee', 'message',
            'status', 'is_expired', 'expires_at', 'responded_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = fields
    
    def get_space(self, obj):
        """获取空间信息"""
        return {
            'id': str(obj.space.uuid),
            'name': obj.space.name,
            'thumbnail_url': obj.space.thumbnail_url
        }
    
    def get_inviter(self, obj):
        """获取邀请者信息"""
        return {
            'id': str(obj.inviter.uuid),
            'username': obj.inviter.username,
            'display_name': obj.inviter.display_name
        }
    
    def get_invitee(self, obj):
        """获取被邀请者信息"""
        return {
            'id': str(obj.invitee.uuid),
            'username': obj.invitee.username,
            'display_name': obj.invitee.display_name
        }


class SpaceSceneSerializer(serializers.ModelSerializer):
    """空间场景序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    space_id = serializers.CharField(source='space.uuid', read_only=True)
    object_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SpaceScene
        fields = [
            'id', 'space_id', 'name', 'description', 'scene_type',
            'scene_data', 'environment_url', 'skybox_url',
            'lighting_config', 'physics_enabled', 'gravity',
            'ambient_sound_url', 'sound_volume',
            'is_active', 'is_default', 'object_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'space_id', 'object_count', 'created_at', 'updated_at'
        ]
    
    def get_object_count(self, obj):
        """获取场景中的对象数量"""
        return obj.objects.filter(is_visible=True).count()
    
    def validate_scene_data(self, value):
        """验证场景数据"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("场景数据必须是字典格式")
        
        # 验证必需的字段
        required_fields = ['objects', 'spawn_points', 'boundaries']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"缺少必需的场景数据字段: {field}")
        
        return value
    
    def validate_lighting_config(self, value):
        """验证光照配置"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("光照配置必须是字典格式")
        
        return value


class SpaceObjectSerializer(serializers.ModelSerializer):
    """空间对象序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    scene_id = serializers.CharField(source='scene.uuid', read_only=True)
    created_by = serializers.SerializerMethodField()
    position = serializers.SerializerMethodField()
    rotation = serializers.SerializerMethodField()
    scale = serializers.SerializerMethodField()
    
    class Meta:
        model = SpaceObject
        fields = [
            'id', 'scene_id', 'created_by', 'name', 'object_type',
            'resource_url', 'thumbnail_url',
            'position', 'rotation', 'scale',
            'is_interactive', 'interaction_data',
            'has_physics', 'is_static', 'mass',
            'is_visible', 'opacity',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'scene_id', 'created_by', 'position', 'rotation', 'scale',
            'created_at', 'updated_at'
        ]
    
    def get_created_by(self, obj):
        """获取创建者信息"""
        if obj.created_by:
            return {
                'id': str(obj.created_by.uuid),
                'username': obj.created_by.username,
                'display_name': obj.created_by.display_name
            }
        return None
    
    def get_position(self, obj):
        """获取位置向量"""
        return obj.position
    
    def get_rotation(self, obj):
        """获取旋转向量"""
        return obj.rotation
    
    def get_scale(self, obj):
        """获取缩放向量"""
        return obj.scale
    
    def validate_interaction_data(self, value):
        """验证交互数据"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("交互数据必须是字典格式")
        
        return value


class SpaceVisitSerializer(serializers.ModelSerializer):
    """空间访问记录序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    space_name = serializers.CharField(source='space.name', read_only=True)
    user = serializers.SerializerMethodField()
    duration_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = SpaceVisit
        fields = [
            'id', 'space_name', 'user', 'ip_address',
            'duration', 'duration_formatted',
            'entered_at', 'left_at'
        ]
        read_only_fields = fields
    
    def get_user(self, obj):
        """获取用户信息"""
        if obj.user:
            return {
                'id': str(obj.user.uuid),
                'username': obj.user.username,
                'display_name': obj.user.display_name
            }
        return None
    
    def get_duration_formatted(self, obj):
        """获取格式化的停留时间"""
        if obj.duration:
            hours = obj.duration // 3600
            minutes = (obj.duration % 3600) // 60
            seconds = obj.duration % 60
            
            if hours > 0:
                return f"{hours}小时{minutes}分钟{seconds}秒"
            elif minutes > 0:
                return f"{minutes}分钟{seconds}秒"
            else:
                return f"{seconds}秒"
        
        return "0秒"


class SpaceStatisticsSerializer(serializers.Serializer):
    """空间统计信息序列化器"""
    
    space_id = serializers.CharField(read_only=True)
    total_members = serializers.IntegerField(read_only=True)
    current_users = serializers.IntegerField(read_only=True)
    max_capacity = serializers.IntegerField(read_only=True)
    occupancy_rate = serializers.FloatField(read_only=True)
    total_visits = serializers.IntegerField(read_only=True)
    recent_visits = serializers.IntegerField(read_only=True)
    total_likes = serializers.IntegerField(read_only=True)
    avg_duration = serializers.IntegerField(read_only=True)
    visit_count = serializers.IntegerField(read_only=True)
    like_count = serializers.IntegerField(read_only=True)