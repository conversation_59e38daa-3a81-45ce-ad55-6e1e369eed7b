"""
空间业务服务 - 高内聚的虚拟空间业务逻辑
职责：处理空间创建、管理、成员管理、场景编辑等核心业务逻辑
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Avg
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent, SpaceCreatedEvent
from apps.core.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    ForbiddenException,
    ConflictException
)
from apps.core.dependencies import inject, ISpaceService
from .models import (
    Space, SpaceCategory, SpaceMember, SpaceScene, SpaceObject,
    SpaceInvitation, SpaceLike, SpaceVisit
)

logger = logging.getLogger(__name__)


class SpaceService(ISpaceService):
    """
    空间业务服务 - 高内聚的空间相关业务逻辑
    
    职责：
    1. 空间创建和管理
    2. 成员管理和权限控制
    3. 场景编辑和对象管理
    4. 邀请和访问控制
    5. 统计和分析
    """
    
    @transaction.atomic
    def create_space(self, owner_id: str, space_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建虚拟空间
        
        Args:
            owner_id: 空间所有者ID
            space_data: 空间数据
            
        Returns:
            Dict: 创建的空间信息
            
        Raises:
            ValidationException: 数据验证失败
            ConflictException: 空间名称冲突
        """
        logger.info(f"开始创建空间: {space_data.get('name')}")
        
        # 1. 数据验证
        self._validate_space_data(space_data)
        
        # 2. 检查业务规则
        self._check_space_creation_rules(owner_id, space_data)
        
        # 3. 创建空间实体
        space = self._create_space_entity(owner_id, space_data)
        
        # 4. 创建默认场景
        default_scene = self._create_default_scene(space)
        
        # 5. 添加所有者为成员
        self._add_owner_as_member(space)
        
        # 6. 发布空间创建事件
        self._publish_space_created_event(space)
        
        logger.info(f"空间创建成功: {space.name} (ID: {space.id})")
        
        return {
            'space_id': str(space.uuid),
            'name': space.name,
            'owner_id': str(space.owner.uuid),
            'space_type': space.space_type,
            'visibility': space.visibility,
            'max_capacity': space.max_capacity,
            'default_scene_id': str(default_scene.uuid),
            'created_at': space.created_at.isoformat()
        }
    
    def get_space_by_id(self, space_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        根据ID获取空间信息
        
        Args:
            space_id: 空间ID
            user_id: 请求用户ID（用于权限检查）
            
        Returns:
            Dict: 空间信息
            
        Raises:
            ResourceNotFoundException: 空间不存在
            ForbiddenException: 无权访问
        """
        try:
            space = Space.objects.select_related(
                'owner', 'category'
            ).prefetch_related(
                'members', 'scenes', 'likes'
            ).get(uuid=space_id)
            
            # 权限检查
            if not self._can_access_space(space, user_id):
                raise ForbiddenException("无权访问该空间")
            
            # 增加访问次数
            space.increment_visit_count()
            
            # 记录访问
            if user_id:
                self._record_space_visit(space, user_id)
            
            return self._serialize_space_detail(space, user_id)
            
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
    
    @transaction.atomic
    def update_space(self, space_id: str, user_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新空间信息
        
        Args:
            space_id: 空间ID
            user_id: 用户ID
            update_data: 更新数据
            
        Returns:
            Dict: 更新后的空间信息
            
        Raises:
            ResourceNotFoundException: 空间不存在
            ForbiddenException: 无权编辑
            ValidationException: 数据验证失败
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        # 权限检查
        if not self._can_edit_space(space, user_id):
            raise ForbiddenException("无权编辑该空间")
        
        # 验证更新数据
        self._validate_space_update_data(update_data)
        
        # 更新字段
        updated_fields = []
        for field, value in update_data.items():
            if hasattr(space, field) and field not in ['id', 'uuid', 'owner', 'created_at']:
                setattr(space, field, value)
                updated_fields.append(field)
        
        space.save(update_fields=updated_fields + ['updated_at'])
        
        # 发布空间更新事件
        self._publish_space_updated_event(space, updated_fields, update_data)
        
        logger.info(f"空间更新成功: {space.name}")
        
        return self._serialize_space_detail(space, user_id)
    
    @transaction.atomic
    def delete_space(self, space_id: str, user_id: str) -> bool:
        """
        删除空间
        
        Args:
            space_id: 空间ID
            user_id: 用户ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            ResourceNotFoundException: 空间不存在
            ForbiddenException: 无权删除
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        # 权限检查（只有所有者可以删除）
        if str(space.owner.uuid) != user_id:
            raise ForbiddenException("只有空间所有者可以删除空间")
        
        # 软删除
        space.soft_delete()
        
        # 发布空间删除事件
        self._publish_space_deleted_event(space)
        
        logger.info(f"空间删除成功: {space.name}")
        
        return True
    
    def search_spaces(self, query: str, filters: Dict[str, Any] = None, 
                     page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        搜索空间
        
        Args:
            query: 搜索关键词
            filters: 过滤条件
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 搜索结果
        """
        queryset = Space.objects.filter(
            status='active',
            visibility__in=['public', 'unlisted']
        ).select_related('owner', 'category')
        
        # 关键词搜索
        if query:
            queryset = queryset.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(category__name__icontains=query)
            )
        
        # 应用过滤器
        if filters:
            if 'space_type' in filters:
                queryset = queryset.filter(space_type=filters['space_type'])
            
            if 'category_id' in filters:
                queryset = queryset.filter(category_id=filters['category_id'])
            
            if 'has_capacity' in filters and filters['has_capacity']:
                queryset = queryset.filter(current_users__lt=models.F('max_capacity'))
        
        # 排序
        order_by = filters.get('order_by', '-created_at')
        queryset = queryset.order_by(order_by)
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        return {
            'spaces': [self._serialize_space_summary(space) for space in page_obj.object_list],
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
    
    @transaction.atomic
    def join_space(self, space_id: str, user_id: str, password: str = None) -> Dict[str, Any]:
        """
        加入空间
        
        Args:
            space_id: 空间ID
            user_id: 用户ID
            password: 空间密码（如果需要）
            
        Returns:
            Dict: 加入结果
            
        Raises:
            ResourceNotFoundException: 空间不存在
            ValidationException: 加入条件不满足
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 检查是否可以加入
        can_join, reason = space.can_join(user)
        if not can_join:
            raise ValidationException(reason)
        
        # 检查密码
        if space.password and space.password != password:
            raise ValidationException("空间密码错误")
        
        # 检查是否已经是成员
        member, created = SpaceMember.objects.get_or_create(
            space=space,
            user=user,
            defaults={
                'role': 'guest' if space.allow_guest_access else 'member',
                'status': 'active'
            }
        )
        
        if not created and member.status == 'active':
            return {
                'already_member': True,
                'message': '您已经是该空间的成员'
            }
        
        # 激活成员状态
        if not created:
            member.status = 'active'
            member.save()
        
        # 更新空间当前用户数
        space.current_users += 1
        space.save(update_fields=['current_users'])
        
        # 增加成员加入次数
        member.join_count += 1
        member.last_active_at = timezone.now()
        member.save(update_fields=['join_count', 'last_active_at'])
        
        # 发布用户加入空间事件
        self._publish_user_joined_space_event(space, user, member)
        
        logger.info(f"用户加入空间: {user.username} -> {space.name}")
        
        return {
            'joined': True,
            'member_id': str(member.uuid),
            'role': member.role,
            'message': '成功加入空间'
        }
    
    @transaction.atomic
    def leave_space(self, space_id: str, user_id: str) -> Dict[str, Any]:
        """
        离开空间
        
        Args:
            space_id: 空间ID
            user_id: 用户ID
            
        Returns:
            Dict: 离开结果
        """
        try:
            member = SpaceMember.objects.get(
                space__uuid=space_id,
                user__uuid=user_id,
                status='active'
            )
        except SpaceMember.DoesNotExist:
            return {'left': False, 'message': '您不是该空间的成员'}
        
        space = member.space
        user = member.user
        
        # 所有者不能离开自己的空间
        if member.role == 'owner':
            raise ValidationException("空间所有者不能离开自己的空间")
        
        # 更新成员状态
        member.status = 'inactive'
        member.save()
        
        # 更新空间当前用户数
        if space.current_users > 0:
            space.current_users -= 1
            space.save(update_fields=['current_users'])
        
        # 发布用户离开空间事件
        self._publish_user_left_space_event(space, user, member)
        
        logger.info(f"用户离开空间: {user.username} <- {space.name}")
        
        return {
            'left': True,
            'message': '成功离开空间'
        }
    
    def get_space_members(self, space_id: str, user_id: str = None) -> List[Dict[str, Any]]:
        """
        获取空间成员列表
        
        Args:
            space_id: 空间ID
            user_id: 请求用户ID
            
        Returns:
            List: 成员列表
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        # 权限检查
        if not self._can_access_space(space, user_id):
            raise ForbiddenException("无权访问该空间")
        
        members = SpaceMember.objects.filter(
            space=space,
            status='active'
        ).select_related('user').order_by('role', 'created_at')
        
        return [self._serialize_space_member(member) for member in members]
    
    @transaction.atomic
    def invite_user_to_space(self, space_id: str, inviter_id: str, 
                           invitee_id: str, message: str = '') -> Dict[str, Any]:
        """
        邀请用户加入空间
        
        Args:
            space_id: 空间ID
            inviter_id: 邀请者ID
            invitee_id: 被邀请者ID
            message: 邀请消息
            
        Returns:
            Dict: 邀请结果
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        # 检查邀请者权限
        if not self._can_invite_to_space(space, inviter_id):
            raise ForbiddenException("无权邀请用户加入该空间")
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            inviter = User.objects.get(uuid=inviter_id)
            invitee = User.objects.get(uuid=invitee_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException("用户不存在", "USER")
        
        # 检查是否已经是成员
        if SpaceMember.objects.filter(space=space, user=invitee, status='active').exists():
            raise ConflictException("用户已经是该空间的成员")
        
        # 检查是否已有待处理的邀请
        existing_invitation = SpaceInvitation.objects.filter(
            space=space,
            invitee=invitee,
            status='pending'
        ).first()
        
        if existing_invitation and not existing_invitation.is_expired:
            raise ConflictException("已有待处理的邀请")
        
        # 创建邀请
        invitation = SpaceInvitation.objects.create(
            space=space,
            inviter=inviter,
            invitee=invitee,
            message=message,
            expires_at=timezone.now() + timedelta(days=7)  # 7天有效期
        )
        
        # 发布邀请事件
        self._publish_space_invitation_event(invitation)
        
        logger.info(f"空间邀请创建: {inviter.username} -> {invitee.username} ({space.name})")
        
        return {
            'invitation_id': str(invitation.uuid),
            'expires_at': invitation.expires_at.isoformat(),
            'message': '邀请发送成功'
        }
    
    @transaction.atomic
    def respond_to_invitation(self, invitation_id: str, user_id: str, 
                            accept: bool) -> Dict[str, Any]:
        """
        响应空间邀请
        
        Args:
            invitation_id: 邀请ID
            user_id: 用户ID
            accept: 是否接受
            
        Returns:
            Dict: 响应结果
        """
        try:
            invitation = SpaceInvitation.objects.get(uuid=invitation_id)
        except SpaceInvitation.DoesNotExist:
            raise ResourceNotFoundException(f"邀请不存在: {invitation_id}", "INVITATION")
        
        # 权限检查
        if str(invitation.invitee.uuid) != user_id:
            raise ForbiddenException("只能响应发给自己的邀请")
        
        if invitation.status != 'pending':
            raise ValidationException("邀请已经被处理过了")
        
        if invitation.is_expired:
            raise ValidationException("邀请已过期")
        
        if accept:
            success = invitation.accept()
            if success:
                # 发布邀请接受事件
                self._publish_invitation_accepted_event(invitation)
                return {'accepted': True, 'message': '成功加入空间'}
            else:
                return {'accepted': False, 'message': '加入失败'}
        else:
            invitation.decline()
            # 发布邀请拒绝事件
            self._publish_invitation_declined_event(invitation)
            return {'declined': True, 'message': '已拒绝邀请'}
    
    def get_user_spaces(self, user_id: str, role: str = None) -> List[Dict[str, Any]]:
        """
        获取用户的空间列表
        
        Args:
            user_id: 用户ID
            role: 角色过滤
            
        Returns:
            List: 空间列表
        """
        queryset = SpaceMember.objects.filter(
            user__uuid=user_id,
            status='active'
        ).select_related('space', 'space__owner')
        
        if role:
            queryset = queryset.filter(role=role)
        
        return [
            {
                'space': self._serialize_space_summary(member.space),
                'membership': self._serialize_space_member(member)
            }
            for member in queryset
        ]
    
    def get_space_statistics(self, space_id: str, user_id: str) -> Dict[str, Any]:
        """
        获取空间统计信息
        
        Args:
            space_id: 空间ID
            user_id: 用户ID
            
        Returns:
            Dict: 统计信息
        """
        try:
            space = Space.objects.get(uuid=space_id)
        except Space.DoesNotExist:
            raise ResourceNotFoundException(f"空间不存在: {space_id}", "SPACE")
        
        # 权限检查
        if not self._can_access_space(space, user_id):
            raise ForbiddenException("无权访问该空间")
        
        # 计算统计数据
        total_members = SpaceMember.objects.filter(space=space, status='active').count()
        total_visits = SpaceVisit.objects.filter(space=space).count()
        total_likes = SpaceLike.objects.filter(space=space).count()
        
        # 最近7天的访问统计
        seven_days_ago = timezone.now() - timedelta(days=7)
        recent_visits = SpaceVisit.objects.filter(
            space=space,
            entered_at__gte=seven_days_ago
        ).count()
        
        # 平均停留时间
        avg_duration = SpaceVisit.objects.filter(
            space=space,
            duration__gt=0
        ).aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
        
        return {
            'space_id': str(space.uuid),
            'total_members': total_members,
            'current_users': space.current_users,
            'max_capacity': space.max_capacity,
            'occupancy_rate': space.occupancy_rate,
            'total_visits': total_visits,
            'recent_visits': recent_visits,
            'total_likes': total_likes,
            'avg_duration': int(avg_duration),
            'visit_count': space.visit_count,
            'like_count': space.like_count
        }
    
    # 私有方法
    
    def _validate_space_data(self, data: Dict[str, Any]):
        """验证空间数据"""
        required_fields = ['name', 'space_type']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )
        
        # 验证空间名称长度
        name = data['name']
        if len(name) < 2 or len(name) > 100:
            raise ValidationException("空间名称长度必须在2-100个字符之间")
        
        # 验证容量
        max_capacity = data.get('max_capacity', 50)
        if max_capacity < 1 or max_capacity > 1000:
            raise ValidationException("空间容量必须在1-1000之间")
    
    def _check_space_creation_rules(self, owner_id: str, data: Dict[str, Any]):
        """检查空间创建业务规则"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            owner = User.objects.get(uuid=owner_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {owner_id}", "USER")
        
        # 检查用户状态
        if owner.status != 'active':
            raise ValidationException("用户状态异常，无法创建空间")
        
        # 检查空间数量限制
        max_spaces = self._get_user_max_spaces(owner)
        current_spaces = Space.objects.filter(owner=owner, status='active').count()
        
        if current_spaces >= max_spaces:
            raise ValidationException(f"您最多只能创建{max_spaces}个空间")
        
        # 检查空间名称是否重复（同一用户）
        name = data['name']
        if Space.objects.filter(owner=owner, name=name, status='active').exists():
            raise ConflictException("您已经有同名的空间了")
    
    def _create_space_entity(self, owner_id: str, data: Dict[str, Any]) -> Space:
        """创建空间实体"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        owner = User.objects.get(uuid=owner_id)
        
        # 获取分类
        category = None
        if data.get('category_id'):
            try:
                category = SpaceCategory.objects.get(uuid=data['category_id'])
            except SpaceCategory.DoesNotExist:
                pass
        
        return Space.objects.create(
            name=data['name'],
            description=data.get('description', ''),
            owner=owner,
            category=category,
            space_type=data['space_type'],
            visibility=data.get('visibility', 'public'),
            max_capacity=data.get('max_capacity', 50),
            allow_voice_chat=data.get('allow_voice_chat', True),
            allow_video_chat=data.get('allow_video_chat', True),
            allow_screen_share=data.get('allow_screen_share', True),
            allow_file_upload=data.get('allow_file_upload', True),
            allow_guest_access=data.get('allow_guest_access', True),
            password=data.get('password', ''),
            thumbnail_url=data.get('thumbnail_url', ''),
            background_url=data.get('background_url', ''),
            created_by=owner,
            updated_by=owner
        )
    
    def _create_default_scene(self, space: Space) -> SpaceScene:
        """创建默认场景"""
        scene = SpaceScene.objects.create(
            space=space,
            name='默认场景',
            description='空间的默认场景',
            scene_type='default',
            is_default=True,
            is_active=True
        )
        
        # 设置默认场景数据
        scene.scene_data = scene.get_default_scene_data()
        scene.lighting_config = scene.get_default_lighting_config()
        scene.save()
        
        return scene
    
    def _add_owner_as_member(self, space: Space):
        """添加所有者为成员"""
        SpaceMember.objects.create(
            space=space,
            user=space.owner,
            role='owner',
            status='active',
            can_invite=True,
            can_kick=True,
            can_mute=True,
            can_edit_space=True
        )
    
    def _can_access_space(self, space: Space, user_id: str = None) -> bool:
        """检查是否可以访问空间"""
        if space.visibility == 'public':
            return True
        
        if not user_id:
            return False
        
        if space.visibility == 'private':
            # 检查是否为成员
            return SpaceMember.objects.filter(
                space=space,
                user__uuid=user_id,
                status='active'
            ).exists()
        
        return True  # unlisted空间认证用户可访问
    
    def _can_edit_space(self, space: Space, user_id: str) -> bool:
        """检查是否可以编辑空间"""
        try:
            member = SpaceMember.objects.get(
                space=space,
                user__uuid=user_id,
                status='active'
            )
            return member.role in ['owner', 'admin'] or member.can_edit_space
        except SpaceMember.DoesNotExist:
            return False
    
    def _can_invite_to_space(self, space: Space, user_id: str) -> bool:
        """检查是否可以邀请用户加入空间"""
        try:
            member = SpaceMember.objects.get(
                space=space,
                user__uuid=user_id,
                status='active'
            )
            return member.role in ['owner', 'admin'] or member.can_invite
        except SpaceMember.DoesNotExist:
            return False
    
    def _get_user_max_spaces(self, user) -> int:
        """获取用户最大空间数量"""
        if user.user_type == 'vip':
            return 50
        elif user.user_type == 'premium':
            return 20
        else:
            return 5
    
    def _record_space_visit(self, space: Space, user_id: str):
        """记录空间访问"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
            SpaceVisit.objects.create(
                space=space,
                user=user,
                ip_address='127.0.0.1',  # 实际应用中从请求获取
                entered_at=timezone.now()
            )
        except User.DoesNotExist:
            pass
    
    def _serialize_space_detail(self, space: Space, user_id: str = None) -> Dict[str, Any]:
        """序列化空间详情"""
        data = {
            'id': str(space.uuid),
            'name': space.name,
            'description': space.description,
            'owner': {
                'id': str(space.owner.uuid),
                'username': space.owner.username,
                'display_name': space.owner.display_name
            },
            'category': {
                'id': str(space.category.uuid),
                'name': space.category.name
            } if space.category else None,
            'space_type': space.space_type,
            'visibility': space.visibility,
            'status': space.status,
            'max_capacity': space.max_capacity,
            'current_users': space.current_users,
            'occupancy_rate': space.occupancy_rate,
            'settings': {
                'allow_voice_chat': space.allow_voice_chat,
                'allow_video_chat': space.allow_video_chat,
                'allow_screen_share': space.allow_screen_share,
                'allow_file_upload': space.allow_file_upload,
                'allow_guest_access': space.allow_guest_access,
                'has_password': bool(space.password)
            },
            'statistics': {
                'visit_count': space.visit_count,
                'like_count': space.like_count,
                'member_count': space.members.filter(status='active').count()
            },
            'media': {
                'thumbnail_url': space.thumbnail_url,
                'background_url': space.background_url
            },
            'created_at': space.created_at.isoformat(),
            'updated_at': space.updated_at.isoformat()
        }
        
        # 添加用户相关信息
        if user_id:
            try:
                member = SpaceMember.objects.get(
                    space=space,
                    user__uuid=user_id,
                    status='active'
                )
                data['user_membership'] = self._serialize_space_member(member)
            except SpaceMember.DoesNotExist:
                data['user_membership'] = None
            
            # 检查是否点赞
            data['user_liked'] = SpaceLike.objects.filter(
                space=space,
                user__uuid=user_id
            ).exists()
        
        return data
    
    def _serialize_space_summary(self, space: Space) -> Dict[str, Any]:
        """序列化空间摘要"""
        return {
            'id': str(space.uuid),
            'name': space.name,
            'description': space.description[:200] + '...' if len(space.description) > 200 else space.description,
            'owner': {
                'id': str(space.owner.uuid),
                'username': space.owner.username,
                'display_name': space.owner.display_name
            },
            'space_type': space.space_type,
            'visibility': space.visibility,
            'current_users': space.current_users,
            'max_capacity': space.max_capacity,
            'occupancy_rate': space.occupancy_rate,
            'thumbnail_url': space.thumbnail_url,
            'like_count': space.like_count,
            'visit_count': space.visit_count,
            'created_at': space.created_at.isoformat()
        }
    
    def _serialize_space_member(self, member: SpaceMember) -> Dict[str, Any]:
        """序列化空间成员"""
        return {
            'id': str(member.uuid),
            'user': {
                'id': str(member.user.uuid),
                'username': member.user.username,
                'display_name': member.user.display_name
            },
            'role': member.role,
            'status': member.status,
            'permissions': {
                'can_invite': member.can_invite,
                'can_kick': member.can_kick,
                'can_mute': member.can_mute,
                'can_edit_space': member.can_edit_space
            },
            'statistics': {
                'join_count': member.join_count,
                'total_time_spent': member.total_time_spent
            },
            'joined_at': member.created_at.isoformat(),
            'last_active_at': member.last_active_at.isoformat() if member.last_active_at else None
        }
    
    def _publish_space_created_event(self, space: Space):
        """发布空间创建事件"""
        event = SpaceCreatedEvent(
            space_id=str(space.uuid),
            owner_id=str(space.owner.uuid),
            space_name=space.name,
            space_type=space.space_type,
            visibility=space.visibility
        )
        event_bus.publish(event)
    
    def _publish_space_updated_event(self, space: Space, updated_fields: List[str], update_data: Dict[str, Any]):
        """发布空间更新事件"""
        event = DomainEvent(
            event_type='space.updated',
            aggregate_id=str(space.uuid),
            data={
                'space_id': str(space.uuid),
                'space_name': space.name,
                'updated_fields': updated_fields,
                'update_data': update_data
            }
        )
        event_bus.publish(event)
    
    def _publish_space_deleted_event(self, space: Space):
        """发布空间删除事件"""
        event = DomainEvent(
            event_type='space.deleted',
            aggregate_id=str(space.uuid),
            data={
                'space_id': str(space.uuid),
                'space_name': space.name,
                'owner_id': str(space.owner.uuid)
            }
        )
        event_bus.publish(event)
    
    def _publish_user_joined_space_event(self, space: Space, user, member: SpaceMember):
        """发布用户加入空间事件"""
        event = DomainEvent(
            event_type='space.user_joined',
            aggregate_id=str(space.uuid),
            data={
                'space_id': str(space.uuid),
                'space_name': space.name,
                'user_id': str(user.uuid),
                'username': user.username,
                'member_id': str(member.uuid),
                'role': member.role
            }
        )
        event_bus.publish(event)
    
    def _publish_user_left_space_event(self, space: Space, user, member: SpaceMember):
        """发布用户离开空间事件"""
        event = DomainEvent(
            event_type='space.user_left',
            aggregate_id=str(space.uuid),
            data={
                'space_id': str(space.uuid),
                'space_name': space.name,
                'user_id': str(user.uuid),
                'username': user.username,
                'member_id': str(member.uuid)
            }
        )
        event_bus.publish(event)
    
    def _publish_space_invitation_event(self, invitation: SpaceInvitation):
        """发布空间邀请事件"""
        event = DomainEvent(
            event_type='space.invitation_sent',
            aggregate_id=str(invitation.space.uuid),
            data={
                'invitation_id': str(invitation.uuid),
                'space_id': str(invitation.space.uuid),
                'space_name': invitation.space.name,
                'inviter_id': str(invitation.inviter.uuid),
                'inviter_username': invitation.inviter.username,
                'invitee_id': str(invitation.invitee.uuid),
                'invitee_username': invitation.invitee.username,
                'message': invitation.message
            }
        )
        event_bus.publish(event)
    
    def _publish_invitation_accepted_event(self, invitation: SpaceInvitation):
        """发布邀请接受事件"""
        event = DomainEvent(
            event_type='space.invitation_accepted',
            aggregate_id=str(invitation.space.uuid),
            data={
                'invitation_id': str(invitation.uuid),
                'space_id': str(invitation.space.uuid),
                'space_name': invitation.space.name,
                'invitee_id': str(invitation.invitee.uuid),
                'invitee_username': invitation.invitee.username
            }
        )
        event_bus.publish(event)
    
    def _publish_invitation_declined_event(self, invitation: SpaceInvitation):
        """发布邀请拒绝事件"""
        event = DomainEvent(
            event_type='space.invitation_declined',
            aggregate_id=str(invitation.space.uuid),
            data={
                'invitation_id': str(invitation.uuid),
                'space_id': str(invitation.space.uuid),
                'space_name': invitation.space.name,
                'invitee_id': str(invitation.invitee.uuid),
                'invitee_username': invitation.invitee.username
            }
        )
        event_bus.publish(event)
    
    def _validate_space_update_data(self, data: Dict[str, Any]):
        """验证空间更新数据"""
        # 验证空间名称
        if 'name' in data:
            name = data['name']
            if len(name) < 2 or len(name) > 100:
                raise ValidationException("空间名称长度必须在2-100个字符之间")
        
        # 验证容量
        if 'max_capacity' in data:
            max_capacity = data['max_capacity']
            if max_capacity < 1 or max_capacity > 1000:
                raise ValidationException("空间容量必须在1-1000之间")