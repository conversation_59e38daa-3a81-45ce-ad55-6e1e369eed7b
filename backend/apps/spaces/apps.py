"""
空间应用配置 - 空间域应用配置
职责：配置空间应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class SpacesConfig(AppConfig):
    """空间应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.spaces'
    verbose_name = '虚拟空间管理'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        logger.info("空间应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import SpaceService
            
            # 注册空间服务为单例
            container.register_singleton(SpaceService, SpaceService)
            
            logger.info("空间服务注册完成")
            
        except Exception as e:
            logger.error(f"空间服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入事件处理器（这会自动注册）
            from . import tasks
            
            logger.info("空间事件处理器注册完成")
            
        except Exception as e:
            logger.error(f"空间事件处理器注册失败: {e}")