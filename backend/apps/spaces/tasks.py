"""
空间异步任务 - 后台处理任务
职责：处理空间相关的异步任务，如通知发送、统计更新等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Avg
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_space_invitation_email(self, invitation_id: int):
    """
    发送空间邀请邮件
    
    Args:
        invitation_id: 邀请ID
    """
    try:
        from .models import SpaceInvitation
        
        invitation = SpaceInvitation.objects.select_related(
            'space', 'inviter', 'invitee'
        ).get(id=invitation_id)
        
        # 检查邀请是否仍然有效
        if invitation.status != 'pending' or invitation.is_expired:
            logger.info(f"邀请已失效，跳过邮件发送: {invitation_id}")
            return
        
        # 渲染邮件模板
        subject = f'{invitation.inviter.username} 邀请您加入空间 "{invitation.space.name}"'
        html_message = render_to_string('emails/space_invitation.html', {
            'invitation': invitation,
            'inviter': invitation.inviter,
            'invitee': invitation.invitee,
            'space': invitation.space,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL,
            'accept_url': f"{settings.FRONTEND_URL}/spaces/invitations/{invitation.uuid}/accept",
            'decline_url': f"{settings.FRONTEND_URL}/spaces/invitations/{invitation.uuid}/decline"
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[invitation.invitee.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"空间邀请邮件发送成功: {invitation.invitee.email}")
        
        # 发布邮件发送事件
        event = DomainEvent(
            event_type='space.invitation_email_sent',
            aggregate_id=str(invitation.space.uuid),
            data={
                'invitation_id': str(invitation.uuid),
                'space_id': str(invitation.space.uuid),
                'invitee_email': invitation.invitee.email
            }
        )
        event_bus.publish(event)
        
    except Exception as exc:
        logger.error(f"发送空间邀请邮件失败: {invitation_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def send_space_notification_email(self, user_id: int, notification_type: str, data: dict):
    """
    发送空间相关通知邮件
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        data: 通知数据
    """
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user = User.objects.get(id=user_id)
        
        # 检查用户通知偏好
        if hasattr(user, 'preferences'):
            notification_settings = user.preferences.notification_settings
            if not notification_settings.get('email', True):
                logger.info(f"用户已关闭邮件通知: {user.username}")
                return
        
        # 根据通知类型选择模板
        template_map = {
            'space_created': 'emails/space_created.html',
            'space_joined': 'emails/space_joined.html',
            'space_left': 'emails/space_left.html',
            'member_joined': 'emails/member_joined.html',
            'member_left': 'emails/member_left.html',
            'space_updated': 'emails/space_updated.html'
        }
        
        template = template_map.get(notification_type)
        if not template:
            logger.error(f"未知的通知类型: {notification_type}")
            return
        
        # 渲染邮件模板
        subject = f"SOIC - {data.get('title', '空间通知')}"
        html_message = render_to_string(template, {
            'user': user,
            'data': data,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"空间通知邮件发送成功: {user.email} - {notification_type}")
        
    except Exception as exc:
        logger.error(f"发送空间通知邮件失败: {user_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task
def update_space_statistics(space_id: int):
    """
    更新空间统计信息
    
    Args:
        space_id: 空间ID
    """
    try:
        from .models import Space, SpaceVisit, SpaceLike, SpaceMember
        
        space = Space.objects.get(id=space_id)
        
        # 更新访问统计
        total_visits = SpaceVisit.objects.filter(space=space).count()
        total_likes = SpaceLike.objects.filter(space=space).count()
        total_members = SpaceMember.objects.filter(space=space, status='active').count()
        
        # 计算平均停留时间
        avg_duration = SpaceVisit.objects.filter(
            space=space,
            duration__gt=0
        ).aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
        
        # 更新空间统计字段
        space.visit_count = total_visits
        space.like_count = total_likes
        space.save(update_fields=['visit_count', 'like_count'])
        
        # 更新元数据
        space.set_metadata('total_members', total_members)
        space.set_metadata('avg_duration', int(avg_duration))
        space.set_metadata('last_stats_update', timezone.now().isoformat())
        
        logger.info(f"空间统计信息更新完成: {space.name}")
        
    except Exception as exc:
        logger.error(f"更新空间统计信息失败: {space_id}, 错误: {exc}")


@shared_task
def cleanup_expired_invitations():
    """
    清理过期的空间邀请
    """
    try:
        from .models import SpaceInvitation
        
        expired_count = SpaceInvitation.objects.filter(
            status='pending',
            expires_at__lt=timezone.now()
        ).update(status='expired')
        
        logger.info(f"清理过期邀请完成: {expired_count}个")
        
    except Exception as exc:
        logger.error(f"清理过期邀请失败: {exc}")


@shared_task
def cleanup_old_visits():
    """
    清理旧的访问记录（保留30天）
    """
    try:
        from .models import SpaceVisit
        
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count, _ = SpaceVisit.objects.filter(
            entered_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"清理旧访问记录完成: {deleted_count}条")
        
    except Exception as exc:
        logger.error(f"清理旧访问记录失败: {exc}")


@shared_task
def generate_space_analytics_report(space_id: int, period: str = 'week'):
    """
    生成空间分析报告
    
    Args:
        space_id: 空间ID
        period: 统计周期 ('day', 'week', 'month')
    """
    try:
        from .models import Space, SpaceVisit, SpaceMember
        
        space = Space.objects.get(id=space_id)
        
        # 确定时间范围
        if period == 'day':
            start_date = timezone.now() - timedelta(days=1)
        elif period == 'week':
            start_date = timezone.now() - timedelta(weeks=1)
        elif period == 'month':
            start_date = timezone.now() - timedelta(days=30)
        else:
            start_date = timezone.now() - timedelta(weeks=1)
        
        # 统计访问数据
        visits = SpaceVisit.objects.filter(
            space=space,
            entered_at__gte=start_date
        )
        
        total_visits = visits.count()
        unique_visitors = visits.values('user').distinct().count()
        avg_duration = visits.aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
        
        # 统计成员数据
        new_members = SpaceMember.objects.filter(
            space=space,
            created_at__gte=start_date,
            status='active'
        ).count()
        
        # 生成报告数据
        report_data = {
            'space_id': str(space.uuid),
            'space_name': space.name,
            'period': period,
            'start_date': start_date.isoformat(),
            'end_date': timezone.now().isoformat(),
            'metrics': {
                'total_visits': total_visits,
                'unique_visitors': unique_visitors,
                'avg_duration': int(avg_duration),
                'new_members': new_members,
                'current_members': space.members.filter(status='active').count(),
                'occupancy_rate': space.occupancy_rate
            }
        }
        
        # 存储报告到元数据
        space.set_metadata(f'analytics_report_{period}', report_data)
        
        logger.info(f"空间分析报告生成完成: {space.name} - {period}")
        
        return report_data
        
    except Exception as exc:
        logger.error(f"生成空间分析报告失败: {space_id}, 错误: {exc}")
        return None


@shared_task
def sync_space_to_external_service(space_id: int, service_name: str):
    """
    同步空间数据到外部服务
    
    Args:
        space_id: 空间ID
        service_name: 外部服务名称
    """
    try:
        from .models import Space
        
        space = Space.objects.get(id=space_id)
        
        # 准备同步数据
        sync_data = {
            'space_id': str(space.uuid),
            'name': space.name,
            'description': space.description,
            'space_type': space.space_type,
            'visibility': space.visibility,
            'max_capacity': space.max_capacity,
            'current_users': space.current_users,
            'owner_id': str(space.owner.uuid),
            'created_at': space.created_at.isoformat(),
            'updated_at': space.updated_at.isoformat()
        }
        
        # 根据服务类型进行同步
        if service_name == 'analytics':
            # 同步到分析服务
            # analytics_client.sync_space(sync_data)
            pass
        elif service_name == 'search':
            # 同步到搜索服务
            # search_client.index_space(sync_data)
            pass
        elif service_name == 'cdn':
            # 同步到CDN
            # cdn_client.cache_space_data(sync_data)
            pass
        
        logger.info(f"空间数据同步完成: {space.name} -> {service_name}")
        
    except Exception as exc:
        logger.error(f"空间数据同步失败: {space_id} -> {service_name}, 错误: {exc}")


@shared_task
def process_space_backup(space_id: int):
    """
    处理空间数据备份
    
    Args:
        space_id: 空间ID
    """
    try:
        from .models import Space, SpaceScene, SpaceObject
        import json
        
        space = Space.objects.get(id=space_id)
        
        # 收集空间数据
        backup_data = {
            'space': {
                'id': str(space.uuid),
                'name': space.name,
                'description': space.description,
                'space_type': space.space_type,
                'visibility': space.visibility,
                'settings': {
                    'max_capacity': space.max_capacity,
                    'allow_voice_chat': space.allow_voice_chat,
                    'allow_video_chat': space.allow_video_chat,
                    'allow_screen_share': space.allow_screen_share,
                    'allow_file_upload': space.allow_file_upload,
                    'allow_guest_access': space.allow_guest_access
                },
                'media': {
                    'thumbnail_url': space.thumbnail_url,
                    'background_url': space.background_url
                },
                'metadata': space.metadata,
                'created_at': space.created_at.isoformat()
            },
            'scenes': [],
            'objects': []
        }
        
        # 收集场景数据
        scenes = SpaceScene.objects.filter(space=space)
        for scene in scenes:
            scene_data = {
                'id': str(scene.uuid),
                'name': scene.name,
                'description': scene.description,
                'scene_type': scene.scene_type,
                'scene_data': scene.scene_data,
                'lighting_config': scene.lighting_config,
                'is_default': scene.is_default,
                'created_at': scene.created_at.isoformat()
            }
            backup_data['scenes'].append(scene_data)
            
            # 收集场景中的对象
            objects = SpaceObject.objects.filter(scene=scene)
            for obj in objects:
                object_data = {
                    'id': str(obj.uuid),
                    'scene_id': str(scene.uuid),
                    'name': obj.name,
                    'object_type': obj.object_type,
                    'resource_url': obj.resource_url,
                    'position': obj.position,
                    'rotation': obj.rotation,
                    'scale': obj.scale,
                    'interaction_data': obj.interaction_data,
                    'created_at': obj.created_at.isoformat()
                }
                backup_data['objects'].append(object_data)
        
        # 生成备份文件名
        backup_filename = f"space_backup_{space.uuid}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 保存备份数据（这里可以保存到文件系统或云存储）
        # with open(f"/backups/{backup_filename}", 'w') as f:
        #     json.dump(backup_data, f, indent=2, ensure_ascii=False)
        
        # 更新空间元数据
        space.set_metadata('last_backup', {
            'filename': backup_filename,
            'timestamp': timezone.now().isoformat(),
            'size': len(json.dumps(backup_data))
        })
        
        logger.info(f"空间备份完成: {space.name} -> {backup_filename}")
        
        return backup_filename
        
    except Exception as exc:
        logger.error(f"空间备份失败: {space_id}, 错误: {exc}")
        return None


# 事件处理器

from apps.core.events import event_handler

@event_handler('space.created', async_handler=True)
def handle_space_created(event):
    """处理空间创建事件"""
    space_id = event.data.get('space_id')
    owner_id = event.data.get('owner_id')
    
    if space_id and owner_id:
        # 发送创建通知
        send_space_notification_email.delay(
            int(owner_id),
            'space_created',
            {
                'title': '空间创建成功',
                'space_name': event.data.get('space_name'),
                'space_id': space_id
            }
        )
        
        # 同步到外部服务
        sync_space_to_external_service.delay(int(space_id), 'search')
        sync_space_to_external_service.delay(int(space_id), 'analytics')


@event_handler('space.invitation_sent', async_handler=True)
def handle_space_invitation_sent(event):
    """处理空间邀请发送事件"""
    invitation_id = event.data.get('invitation_id')
    if invitation_id:
        # 发送邀请邮件
        send_space_invitation_email.delay(int(invitation_id))


@event_handler('space.user_joined', async_handler=True)
def handle_user_joined_space(event):
    """处理用户加入空间事件"""
    space_id = event.data.get('space_id')
    user_id = event.data.get('user_id')
    
    if space_id and user_id:
        # 更新空间统计
        update_space_statistics.delay(int(space_id))
        
        # 发送加入通知给空间所有者
        send_space_notification_email.delay(
            int(user_id),
            'space_joined',
            {
                'title': '成功加入空间',
                'space_name': event.data.get('space_name'),
                'space_id': space_id
            }
        )


@event_handler('space.user_left', async_handler=True)
def handle_user_left_space(event):
    """处理用户离开空间事件"""
    space_id = event.data.get('space_id')
    if space_id:
        # 更新空间统计
        update_space_statistics.delay(int(space_id))


@event_handler('space.updated', async_handler=True)
def handle_space_updated(event):
    """处理空间更新事件"""
    space_id = event.data.get('space_id')
    if space_id:
        # 同步到外部服务
        sync_space_to_external_service.delay(int(space_id), 'search')
        sync_space_to_external_service.delay(int(space_id), 'cdn')