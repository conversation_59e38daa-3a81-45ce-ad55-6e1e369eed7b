"""
空间权限控制 - 访问控制和权限验证
职责：定义空间相关的权限类，控制API访问权限
"""

from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView
from django.contrib.auth.models import AnonymousUser
from .models import Space, SpaceMember


class IsSpaceOwnerOrReadOnly(permissions.BasePermission):
    """
    只有空间所有者可以编辑，其他人只能读取
    """
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 读取权限对所有人开放（根据空间可见性）
        if request.method in permissions.SAFE_METHODS:
            return self._can_read_space(request, obj)
        
        # 写入权限只对所有者开放
        return obj.owner == request.user
    
    def _can_read_space(self, request: Request, space: Space) -> bool:
        """检查是否可以读取空间"""
        if space.visibility == 'public':
            return True
        
        if not request.user.is_authenticated:
            return False
        
        if space.visibility == 'private':
            # 检查是否为成员
            return SpaceMember.objects.filter(
                space=space,
                user=request.user,
                status='active'
            ).exists()
        
        return True  # unlisted空间认证用户可访问


class CanAccessSpace(permissions.BasePermission):
    """
    可以访问空间的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        # 基本认证检查
        return True  # 在对象级别检查具体权限
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        # 公开空间所有人可访问
        if space.visibility == 'public':
            return True
        
        # 未认证用户不能访问非公开空间
        if not request.user.is_authenticated:
            return False
        
        # 私有空间只有成员可访问
        if space.visibility == 'private':
            return SpaceMember.objects.filter(
                space=space,
                user=request.user,
                status='active'
            ).exists()
        
        # unlisted空间认证用户可访问
        return True


class CanEditSpace(permissions.BasePermission):
    """
    可以编辑空间的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            
            # 所有者和管理员可以编辑
            if member.role in ['owner', 'admin']:
                return True
            
            # 检查特定编辑权限
            return member.can_edit_space
            
        except SpaceMember.DoesNotExist:
            return False


class CanManageSpaceMembers(permissions.BasePermission):
    """
    可以管理空间成员的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            
            # 所有者和管理员可以管理成员
            if member.role in ['owner', 'admin']:
                return True
            
            # 版主可以踢出和禁言
            if member.role == 'moderator':
                return member.can_kick or member.can_mute
            
            return False
            
        except SpaceMember.DoesNotExist:
            return False


class CanInviteToSpace(permissions.BasePermission):
    """
    可以邀请用户加入空间的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            
            # 所有者和管理员可以邀请
            if member.role in ['owner', 'admin']:
                return True
            
            # 检查邀请权限
            return member.can_invite
            
        except SpaceMember.DoesNotExist:
            return False


class CanCreateSpaceObjects(permissions.BasePermission):
    """
    可以在空间中创建对象的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        # 检查用户是否为空间成员
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            
            # 所有者、管理员、版主可以创建对象
            if member.role in ['owner', 'admin', 'moderator']:
                return True
            
            # 普通成员需要检查空间设置
            return space.allow_file_upload  # 假设文件上传权限控制对象创建
            
        except SpaceMember.DoesNotExist:
            return False


class IsSpaceMemberOrReadOnly(permissions.BasePermission):
    """
    只有空间成员可以写入，其他人只能读取
    """
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        # 读取权限检查
        if request.method in permissions.SAFE_METHODS:
            return self._can_read_space(request, space)
        
        # 写入权限需要是成员
        if not request.user.is_authenticated:
            return False
        
        return SpaceMember.objects.filter(
            space=space,
            user=request.user,
            status='active'
        ).exists()
    
    def _can_read_space(self, request: Request, space: Space) -> bool:
        """检查是否可以读取空间"""
        if space.visibility == 'public':
            return True
        
        if not request.user.is_authenticated:
            return False
        
        if space.visibility == 'private':
            return SpaceMember.objects.filter(
                space=space,
                user=request.user,
                status='active'
            ).exists()
        
        return True


class CanJoinSpace(permissions.BasePermission):
    """
    可以加入空间的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        # 检查空间状态
        if space.status != 'active':
            return False
        
        # 检查是否已满
        if space.is_full:
            return False
        
        # 检查是否已经是成员
        if SpaceMember.objects.filter(
            space=space,
            user=request.user,
            status='active'
        ).exists():
            return False  # 已经是成员，不需要再加入
        
        # 公开空间可以直接加入
        if space.visibility == 'public':
            return True
        
        # 私有空间需要邀请
        if space.visibility == 'private':
            from .models import SpaceInvitation
            return SpaceInvitation.objects.filter(
                space=space,
                invitee=request.user,
                status='pending'
            ).exists()
        
        # unlisted空间认证用户可加入
        return True


class SpaceOwnerPermission(permissions.BasePermission):
    """
    空间所有者权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        return space.owner == request.user


class SpaceAdminPermission(permissions.BasePermission):
    """
    空间管理员权限（所有者或管理员）
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            return member.role in ['owner', 'admin']
            
        except SpaceMember.DoesNotExist:
            return False


class SpaceModeratorPermission(permissions.BasePermission):
    """
    空间版主权限（所有者、管理员或版主）
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        space = obj if isinstance(obj, Space) else obj.space
        
        try:
            member = SpaceMember.objects.get(
                space=space,
                user=request.user,
                status='active'
            )
            return member.role in ['owner', 'admin', 'moderator']
            
        except SpaceMember.DoesNotExist:
            return False


# 权限检查函数

def can_access_space(user, space: Space) -> bool:
    """
    检查用户是否可以访问空间
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        bool: 是否可以访问
    """
    if isinstance(user, AnonymousUser):
        return space.visibility == 'public'
    
    if space.visibility == 'public':
        return True
    
    if space.visibility == 'private':
        return SpaceMember.objects.filter(
            space=space,
            user=user,
            status='active'
        ).exists()
    
    # unlisted空间认证用户可访问
    return user.is_authenticated


def can_edit_space(user, space: Space) -> bool:
    """
    检查用户是否可以编辑空间
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        bool: 是否可以编辑
    """
    if isinstance(user, AnonymousUser):
        return False
    
    try:
        member = SpaceMember.objects.get(
            space=space,
            user=user,
            status='active'
        )
        
        if member.role in ['owner', 'admin']:
            return True
        
        return member.can_edit_space
        
    except SpaceMember.DoesNotExist:
        return False


def can_invite_to_space(user, space: Space) -> bool:
    """
    检查用户是否可以邀请其他用户加入空间
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        bool: 是否可以邀请
    """
    if isinstance(user, AnonymousUser):
        return False
    
    try:
        member = SpaceMember.objects.get(
            space=space,
            user=user,
            status='active'
        )
        
        if member.role in ['owner', 'admin']:
            return True
        
        return member.can_invite
        
    except SpaceMember.DoesNotExist:
        return False


def can_manage_space_members(user, space: Space) -> bool:
    """
    检查用户是否可以管理空间成员
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        bool: 是否可以管理成员
    """
    if isinstance(user, AnonymousUser):
        return False
    
    try:
        member = SpaceMember.objects.get(
            space=space,
            user=user,
            status='active'
        )
        
        if member.role in ['owner', 'admin']:
            return True
        
        if member.role == 'moderator':
            return member.can_kick or member.can_mute
        
        return False
        
    except SpaceMember.DoesNotExist:
        return False


def get_user_space_role(user, space: Space) -> str:
    """
    获取用户在空间中的角色
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        str: 用户角色，如果不是成员返回None
    """
    if isinstance(user, AnonymousUser):
        return None
    
    try:
        member = SpaceMember.objects.get(
            space=space,
            user=user,
            status='active'
        )
        return member.role
        
    except SpaceMember.DoesNotExist:
        return None


def get_user_space_permissions(user, space: Space) -> dict:
    """
    获取用户在空间中的权限
    
    Args:
        user: 用户对象
        space: 空间对象
        
    Returns:
        dict: 权限字典
    """
    if isinstance(user, AnonymousUser):
        return {
            'can_access': space.visibility == 'public',
            'can_edit': False,
            'can_invite': False,
            'can_kick': False,
            'can_mute': False,
            'can_manage_members': False,
            'role': None
        }
    
    try:
        member = SpaceMember.objects.get(
            space=space,
            user=user,
            status='active'
        )
        
        is_owner_or_admin = member.role in ['owner', 'admin']
        
        return {
            'can_access': True,
            'can_edit': is_owner_or_admin or member.can_edit_space,
            'can_invite': is_owner_or_admin or member.can_invite,
            'can_kick': is_owner_or_admin or member.can_kick,
            'can_mute': is_owner_or_admin or member.can_mute,
            'can_manage_members': is_owner_or_admin or (member.role == 'moderator' and (member.can_kick or member.can_mute)),
            'role': member.role
        }
        
    except SpaceMember.DoesNotExist:
        return {
            'can_access': can_access_space(user, space),
            'can_edit': False,
            'can_invite': False,
            'can_kick': False,
            'can_mute': False,
            'can_manage_members': False,
            'role': None
        }