"""
空间URL路由配置 - 独立的空间域路由
职责：定义空间相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SpaceViewSet, 
    SpaceInvitationViewSet, 
    SpaceCategoryViewSet,
    SpaceSceneViewSet,
    SpaceObjectViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'spaces', SpaceViewSet, basename='space')
router.register(r'space-invitations', SpaceInvitationViewSet, basename='spaceinvitation')
router.register(r'space-categories', SpaceCategoryViewSet, basename='spacecategory')
router.register(r'space-scenes', SpaceSceneViewSet, basename='spacescene')
router.register(r'space-objects', SpaceObjectViewSet, basename='spaceobject')

# URL模式
urlpatterns = [
    # API路由
    path('api/v1/', include(router.urls)),
]

# 应用命名空间
app_name = 'spaces'