"""
空间视图控制器 - 薄层HTTP处理
职责：处理空间相关的HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.core.responses import APIResponse, SuccessMessages
from apps.core.exceptions import handle_exceptions
from .services import SpaceService
from .serializers import (
    SpaceCreateSerializer,
    SpaceUpdateSerializer,
    SpaceSerializer,
    SpaceMemberSerializer,
    SpaceInvitationSerializer,
    SpaceJoinSerializer
)
from .permissions import IsSpaceOwnerOrReadOnly, CanAccessSpace, CanEditSpace

logger = logging.getLogger(__name__)


class SpaceViewSet(viewsets.ModelViewSet):
    """
    空间视图集 - 薄层控制器
    
    职责：
    1. 处理空间相关的HTTP请求
    2. 调用空间业务服务
    3. 返回标准化响应
    4. 处理权限控制
    """
    
    serializer_class = SpaceSerializer
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.space_service = SpaceService()
    
    @handle_exceptions
    def create(self, request: Request) -> Response:
        """
        创建空间
        
        POST /api/v1/spaces/
        """
        logger.info(f"创建空间请求: {request.user.username}")
        
        # 数据验证
        serializer = SpaceCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.space_service.create_space(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message=SuccessMessages.SPACE_CREATED
        )
    
    @handle_exceptions
    def retrieve(self, request: Request, pk=None) -> Response:
        """
        获取空间详情
        
        GET /api/v1/spaces/{id}/
        """
        user_id = str(request.user.uuid) if request.user.is_authenticated else None
        
        # 调用业务服务
        result = self.space_service.get_space_by_id(pk, user_id)
        
        return APIResponse.success(data=result)
    
    @handle_exceptions
    def update(self, request: Request, pk=None) -> Response:
        """
        更新空间信息
        
        PUT /api/v1/spaces/{id}/
        """
        logger.info(f"更新空间请求: {request.user.username} - {pk}")
        
        # 数据验证
        serializer = SpaceUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.space_service.update_space(
            pk,
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.updated(
            data=result,
            message=SuccessMessages.SPACE_UPDATED
        )
    
    @handle_exceptions
    def partial_update(self, request: Request, pk=None) -> Response:
        """
        部分更新空间信息
        
        PATCH /api/v1/spaces/{id}/
        """
        return self.update(request, pk)
    
    @handle_exceptions
    def destroy(self, request: Request, pk=None) -> Response:
        """
        删除空间
        
        DELETE /api/v1/spaces/{id}/
        """
        logger.info(f"删除空间请求: {request.user.username} - {pk}")
        
        # 调用业务服务
        success = self.space_service.delete_space(pk, str(request.user.uuid))
        
        if success:
            return APIResponse.deleted(message="空间删除成功")
        else:
            return APIResponse.error(
                code="DELETE_FAILED",
                message="空间删除失败",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    @method_decorator(cache_page(60 * 2))  # 缓存2分钟
    @handle_exceptions
    def search(self, request: Request) -> Response:
        """
        搜索空间
        
        GET /api/v1/spaces/search/?q=keyword&space_type=social&page=1&page_size=20
        """
        query = request.query_params.get('q', '').strip()
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        
        # 构建过滤器
        filters = {}
        if request.query_params.get('space_type'):
            filters['space_type'] = request.query_params.get('space_type')
        
        if request.query_params.get('category_id'):
            filters['category_id'] = request.query_params.get('category_id')
        
        if request.query_params.get('has_capacity'):
            filters['has_capacity'] = request.query_params.get('has_capacity').lower() == 'true'
        
        if request.query_params.get('order_by'):
            filters['order_by'] = request.query_params.get('order_by')
        
        # 调用业务服务
        result = self.space_service.search_spaces(query, filters, page, page_size)
        
        return APIResponse.success(
            data=result['spaces'],
            meta={'pagination': result['pagination'], 'query': query}
        )
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def join(self, request: Request, pk=None) -> Response:
        """
        加入空间
        
        POST /api/v1/spaces/{id}/join/
        """
        logger.info(f"加入空间请求: {request.user.username} - {pk}")
        
        # 数据验证
        serializer = SpaceJoinSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        password = serializer.validated_data.get('password')
        
        # 调用业务服务
        result = self.space_service.join_space(
            pk,
            str(request.user.uuid),
            password
        )
        
        return APIResponse.success(
            data=result,
            message=result.get('message', SuccessMessages.SPACE_JOINED)
        )
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def leave(self, request: Request, pk=None) -> Response:
        """
        离开空间
        
        POST /api/v1/spaces/{id}/leave/
        """
        logger.info(f"离开空间请求: {request.user.username} - {pk}")
        
        # 调用业务服务
        result = self.space_service.leave_space(pk, str(request.user.uuid))
        
        return APIResponse.success(
            data=result,
            message=result.get('message', SuccessMessages.SPACE_LEFT)
        )
    
    @action(detail=True, methods=['get'])
    @handle_exceptions
    def members(self, request: Request, pk=None) -> Response:
        """
        获取空间成员列表
        
        GET /api/v1/spaces/{id}/members/
        """
        user_id = str(request.user.uuid) if request.user.is_authenticated else None
        
        # 调用业务服务
        result = self.space_service.get_space_members(pk, user_id)
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def invite(self, request: Request, pk=None) -> Response:
        """
        邀请用户加入空间
        
        POST /api/v1/spaces/{id}/invite/
        """
        logger.info(f"邀请用户请求: {request.user.username} - {pk}")
        
        # 数据验证
        serializer = SpaceInvitationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.space_service.invite_user_to_space(
            pk,
            str(request.user.uuid),
            serializer.validated_data['invitee_id'],
            serializer.validated_data.get('message', '')
        )
        
        return APIResponse.success(
            data=result,
            message="邀请发送成功"
        )
    
    @action(detail=True, methods=['get'])
    @handle_exceptions
    def statistics(self, request: Request, pk=None) -> Response:
        """
        获取空间统计信息
        
        GET /api/v1/spaces/{id}/statistics/
        """
        # 调用业务服务
        result = self.space_service.get_space_statistics(pk, str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def like(self, request: Request, pk=None) -> Response:
        """
        点赞空间
        
        POST /api/v1/spaces/{id}/like/
        """
        from .models import Space, SpaceLike
        
        try:
            space = Space.objects.get(uuid=pk)
        except Space.DoesNotExist:
            return APIResponse.not_found(message="空间不存在")
        
        # 检查是否已点赞
        like, created = SpaceLike.objects.get_or_create(
            space=space,
            user=request.user
        )
        
        if created:
            space.increment_like_count()
            return APIResponse.success(
                data={'liked': True},
                message="点赞成功"
            )
        else:
            return APIResponse.success(
                data={'liked': False},
                message="您已经点赞过了"
            )
    
    @action(detail=True, methods=['delete'])
    @handle_exceptions
    def unlike(self, request: Request, pk=None) -> Response:
        """
        取消点赞空间
        
        DELETE /api/v1/spaces/{id}/like/
        """
        from .models import Space, SpaceLike
        
        try:
            space = Space.objects.get(uuid=pk)
            like = SpaceLike.objects.get(space=space, user=request.user)
            like.delete()
            space.decrement_like_count()
            
            return APIResponse.success(
                data={'unliked': True},
                message="取消点赞成功"
            )
            
        except Space.DoesNotExist:
            return APIResponse.not_found(message="空间不存在")
        except SpaceLike.DoesNotExist:
            return APIResponse.success(
                data={'unliked': False},
                message="您还没有点赞过"
            )
    
    @action(detail=False, methods=['get'])
    @handle_exceptions
    def my_spaces(self, request: Request) -> Response:
        """
        获取我的空间列表
        
        GET /api/v1/spaces/my_spaces/?role=owner
        """
        role = request.query_params.get('role')
        
        # 调用业务服务
        result = self.space_service.get_user_spaces(str(request.user.uuid), role)
        
        return APIResponse.success(data=result)


class SpaceInvitationViewSet(viewsets.ReadOnlyModelViewSet):
    """空间邀请视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.space_service = SpaceService()
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def accept(self, request: Request, pk=None) -> Response:
        """
        接受邀请
        
        POST /api/v1/space-invitations/{id}/accept/
        """
        logger.info(f"接受空间邀请: {request.user.username} - {pk}")
        
        # 调用业务服务
        result = self.space_service.respond_to_invitation(
            pk,
            str(request.user.uuid),
            accept=True
        )
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '邀请处理成功')
        )
    
    @action(detail=True, methods=['post'])
    @handle_exceptions
    def decline(self, request: Request, pk=None) -> Response:
        """
        拒绝邀请
        
        POST /api/v1/space-invitations/{id}/decline/
        """
        logger.info(f"拒绝空间邀请: {request.user.username} - {pk}")
        
        # 调用业务服务
        result = self.space_service.respond_to_invitation(
            pk,
            str(request.user.uuid),
            accept=False
        )
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '邀请处理成功')
        )
    
    @action(detail=False, methods=['get'])
    @handle_exceptions
    def pending(self, request: Request) -> Response:
        """
        获取待处理的邀请列表
        
        GET /api/v1/space-invitations/pending/
        """
        from .models import SpaceInvitation
        
        invitations = SpaceInvitation.objects.filter(
            invitee=request.user,
            status='pending'
        ).select_related('space', 'inviter').order_by('-created_at')
        
        data = []
        for invitation in invitations:
            if not invitation.is_expired:
                data.append({
                    'id': str(invitation.uuid),
                    'space': {
                        'id': str(invitation.space.uuid),
                        'name': invitation.space.name,
                        'thumbnail_url': invitation.space.thumbnail_url
                    },
                    'inviter': {
                        'id': str(invitation.inviter.uuid),
                        'username': invitation.inviter.username,
                        'display_name': invitation.inviter.display_name
                    },
                    'message': invitation.message,
                    'created_at': invitation.created_at.isoformat(),
                    'expires_at': invitation.expires_at.isoformat()
                })
        
        return APIResponse.success(data=data)


class SpaceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """空间分类视图集"""
    
    permission_classes = [AllowAny]
    
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    @handle_exceptions
    def list(self, request: Request) -> Response:
        """
        获取空间分类列表
        
        GET /api/v1/space-categories/
        """
        from .models import SpaceCategory
        
        categories = SpaceCategory.objects.filter(is_active=True).order_by('sort_order', 'name')
        
        data = []
        for category in categories:
            data.append({
                'id': str(category.uuid),
                'name': category.name,
                'description': category.description,
                'icon_url': category.icon_url,
                'color': category.color,
                'space_count': category.spaces.filter(status='active').count()
            })
        
        return APIResponse.success(data=data)


class SpaceSceneViewSet(viewsets.ModelViewSet):
    """空间场景视图集"""
    
    permission_classes = [IsAuthenticated, CanEditSpace]
    
    @handle_exceptions
    def list(self, request: Request) -> Response:
        """
        获取空间场景列表
        
        GET /api/v1/space-scenes/?space_id=xxx
        """
        space_id = request.query_params.get('space_id')
        if not space_id:
            return APIResponse.validation_error(
                errors={'space_id': ['空间ID不能为空']},
                message="参数验证失败"
            )
        
        from .models import SpaceScene
        
        scenes = SpaceScene.objects.filter(
            space__uuid=space_id,
            is_active=True
        ).order_by('-is_default', 'created_at')
        
        data = []
        for scene in scenes:
            data.append({
                'id': str(scene.uuid),
                'name': scene.name,
                'description': scene.description,
                'scene_type': scene.scene_type,
                'is_default': scene.is_default,
                'is_active': scene.is_active,
                'environment_url': scene.environment_url,
                'skybox_url': scene.skybox_url,
                'created_at': scene.created_at.isoformat()
            })
        
        return APIResponse.success(data=data)
    
    @handle_exceptions
    def create(self, request: Request) -> Response:
        """
        创建空间场景
        
        POST /api/v1/space-scenes/
        """
        # TODO: 实现场景创建逻辑
        return APIResponse.success(message="场景创建功能开发中")
    
    @handle_exceptions
    def update(self, request: Request, pk=None) -> Response:
        """
        更新空间场景
        
        PUT /api/v1/space-scenes/{id}/
        """
        # TODO: 实现场景更新逻辑
        return APIResponse.success(message="场景更新功能开发中")


class SpaceObjectViewSet(viewsets.ModelViewSet):
    """空间对象视图集"""
    
    permission_classes = [IsAuthenticated, CanEditSpace]
    
    @handle_exceptions
    def list(self, request: Request) -> Response:
        """
        获取空间对象列表
        
        GET /api/v1/space-objects/?scene_id=xxx
        """
        scene_id = request.query_params.get('scene_id')
        if not scene_id:
            return APIResponse.validation_error(
                errors={'scene_id': ['场景ID不能为空']},
                message="参数验证失败"
            )
        
        from .models import SpaceObject
        
        objects = SpaceObject.objects.filter(
            scene__uuid=scene_id,
            is_visible=True
        ).select_related('created_by').order_by('created_at')
        
        data = []
        for obj in objects:
            data.append({
                'id': str(obj.uuid),
                'name': obj.name,
                'object_type': obj.object_type,
                'resource_url': obj.resource_url,
                'thumbnail_url': obj.thumbnail_url,
                'position': obj.position,
                'rotation': obj.rotation,
                'scale': obj.scale,
                'is_interactive': obj.is_interactive,
                'has_physics': obj.has_physics,
                'opacity': obj.opacity,
                'created_by': {
                    'id': str(obj.created_by.uuid),
                    'username': obj.created_by.username
                } if obj.created_by else None,
                'created_at': obj.created_at.isoformat()
            })
        
        return APIResponse.success(data=data)
    
    @handle_exceptions
    def create(self, request: Request) -> Response:
        """
        创建空间对象
        
        POST /api/v1/space-objects/
        """
        # TODO: 实现对象创建逻辑
        return APIResponse.success(message="对象创建功能开发中")
    
    @handle_exceptions
    def update(self, request: Request, pk=None) -> Response:
        """
        更新空间对象
        
        PUT /api/v1/space-objects/{id}/
        """
        # TODO: 实现对象更新逻辑
        return APIResponse.success(message="对象更新功能开发中")