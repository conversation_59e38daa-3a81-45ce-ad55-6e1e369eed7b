"""
空间域数据模型 - 高内聚的虚拟空间相关模型
职责：定义虚拟空间、场景、权限、成员等数据模型
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, AuditModel, StatusModel, MetadataModel
from django.contrib.auth import get_user_model

User = get_user_model()


class SpaceCategory(BaseModel):
    """空间分类模型"""
    
    name = models.CharField(max_length=100, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    icon_url = models.URLField(blank=True, verbose_name='图标URL')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='主题色')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    class Meta:
        db_table = 'spaces_categories'
        verbose_name = '空间分类'
        verbose_name_plural = '空间分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class Space(AuditModel, StatusModel, MetadataModel):
    """虚拟空间模型"""
    
    VISIBILITY_CHOICES = [
        ('public', '公开'),
        ('unlisted', '不公开列表'),
        ('private', '私有'),
    ]
    
    SPACE_TYPE_CHOICES = [
        ('social', '社交空间'),
        ('meeting', '会议室'),
        ('exhibition', '展览厅'),
        ('game', '游戏空间'),
        ('education', '教育空间'),
        ('custom', '自定义'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='空间名称')
    description = models.TextField(blank=True, verbose_name='空间描述')
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='owned_spaces',
        verbose_name='空间所有者'
    )
    category = models.ForeignKey(
        SpaceCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='spaces',
        verbose_name='空间分类'
    )
    
    # 空间属性
    space_type = models.CharField(
        max_length=20,
        choices=SPACE_TYPE_CHOICES,
        default='social',
        verbose_name='空间类型'
    )
    visibility = models.CharField(
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default='public',
        verbose_name='可见性'
    )
    
    # 容量设置
    max_capacity = models.PositiveIntegerField(
        default=50,
        validators=[MinValueValidator(1), MaxValueValidator(1000)],
        verbose_name='最大容量'
    )
    current_users = models.PositiveIntegerField(default=0, verbose_name='当前用户数')
    
    # 空间设置
    allow_voice_chat = models.BooleanField(default=True, verbose_name='允许语音聊天')
    allow_video_chat = models.BooleanField(default=True, verbose_name='允许视频聊天')
    allow_screen_share = models.BooleanField(default=True, verbose_name='允许屏幕共享')
    allow_file_upload = models.BooleanField(default=True, verbose_name='允许文件上传')
    allow_guest_access = models.BooleanField(default=True, verbose_name='允许访客访问')
    
    # 密码保护
    password = models.CharField(max_length=128, blank=True, verbose_name='访问密码')
    
    # 统计信息
    visit_count = models.PositiveIntegerField(default=0, verbose_name='访问次数')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    
    # 媒体资源
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    background_url = models.URLField(blank=True, verbose_name='背景图URL')
    
    # 地理位置（虚拟坐标）
    virtual_x = models.FloatField(default=0.0, verbose_name='虚拟X坐标')
    virtual_y = models.FloatField(default=0.0, verbose_name='虚拟Y坐标')
    virtual_z = models.FloatField(default=0.0, verbose_name='虚拟Z坐标')
    
    class Meta:
        db_table = 'spaces'
        verbose_name = '虚拟空间'
        verbose_name_plural = '虚拟空间'
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['category']),
            models.Index(fields=['space_type']),
            models.Index(fields=['visibility']),
            models.Index(fields=['status']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.owner.username})"
    
    @property
    def is_full(self):
        """检查空间是否已满"""
        return self.current_users >= self.max_capacity
    
    @property
    def occupancy_rate(self):
        """获取占用率"""
        if self.max_capacity == 0:
            return 0
        return (self.current_users / self.max_capacity) * 100
    
    def can_join(self, user):
        """检查用户是否可以加入空间"""
        if self.is_full:
            return False, "空间已满"
        
        if self.status != 'active':
            return False, "空间不可用"
        
        if self.visibility == 'private':
            # 检查是否为成员或有邀请
            if not self.members.filter(user=user, status='active').exists():
                return False, "需要邀请才能加入私有空间"
        
        return True, "可以加入"
    
    def increment_visit_count(self):
        """增加访问次数"""
        self.visit_count += 1
        self.save(update_fields=['visit_count'])
    
    def increment_like_count(self):
        """增加点赞数"""
        self.like_count += 1
        self.save(update_fields=['like_count'])
    
    def decrement_like_count(self):
        """减少点赞数"""
        if self.like_count > 0:
            self.like_count -= 1
            self.save(update_fields=['like_count'])


class SpaceMember(BaseModel):
    """空间成员模型"""
    
    ROLE_CHOICES = [
        ('owner', '所有者'),
        ('admin', '管理员'),
        ('moderator', '版主'),
        ('member', '成员'),
        ('guest', '访客'),
    ]
    
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('inactive', '非活跃'),
        ('banned', '被禁'),
        ('pending', '待审核'),
    ]
    
    space = models.ForeignKey(
        Space,
        on_delete=models.CASCADE,
        related_name='members',
        verbose_name='空间'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='space_memberships',
        verbose_name='用户'
    )
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='member',
        verbose_name='角色'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name='状态'
    )
    
    # 权限设置
    can_invite = models.BooleanField(default=False, verbose_name='可以邀请')
    can_kick = models.BooleanField(default=False, verbose_name='可以踢出')
    can_mute = models.BooleanField(default=False, verbose_name='可以禁言')
    can_edit_space = models.BooleanField(default=False, verbose_name='可以编辑空间')
    
    # 统计信息
    join_count = models.PositiveIntegerField(default=0, verbose_name='加入次数')
    total_time_spent = models.PositiveIntegerField(default=0, verbose_name='总停留时间(秒)')
    last_active_at = models.DateTimeField(null=True, blank=True, verbose_name='最后活跃时间')
    
    class Meta:
        db_table = 'space_members'
        verbose_name = '空间成员'
        verbose_name_plural = '空间成员'
        unique_together = ['space', 'user']
        indexes = [
            models.Index(fields=['space', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['role']),
        ]
    
    def __str__(self):
        return f"{self.user.username} in {self.space.name} ({self.role})"
    
    def has_permission(self, permission):
        """检查是否有指定权限"""
        permission_map = {
            'invite': self.can_invite,
            'kick': self.can_kick,
            'mute': self.can_mute,
            'edit_space': self.can_edit_space,
        }
        
        # 所有者和管理员有所有权限
        if self.role in ['owner', 'admin']:
            return True
        
        return permission_map.get(permission, False)


class SpaceScene(BaseModel, MetadataModel):
    """空间场景模型"""
    
    SCENE_TYPE_CHOICES = [
        ('default', '默认场景'),
        ('custom', '自定义场景'),
        ('template', '模板场景'),
    ]
    
    space = models.ForeignKey(
        Space,
        on_delete=models.CASCADE,
        related_name='scenes',
        verbose_name='空间'
    )
    name = models.CharField(max_length=100, verbose_name='场景名称')
    description = models.TextField(blank=True, verbose_name='场景描述')
    scene_type = models.CharField(
        max_length=20,
        choices=SCENE_TYPE_CHOICES,
        default='default',
        verbose_name='场景类型'
    )
    
    # 3D场景数据
    scene_data = models.JSONField(default=dict, verbose_name='场景数据')
    environment_url = models.URLField(blank=True, verbose_name='环境贴图URL')
    skybox_url = models.URLField(blank=True, verbose_name='天空盒URL')
    
    # 光照设置
    lighting_config = models.JSONField(default=dict, verbose_name='光照配置')
    
    # 物理设置
    physics_enabled = models.BooleanField(default=True, verbose_name='启用物理')
    gravity = models.FloatField(default=-9.81, verbose_name='重力')
    
    # 音频设置
    ambient_sound_url = models.URLField(blank=True, verbose_name='环境音效URL')
    sound_volume = models.FloatField(
        default=0.5,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name='音量'
    )
    
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_default = models.BooleanField(default=False, verbose_name='是否默认场景')
    
    class Meta:
        db_table = 'space_scenes'
        verbose_name = '空间场景'
        verbose_name_plural = '空间场景'
        indexes = [
            models.Index(fields=['space']),
            models.Index(fields=['scene_type']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.space.name} - {self.name}"
    
    def get_default_scene_data(self):
        """获取默认场景数据"""
        return {
            'objects': [],
            'spawn_points': [
                {'x': 0, 'y': 0, 'z': 0, 'rotation': {'x': 0, 'y': 0, 'z': 0}}
            ],
            'boundaries': {
                'min': {'x': -50, 'y': -10, 'z': -50},
                'max': {'x': 50, 'y': 50, 'z': 50}
            }
        }
    
    def get_default_lighting_config(self):
        """获取默认光照配置"""
        return {
            'ambient': {'color': '#404040', 'intensity': 0.4},
            'directional': {
                'color': '#ffffff',
                'intensity': 0.8,
                'position': {'x': 10, 'y': 10, 'z': 5}
            },
            'shadows': {'enabled': True, 'type': 'PCFSoft'}
        }


class SpaceObject(BaseModel, MetadataModel):
    """空间对象模型"""
    
    OBJECT_TYPE_CHOICES = [
        ('model', '3D模型'),
        ('image', '图片'),
        ('video', '视频'),
        ('audio', '音频'),
        ('text', '文本'),
        ('link', '链接'),
        ('portal', '传送门'),
        ('interactive', '交互对象'),
    ]
    
    scene = models.ForeignKey(
        SpaceScene,
        on_delete=models.CASCADE,
        related_name='objects',
        verbose_name='场景'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='创建者'
    )
    
    name = models.CharField(max_length=100, verbose_name='对象名称')
    object_type = models.CharField(
        max_length=20,
        choices=OBJECT_TYPE_CHOICES,
        verbose_name='对象类型'
    )
    
    # 资源URL
    resource_url = models.URLField(verbose_name='资源URL')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    
    # 3D变换
    position_x = models.FloatField(default=0.0, verbose_name='X坐标')
    position_y = models.FloatField(default=0.0, verbose_name='Y坐标')
    position_z = models.FloatField(default=0.0, verbose_name='Z坐标')
    
    rotation_x = models.FloatField(default=0.0, verbose_name='X旋转')
    rotation_y = models.FloatField(default=0.0, verbose_name='Y旋转')
    rotation_z = models.FloatField(default=0.0, verbose_name='Z旋转')
    
    scale_x = models.FloatField(default=1.0, verbose_name='X缩放')
    scale_y = models.FloatField(default=1.0, verbose_name='Y缩放')
    scale_z = models.FloatField(default=1.0, verbose_name='Z缩放')
    
    # 交互设置
    is_interactive = models.BooleanField(default=False, verbose_name='可交互')
    interaction_data = models.JSONField(default=dict, verbose_name='交互数据')
    
    # 物理属性
    has_physics = models.BooleanField(default=False, verbose_name='启用物理')
    is_static = models.BooleanField(default=True, verbose_name='静态对象')
    mass = models.FloatField(default=1.0, verbose_name='质量')
    
    # 可见性
    is_visible = models.BooleanField(default=True, verbose_name='可见')
    opacity = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name='透明度'
    )
    
    class Meta:
        db_table = 'space_objects'
        verbose_name = '空间对象'
        verbose_name_plural = '空间对象'
        indexes = [
            models.Index(fields=['scene']),
            models.Index(fields=['object_type']),
            models.Index(fields=['created_by']),
            models.Index(fields=['is_visible']),
        ]
    
    def __str__(self):
        return f"{self.scene.space.name} - {self.name}"
    
    @property
    def position(self):
        """获取位置向量"""
        return {'x': self.position_x, 'y': self.position_y, 'z': self.position_z}
    
    @property
    def rotation(self):
        """获取旋转向量"""
        return {'x': self.rotation_x, 'y': self.rotation_y, 'z': self.rotation_z}
    
    @property
    def scale(self):
        """获取缩放向量"""
        return {'x': self.scale_x, 'y': self.scale_y, 'z': self.scale_z}
    
    def set_position(self, x, y, z):
        """设置位置"""
        self.position_x = x
        self.position_y = y
        self.position_z = z
    
    def set_rotation(self, x, y, z):
        """设置旋转"""
        self.rotation_x = x
        self.rotation_y = y
        self.rotation_z = z
    
    def set_scale(self, x, y, z):
        """设置缩放"""
        self.scale_x = x
        self.scale_y = y
        self.scale_z = z


class SpaceInvitation(BaseModel):
    """空间邀请模型"""
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('accepted', '已接受'),
        ('declined', '已拒绝'),
        ('expired', '已过期'),
    ]
    
    space = models.ForeignKey(
        Space,
        on_delete=models.CASCADE,
        related_name='invitations',
        verbose_name='空间'
    )
    inviter = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_space_invitations',
        verbose_name='邀请者'
    )
    invitee = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_space_invitations',
        verbose_name='被邀请者'
    )
    
    message = models.TextField(blank=True, verbose_name='邀请消息')
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    expires_at = models.DateTimeField(verbose_name='过期时间')
    responded_at = models.DateTimeField(null=True, blank=True, verbose_name='响应时间')
    
    class Meta:
        db_table = 'space_invitations'
        verbose_name = '空间邀请'
        verbose_name_plural = '空间邀请'
        unique_together = ['space', 'invitee']
        indexes = [
            models.Index(fields=['inviter']),
            models.Index(fields=['invitee']),
            models.Index(fields=['status']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.inviter.username} -> {self.invitee.username} ({self.space.name})"
    
    @property
    def is_expired(self):
        """检查是否已过期"""
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def accept(self):
        """接受邀请"""
        if self.status == 'pending' and not self.is_expired:
            self.status = 'accepted'
            self.responded_at = timezone.now()
            self.save()
            
            # 创建空间成员
            SpaceMember.objects.get_or_create(
                space=self.space,
                user=self.invitee,
                defaults={'role': 'member', 'status': 'active'}
            )
            return True
        return False
    
    def decline(self):
        """拒绝邀请"""
        if self.status == 'pending':
            self.status = 'declined'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False


class SpaceLike(BaseModel):
    """空间点赞模型"""
    
    space = models.ForeignKey(
        Space,
        on_delete=models.CASCADE,
        related_name='likes',
        verbose_name='空间'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='space_likes',
        verbose_name='用户'
    )
    
    class Meta:
        db_table = 'space_likes'
        verbose_name = '空间点赞'
        verbose_name_plural = '空间点赞'
        unique_together = ['space', 'user']
        indexes = [
            models.Index(fields=['space']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"{self.user.username} likes {self.space.name}"


class SpaceVisit(BaseModel):
    """空间访问记录模型"""
    
    space = models.ForeignKey(
        Space,
        on_delete=models.CASCADE,
        related_name='visits',
        verbose_name='空间'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='space_visits',
        verbose_name='用户'
    )
    
    # 访问信息
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')
    duration = models.PositiveIntegerField(default=0, verbose_name='停留时间(秒)')
    
    # 进入和离开时间
    entered_at = models.DateTimeField(auto_now_add=True, verbose_name='进入时间')
    left_at = models.DateTimeField(null=True, blank=True, verbose_name='离开时间')
    
    class Meta:
        db_table = 'space_visits'
        verbose_name = '空间访问记录'
        verbose_name_plural = '空间访问记录'
        indexes = [
            models.Index(fields=['space', 'entered_at']),
            models.Index(fields=['user', 'entered_at']),
        ]
    
    def __str__(self):
        username = self.user.username if self.user else 'Anonymous'
        return f"{username} visited {self.space.name}"
    
    def calculate_duration(self):
        """计算停留时间"""
        if self.left_at:
            duration = (self.left_at - self.entered_at).total_seconds()
            self.duration = int(duration)
            self.save(update_fields=['duration'])
            return self.duration
        return 0