"""
内容权限控制 - 访问控制和权限验证
职责：定义内容相关的权限类，控制API访问权限
"""

from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView
from django.contrib.auth.models import AnonymousUser
from .models import Post, Comment, ContentCategory


class CanCreatePost(permissions.BasePermission):
    """
    可以创建帖子的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 检查用户状态
        if request.user.status != 'active':
            return False
        
        # 检查用户类型限制
        if hasattr(request.user, 'user_type'):
            banned_types = ['banned', 'suspended']
            if request.user.user_type in banned_types:
                return False
        
        return True


class CanEditPost(permissions.BasePermission):
    """
    可以编辑帖子的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        post = obj if isinstance(obj, Post) else obj
        
        # 只有作者可以编辑
        if post.author != request.user:
            return False
        
        # 检查帖子状态
        if post.status != 'active':
            return False
        
        # 检查是否已发布（发布后可能有编辑时间限制）
        if post.published_at:
            from django.utils import timezone
            from datetime import timedelta
            
            # 发布后1小时内可以编辑
            edit_deadline = post.published_at + timedelta(hours=1)
            if timezone.now() > edit_deadline:
                return False
        
        return True


class CanDeletePost(permissions.BasePermission):
    """
    可以删除帖子的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        post = obj if isinstance(obj, Post) else obj
        
        # 作者可以删除
        if post.author == request.user:
            return True
        
        # 管理员可以删除
        if request.user.is_staff or request.user.user_type in ['moderator', 'admin']:
            return True
        
        return False


class CanViewPost(permissions.BasePermission):
    """
    可以查看帖子的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return True  # 基本权限检查在对象级别
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        post = obj if isinstance(obj, Post) else obj
        
        # 检查帖子状态
        if post.status != 'active':
            # 只有作者和管理员可以查看非活跃帖子
            if request.user.is_authenticated:
                return (post.author == request.user or 
                       request.user.is_staff or 
                       request.user.user_type in ['moderator', 'admin'])
            return False
        
        # 检查审核状态
        if post.moderation_status != 'approved':
            # 只有作者和审核员可以查看未通过审核的帖子
            if request.user.is_authenticated:
                return (post.author == request.user or 
                       request.user.is_staff or 
                       request.user.user_type in ['moderator', 'admin'])
            return False
        
        # 检查可见性
        if post.visibility == 'public':
            return True
        elif post.visibility == 'private':
            return request.user.is_authenticated and post.author == request.user
        elif post.visibility == 'friends':
            if not request.user.is_authenticated:
                return False
            # 检查是否为好友关系
            return self._are_friends(post.author, request.user)
        elif post.visibility == 'space':
            # 检查是否为空间成员
            return self._is_space_member(post.related_space_id, request.user)
        
        return False
    
    def _are_friends(self, user1, user2) -> bool:
        """检查是否为好友关系"""
        from apps.social.models import Friendship
        from django.db.models import Q
        
        return Friendship.objects.filter(
            Q(requester=user1, addressee=user2) |
            Q(requester=user2, addressee=user1),
            status='accepted'
        ).exists()
    
    def _is_space_member(self, space_id: str, user) -> bool:
        """检查是否为空间成员"""
        if not space_id or not user.is_authenticated:
            return False
        
        # 这里需要调用空间服务检查成员关系
        # 简化实现，实际应该通过服务调用
        return True


class CanCommentOnPost(permissions.BasePermission):
    """
    可以评论帖子的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        post = obj if isinstance(obj, Post) else obj.post
        
        # 检查帖子是否允许评论
        if not post.allow_comments:
            return False
        
        # 检查是否可以查看帖子
        can_view_post = CanViewPost()
        if not can_view_post.has_object_permission(request, view, post):
            return False
        
        # 检查用户状态
        if request.user.status != 'active':
            return False
        
        return True


class CanEditComment(permissions.BasePermission):
    """
    可以编辑评论的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        comment = obj if isinstance(obj, Comment) else obj
        
        # 只有作者可以编辑
        if comment.author != request.user:
            return False
        
        # 检查评论状态
        if comment.status != 'active':
            return False
        
        # 检查时间限制（评论后30分钟内可以编辑）
        from django.utils import timezone
        from datetime import timedelta
        
        edit_deadline = comment.created_at + timedelta(minutes=30)
        if timezone.now() > edit_deadline:
            return False
        
        return True


class CanDeleteComment(permissions.BasePermission):
    """
    可以删除评论的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        comment = obj if isinstance(obj, Comment) else obj
        
        # 评论作者可以删除
        if comment.author == request.user:
            return True
        
        # 帖子作者可以删除评论
        if comment.post.author == request.user:
            return True
        
        # 管理员可以删除
        if request.user.is_staff or request.user.user_type in ['moderator', 'admin']:
            return True
        
        return False


class CanModerateContent(permissions.BasePermission):
    """
    可以审核内容的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 检查是否为管理员或审核员
        return (request.user.is_staff or 
                request.user.user_type in ['moderator', 'admin'])


class CanReportContent(permissions.BasePermission):
    """
    可以举报内容的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 不能举报自己的内容
        if hasattr(obj, 'author') and obj.author == request.user:
            return False
        
        # 检查是否可以查看内容
        if isinstance(obj, Post):
            can_view = CanViewPost()
            return can_view.has_object_permission(request, view, obj)
        
        return True


class CanManageCategory(permissions.BasePermission):
    """
    可以管理分类的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 只有管理员可以管理分类
        return request.user.is_staff or request.user.user_type == 'admin'


# 权限检查函数

def can_create_post(user) -> bool:
    """
    检查用户是否可以创建帖子
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否可以创建帖子
    """
    if isinstance(user, AnonymousUser):
        return False
    
    if user.status != 'active':
        return False
    
    if hasattr(user, 'user_type'):
        banned_types = ['banned', 'suspended']
        if user.user_type in banned_types:
            return False
    
    return True


def can_edit_post(user, post: Post) -> bool:
    """
    检查用户是否可以编辑帖子
    
    Args:
        user: 用户对象
        post: 帖子对象
        
    Returns:
        bool: 是否可以编辑
    """
    if isinstance(user, AnonymousUser):
        return False
    
    # 只有作者可以编辑
    if post.author != user:
        return False
    
    # 检查帖子状态
    if post.status != 'active':
        return False
    
    # 检查编辑时间限制
    if post.published_at:
        from django.utils import timezone
        from datetime import timedelta
        
        edit_deadline = post.published_at + timedelta(hours=1)
        if timezone.now() > edit_deadline:
            return False
    
    return True


def can_delete_post(user, post: Post) -> bool:
    """
    检查用户是否可以删除帖子
    
    Args:
        user: 用户对象
        post: 帖子对象
        
    Returns:
        bool: 是否可以删除
    """
    if isinstance(user, AnonymousUser):
        return False
    
    # 作者可以删除
    if post.author == user:
        return True
    
    # 管理员可以删除
    if user.is_staff or user.user_type in ['moderator', 'admin']:
        return True
    
    return False


def can_view_post(user, post: Post) -> bool:
    """
    检查用户是否可以查看帖子
    
    Args:
        user: 用户对象
        post: 帖子对象
        
    Returns:
        bool: 是否可以查看
    """
    # 检查帖子状态
    if post.status != 'active':
        if isinstance(user, AnonymousUser):
            return False
        return (post.author == user or 
               user.is_staff or 
               user.user_type in ['moderator', 'admin'])
    
    # 检查审核状态
    if post.moderation_status != 'approved':
        if isinstance(user, AnonymousUser):
            return False
        return (post.author == user or 
               user.is_staff or 
               user.user_type in ['moderator', 'admin'])
    
    # 检查可见性
    if post.visibility == 'public':
        return True
    elif post.visibility == 'private':
        return not isinstance(user, AnonymousUser) and post.author == user
    elif post.visibility == 'friends':
        if isinstance(user, AnonymousUser):
            return False
        # 检查好友关系
        from apps.social.models import Friendship
        from django.db.models import Q
        
        return Friendship.objects.filter(
            Q(requester=post.author, addressee=user) |
            Q(requester=user, addressee=post.author),
            status='accepted'
        ).exists()
    
    return False


def can_moderate_content(user) -> bool:
    """
    检查用户是否可以审核内容
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否可以审核
    """
    if isinstance(user, AnonymousUser):
        return False
    
    return user.is_staff or user.user_type in ['moderator', 'admin']


def get_user_content_permissions(user, post: Post) -> dict:
    """
    获取用户对内容的权限
    
    Args:
        user: 用户对象
        post: 帖子对象
        
    Returns:
        dict: 权限字典
    """
    if isinstance(user, AnonymousUser):
        return {
            'can_view': can_view_post(user, post),
            'can_edit': False,
            'can_delete': False,
            'can_comment': False,
            'can_like': False,
            'can_report': False,
            'can_moderate': False
        }
    
    return {
        'can_view': can_view_post(user, post),
        'can_edit': can_edit_post(user, post),
        'can_delete': can_delete_post(user, post),
        'can_comment': post.allow_comments and user.status == 'active',
        'can_like': user.status == 'active',
        'can_report': post.author != user and user.status == 'active',
        'can_moderate': can_moderate_content(user)
    }
