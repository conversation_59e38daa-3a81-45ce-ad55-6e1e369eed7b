"""
内容域信号处理器
处理内容相关的Django信号
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Post, Comment


@receiver(post_save, sender=Post)
def handle_post_created(sender, instance, created, **kwargs):
    """
    处理帖子创建
    """
    if created:
        # 可以在这里添加帖子创建后的逻辑
        # 比如内容审核、推荐算法更新等
        pass


@receiver(post_save, sender=Comment)
def handle_comment_created(sender, instance, created, **kwargs):
    """
    处理评论创建
    """
    if created:
        # 更新帖子的评论数
        post = instance.post
        post.comment_count = post.comments.filter(status='active').count()
        post.save()
        
        # 可以在这里添加其他逻辑
        # 比如发送通知给帖子作者


@receiver(post_delete, sender=Comment)
def handle_comment_deleted(sender, instance, **kwargs):
    """
    处理评论删除
    """
    # 更新帖子的评论数
    post = instance.post
    post.comment_count = post.comments.filter(status='active').count()
    post.save()


@receiver(post_delete, sender=Post)
def handle_post_deleted(sender, instance, **kwargs):
    """
    处理帖子删除
    """
    # 可以在这里添加帖子删除后的逻辑
    pass
