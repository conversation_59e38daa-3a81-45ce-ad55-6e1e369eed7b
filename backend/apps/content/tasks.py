"""
内容异步任务 - 后台处理任务
职责：处理内容相关的异步任务，如内容审核、推荐生成、统计更新等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Q, F
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def auto_moderate_content(self, post_id: int):
    """
    自动审核内容
    
    Args:
        post_id: 帖子ID
    """
    try:
        from .models import Post
        from .services import ContentService
        
        post = Post.objects.get(id=post_id)
        
        # 简单的自动审核逻辑
        auto_approve = True
        rejection_reason = ""
        
        # 检查内容长度
        if len(post.content) < 10:
            auto_approve = False
            rejection_reason = "内容过短"
        
        # 检查敏感词
        sensitive_words = ['spam', 'fake', 'scam', 'illegal']
        content_lower = post.content.lower()
        title_lower = post.title.lower()
        
        for word in sensitive_words:
            if word in content_lower or word in title_lower:
                auto_approve = False
                rejection_reason = f"包含敏感词: {word}"
                break
        
        # 检查用户历史记录
        user_posts = Post.objects.filter(author=post.author)
        if user_posts.count() > 0:
            rejected_rate = user_posts.filter(moderation_status='rejected').count() / user_posts.count()
            if rejected_rate > 0.3:  # 拒绝率超过30%
                auto_approve = False
                rejection_reason = "用户历史记录异常"
        
        # 执行审核
        if auto_approve:
            post.moderation_status = 'approved'
            post.published_at = timezone.now()
            post.moderated_at = timezone.now()
            post.save()
            
            # 发送通知
            send_content_notification.delay(
                post.author.id,
                'post_approved',
                {
                    'post_id': post.id,
                    'post_title': post.title
                }
            )
            
            logger.info(f"帖子自动审核通过: {post.id}")
        else:
            post.moderation_status = 'flagged'
            post.moderation_reason = rejection_reason
            post.moderated_at = timezone.now()
            post.save()
            
            logger.info(f"帖子需要人工审核: {post.id} - {rejection_reason}")
        
    except Exception as exc:
        logger.error(f"自动审核内容失败: {post_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def send_content_notification(self, user_id: int, notification_type: str, data: dict):
    """
    发送内容通知
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        data: 通知数据
    """
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 检查用户通知偏好
        if hasattr(user, 'preferences'):
            notification_settings = user.preferences.notification_settings
            if not notification_settings.get('content_notifications', True):
                logger.info(f"用户已关闭内容通知: {user.username}")
                return
        
        # 根据通知类型选择模板和内容
        template_map = {
            'post_approved': {
                'template': 'emails/post_approved.html',
                'subject': '您的帖子已通过审核'
            },
            'post_rejected': {
                'template': 'emails/post_rejected.html',
                'subject': '您的帖子未通过审核'
            },
            'comment_received': {
                'template': 'emails/comment_received.html',
                'subject': '您的帖子收到新评论'
            },
            'post_liked': {
                'template': 'emails/post_liked.html',
                'subject': '您的帖子收到点赞'
            }
        }
        
        template_info = template_map.get(notification_type)
        if not template_info:
            logger.error(f"未知的通知类型: {notification_type}")
            return
        
        # 渲染邮件模板
        html_message = render_to_string(template_info['template'], {
            'user': user,
            'data': data,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=template_info['subject'],
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"内容通知发送成功: {user.email} - {notification_type}")
        
    except Exception as exc:
        logger.error(f"发送内容通知失败: {user_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task
def generate_content_recommendations():
    """
    生成内容推荐
    """
    try:
        from django.contrib.auth import get_user_model
        from .models import Post, PostLike, Comment
        from .services import ContentService
        
        User = get_user_model()
        content_service = ContentService()
        
        # 获取活跃用户（最近7天有活动）
        seven_days_ago = timezone.now() - timedelta(days=7)
        active_users = User.objects.filter(
            Q(post_likes__created_at__gte=seven_days_ago) |
            Q(comments__created_at__gte=seven_days_ago),
            is_active=True
        ).distinct()[:1000]  # 限制处理数量
        
        total_generated = 0
        for user in active_users:
            try:
                # 为每个用户生成推荐
                recommendations = content_service.get_recommended_posts(str(user.uuid), 20)
                
                # 存储推荐结果到缓存或数据库
                # 这里可以使用Redis或数据库存储推荐结果
                
                total_generated += len(recommendations)
                
            except Exception as e:
                logger.error(f"为用户 {user.username} 生成推荐失败: {e}")
                continue
        
        logger.info(f"内容推荐生成完成: 处理了 {len(active_users)} 个用户，生成了 {total_generated} 个推荐")
        
        return total_generated
        
    except Exception as exc:
        logger.error(f"生成内容推荐失败: {exc}")
        return 0


@shared_task
def update_content_statistics():
    """
    更新内容统计信息
    """
    try:
        from .models import Post, Comment, ContentCategory
        
        # 更新帖子统计
        posts = Post.objects.filter(status='active')
        
        for post in posts:
            try:
                # 更新点赞数
                like_count = post.likes.count()
                post.like_count = like_count
                
                # 更新评论数
                comment_count = post.comments.filter(status='active').count()
                post.comment_count = comment_count
                
                post.save(update_fields=['like_count', 'comment_count'])
                
            except Exception as e:
                logger.error(f"更新帖子 {post.id} 统计失败: {e}")
                continue
        
        # 更新分类统计
        categories = ContentCategory.objects.filter(status='active')
        
        for category in categories:
            try:
                post_count = Post.objects.filter(
                    category=category,
                    status='active',
                    moderation_status='approved'
                ).count()
                
                category.post_count = post_count
                category.save(update_fields=['post_count'])
                
            except Exception as e:
                logger.error(f"更新分类 {category.id} 统计失败: {e}")
                continue
        
        logger.info("内容统计更新完成")
        
    except Exception as exc:
        logger.error(f"更新内容统计失败: {exc}")


@shared_task
def cleanup_old_content():
    """
    清理旧内容
    """
    try:
        from .models import Post, Comment, PostView
        
        # 清理已删除的帖子（30天前）
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_posts = Post.objects.filter(
            status='deleted',
            updated_at__lt=cutoff_date
        )
        
        deleted_count = deleted_posts.count()
        deleted_posts.delete()
        
        # 清理已删除的评论（30天前）
        deleted_comments = Comment.objects.filter(
            status='deleted',
            updated_at__lt=cutoff_date
        )
        
        deleted_comment_count = deleted_comments.count()
        deleted_comments.delete()
        
        # 清理旧的浏览记录（90天前）
        old_cutoff_date = timezone.now() - timedelta(days=90)
        old_views = PostView.objects.filter(
            created_at__lt=old_cutoff_date
        )
        
        deleted_view_count = old_views.count()
        old_views.delete()
        
        logger.info(f"内容清理完成: 删除了 {deleted_count} 个帖子，{deleted_comment_count} 个评论，{deleted_view_count} 个浏览记录")
        
        return {
            'deleted_posts': deleted_count,
            'deleted_comments': deleted_comment_count,
            'deleted_views': deleted_view_count
        }
        
    except Exception as exc:
        logger.error(f"清理旧内容失败: {exc}")
        return {'deleted_posts': 0, 'deleted_comments': 0, 'deleted_views': 0}


@shared_task
def analyze_content_trends():
    """
    分析内容趋势
    """
    try:
        from .models import Post, ContentCategory
        from django.db.models import Count
        
        # 分析最近7天的内容趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        
        # 热门标签
        popular_tags = []
        posts_with_tags = Post.objects.filter(
            published_at__gte=seven_days_ago,
            status='active',
            moderation_status='approved'
        ).exclude(tags=[])
        
        all_tags = []
        for post in posts_with_tags:
            all_tags.extend(post.tags)
        
        from collections import Counter
        tag_counts = Counter(all_tags)
        popular_tags = [{'tag': tag, 'count': count} for tag, count in tag_counts.most_common(20)]
        
        # 热门分类
        popular_categories = ContentCategory.objects.filter(
            posts__published_at__gte=seven_days_ago,
            posts__status='active',
            posts__moderation_status='approved'
        ).annotate(
            post_count=Count('posts')
        ).order_by('-post_count')[:10]
        
        # 内容类型分布
        content_types = Post.objects.filter(
            published_at__gte=seven_days_ago,
            status='active',
            moderation_status='approved'
        ).values('post_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 生成趋势报告
        trend_report = {
            'period': {
                'start_date': seven_days_ago.isoformat(),
                'end_date': timezone.now().isoformat()
            },
            'popular_tags': popular_tags,
            'popular_categories': [
                {
                    'name': cat.name,
                    'post_count': cat.post_count
                } for cat in popular_categories
            ],
            'content_types': list(content_types),
            'generated_at': timezone.now().isoformat()
        }
        
        # 存储趋势报告（可以存储到缓存或数据库）
        # cache.set('content_trends_weekly', trend_report, 60 * 60 * 24)  # 缓存24小时
        
        logger.info("内容趋势分析完成")
        
        return trend_report
        
    except Exception as exc:
        logger.error(f"分析内容趋势失败: {exc}")
        return None


@shared_task
def process_content_reports():
    """
    处理内容举报
    """
    try:
        from .models import ContentReport, Post, Comment
        
        # 获取待处理的举报
        pending_reports = ContentReport.objects.filter(
            status='pending'
        ).select_related('post', 'comment', 'reporter')
        
        processed_count = 0
        for report in pending_reports:
            try:
                # 简单的自动处理逻辑
                should_auto_handle = False
                action = None
                
                # 检查举报数量
                if report.post:
                    report_count = ContentReport.objects.filter(
                        post=report.post,
                        status__in=['pending', 'reviewing']
                    ).count()
                    
                    if report_count >= 5:  # 5个以上举报自动处理
                        should_auto_handle = True
                        action = 'flag_content'
                
                elif report.comment:
                    report_count = ContentReport.objects.filter(
                        comment=report.comment,
                        status__in=['pending', 'reviewing']
                    ).count()
                    
                    if report_count >= 3:  # 3个以上举报自动处理
                        should_auto_handle = True
                        action = 'flag_content'
                
                if should_auto_handle:
                    # 标记内容
                    if report.post:
                        report.post.moderation_status = 'flagged'
                        report.post.save()
                    elif report.comment:
                        report.comment.moderation_status = 'flagged'
                        report.comment.save()
                    
                    # 更新举报状态
                    report.status = 'resolved'
                    report.handled_at = timezone.now()
                    report.handle_result = f"自动处理: {action}"
                    report.save()
                    
                    processed_count += 1
                else:
                    # 标记为需要人工审核
                    report.status = 'reviewing'
                    report.save()
                
            except Exception as e:
                logger.error(f"处理举报 {report.id} 失败: {e}")
                continue
        
        logger.info(f"内容举报处理完成: 自动处理了 {processed_count} 个举报")
        
        return processed_count
        
    except Exception as exc:
        logger.error(f"处理内容举报失败: {exc}")
        return 0


# 事件处理器

from apps.core.events import event_handler

@event_handler('content.post_created', async_handler=True)
def handle_post_created(event):
    """处理帖子创建事件"""
    post_id = event.data.get('post_id')
    moderation_status = event.data.get('moderation_status')
    
    if post_id and moderation_status == 'pending':
        # 启动自动审核
        auto_moderate_content.delay(int(post_id))


@event_handler('content.comment_created', async_handler=True)
def handle_comment_created(event):
    """处理评论创建事件"""
    comment_id = event.data.get('comment_id')
    post_id = event.data.get('post_id')
    author_id = event.data.get('author_id')
    
    if comment_id and post_id:
        # 通知帖子作者
        from .models import Post
        
        try:
            post = Post.objects.get(uuid=post_id)
            if str(post.author.uuid) != author_id:  # 不是自己评论自己的帖子
                send_content_notification.delay(
                    post.author.id,
                    'comment_received',
                    {
                        'post_id': post.id,
                        'post_title': post.title,
                        'commenter_username': event.data.get('author_username')
                    }
                )
        except Post.DoesNotExist:
            pass


@event_handler('content.post_liked', async_handler=True)
def handle_post_liked(event):
    """处理帖子点赞事件"""
    post_id = event.data.get('post_id')
    post_author_id = event.data.get('post_author_id')
    liker_id = event.data.get('liker_id')
    
    if post_id and post_author_id != liker_id:  # 不是自己点赞自己的帖子
        # 通知帖子作者
        from .models import Post
        
        try:
            post = Post.objects.get(uuid=post_id)
            send_content_notification.delay(
                post.author.id,
                'post_liked',
                {
                    'post_id': post.id,
                    'post_title': post.title,
                    'liker_username': event.data.get('liker_username'),
                    'like_count': event.data.get('like_count')
                }
            )
        except Post.DoesNotExist:
            pass


@event_handler('content.post_moderated', async_handler=True)
def handle_post_moderated(event):
    """处理帖子审核事件"""
    post_id = event.data.get('post_id')
    author_id = event.data.get('author_id')
    action = event.data.get('action')
    
    if post_id and author_id:
        # 通知作者审核结果
        notification_type = 'post_approved' if action == 'approve' else 'post_rejected'
        
        from .models import Post
        
        try:
            post = Post.objects.get(uuid=post_id)
            send_content_notification.delay(
                post.author.id,
                notification_type,
                {
                    'post_id': post.id,
                    'post_title': post.title,
                    'reason': event.data.get('reason', '')
                }
            )
        except Post.DoesNotExist:
            pass
