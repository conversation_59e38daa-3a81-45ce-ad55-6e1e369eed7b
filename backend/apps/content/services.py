"""
内容业务服务 - 高内聚的用户生成内容业务逻辑
职责：处理内容发布、审核流程、推荐算法、搜索功能等核心业务逻辑
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Avg, F, Case, When
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent
from apps.core.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    ForbiddenException,
    ConflictException
)
from apps.core.dependencies import inject
from .models import (
    Post, Comment, PostLike, CommentLike, ContentCategory,
    MediaFile, PostView, ContentReport
)

logger = logging.getLogger(__name__)


class ContentService:
    """
    内容业务服务 - 高内聚的内容相关业务逻辑
    
    职责：
    1. 内容发布和管理
    2. 评论系统管理
    3. 内容审核流程
    4. 推荐算法实现
    5. 内容搜索功能
    """
    
    @transaction.atomic
    def create_post(self, author_id: str, post_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建帖子
        
        Args:
            author_id: 作者ID
            post_data: 帖子数据
            
        Returns:
            Dict: 创建的帖子信息
            
        Raises:
            ValidationException: 数据验证失败
            ForbiddenException: 无权创建帖子
        """
        logger.info(f"创建帖子: {author_id}")
        
        # 验证作者
        author = self._get_user_by_id(author_id)
        
        # 检查发布权限
        self._check_post_creation_permission(author, post_data)
        
        # 验证帖子数据
        self._validate_post_data(post_data)
        
        # 创建帖子
        post = self._create_post_entity(author, post_data)
        
        # 处理媒体文件
        self._process_post_media(post, post_data.get('media_urls', []))
        
        # 更新分类统计
        if post.category:
            post.category.increment_post_count()
        
        # 发布帖子创建事件
        self._publish_post_created_event(post)
        
        logger.info(f"帖子创建成功: {post.id}")
        
        return {
            'post_id': str(post.uuid),
            'title': post.title,
            'post_type': post.post_type,
            'moderation_status': post.moderation_status,
            'created_at': post.created_at.isoformat()
        }
    
    def get_post_by_id(self, post_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        根据ID获取帖子详情
        
        Args:
            post_id: 帖子ID
            user_id: 请求用户ID
            
        Returns:
            Dict: 帖子详情
        """
        try:
            post = Post.objects.select_related('author', 'category').get(uuid=post_id)
        except Post.DoesNotExist:
            raise ResourceNotFoundException(f"帖子不存在: {post_id}", "POST")
        
        # 权限检查
        if not self._can_access_post(post, user_id):
            raise ForbiddenException("无权访问该帖子")
        
        # 记录浏览
        if user_id:
            self._record_post_view(post, user_id)
        
        return self._serialize_post_detail(post, user_id)
    
    def get_posts(self, filters: Dict[str, Any] = None, 
                 page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取帖子列表
        
        Args:
            filters: 过滤条件
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 帖子列表
        """
        filters = filters or {}
        
        # 构建查询
        queryset = Post.objects.filter(
            status='active',
            moderation_status='approved',
            published_at__isnull=False
        ).select_related('author', 'category')
        
        # 应用过滤器
        queryset = self._apply_post_filters(queryset, filters)
        
        # 排序
        order_by = filters.get('order_by', '-published_at')
        queryset = queryset.order_by(order_by)
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化帖子
        posts = []
        for post in page_obj.object_list:
            post_data = self._serialize_post_summary(post, filters.get('user_id'))
            posts.append(post_data)
        
        return {
            'posts': posts,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
    
    @transaction.atomic
    def create_comment(self, author_id: str, post_id: str, 
                      comment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建评论
        
        Args:
            author_id: 作者ID
            post_id: 帖子ID
            comment_data: 评论数据
            
        Returns:
            Dict: 创建的评论信息
        """
        logger.info(f"创建评论: {author_id} -> {post_id}")
        
        # 验证作者和帖子
        author = self._get_user_by_id(author_id)
        post = self._get_post_by_id(post_id)
        
        # 检查评论权限
        self._check_comment_permission(author, post)
        
        # 验证评论数据
        self._validate_comment_data(comment_data)
        
        # 创建评论
        comment = self._create_comment_entity(author, post, comment_data)
        
        # 更新帖子评论数
        post.increment_comment_count()
        
        # 如果是回复，更新父评论回复数
        if comment.parent:
            comment.parent.increment_reply_count()
        
        # 发布评论创建事件
        self._publish_comment_created_event(comment)
        
        logger.info(f"评论创建成功: {comment.id}")
        
        return {
            'comment_id': str(comment.uuid),
            'post_id': str(post.uuid),
            'content': comment.content,
            'is_reply': comment.is_reply,
            'created_at': comment.created_at.isoformat()
        }
    
    @transaction.atomic
    def like_post(self, user_id: str, post_id: str) -> Dict[str, Any]:
        """
        点赞帖子
        
        Args:
            user_id: 用户ID
            post_id: 帖子ID
            
        Returns:
            Dict: 点赞结果
        """
        user = self._get_user_by_id(user_id)
        post = self._get_post_by_id(post_id)
        
        # 检查是否已点赞
        like, created = PostLike.objects.get_or_create(
            post=post,
            user=user
        )
        
        if created:
            post.increment_like_count()
            
            # 发布点赞事件
            self._publish_post_liked_event(post, user)
            
            return {'liked': True, 'message': '点赞成功'}
        else:
            return {'liked': False, 'message': '您已经点赞过了'}
    
    @transaction.atomic
    def unlike_post(self, user_id: str, post_id: str) -> Dict[str, Any]:
        """
        取消点赞帖子
        
        Args:
            user_id: 用户ID
            post_id: 帖子ID
            
        Returns:
            Dict: 取消点赞结果
        """
        user = self._get_user_by_id(user_id)
        post = self._get_post_by_id(post_id)
        
        try:
            like = PostLike.objects.get(post=post, user=user)
            like.delete()
            post.decrement_like_count()
            
            return {'unliked': True, 'message': '取消点赞成功'}
            
        except PostLike.DoesNotExist:
            return {'unliked': False, 'message': '您还没有点赞过'}
    
    def search_posts(self, query: str, filters: Dict[str, Any] = None,
                    page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        搜索帖子
        
        Args:
            query: 搜索关键词
            filters: 过滤条件
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 搜索结果
        """
        filters = filters or {}
        
        # 构建搜索查询
        queryset = Post.objects.filter(
            status='active',
            moderation_status='approved',
            published_at__isnull=False
        ).select_related('author', 'category')
        
        # 搜索条件
        if query:
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(content__icontains=query) |
                Q(tags__contains=[query])
            )
        
        # 应用过滤器
        queryset = self._apply_post_filters(queryset, filters)
        
        # 按相关性排序（简化版）
        queryset = queryset.order_by('-created_at')
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化结果
        posts = []
        for post in page_obj.object_list:
            post_data = self._serialize_post_summary(post, filters.get('user_id'))
            posts.append(post_data)
        
        return {
            'posts': posts,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            },
            'query': query
        }
    
    def get_recommended_posts(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取推荐帖子
        
        Args:
            user_id: 用户ID
            limit: 推荐数量
            
        Returns:
            List: 推荐帖子列表
        """
        user = self._get_user_by_id(user_id)
        
        # 获取用户兴趣标签（基于点赞和评论的帖子）
        user_interests = self._get_user_interests(user)
        
        # 获取用户关注的人的帖子
        following_posts = self._get_following_posts(user)
        
        # 获取热门帖子
        trending_posts = self._get_trending_posts()
        
        # 基于兴趣的推荐
        interest_posts = self._get_posts_by_interests(user_interests, user)
        
        # 合并和排序推荐结果
        recommended_posts = self._merge_recommendations(
            following_posts, trending_posts, interest_posts, limit
        )
        
        return [
            self._serialize_post_summary(post, user_id) 
            for post in recommended_posts
        ]
    
    @transaction.atomic
    def moderate_post(self, moderator_id: str, post_id: str, 
                     action: str, reason: str = '') -> Dict[str, Any]:
        """
        审核帖子
        
        Args:
            moderator_id: 审核者ID
            post_id: 帖子ID
            action: 审核动作 ('approve', 'reject', 'flag')
            reason: 审核原因
            
        Returns:
            Dict: 审核结果
        """
        moderator = self._get_user_by_id(moderator_id)
        post = self._get_post_by_id(post_id)
        
        # 检查审核权限
        if not self._can_moderate_content(moderator):
            raise ForbiddenException("无权审核内容")
        
        # 执行审核动作
        if action == 'approve':
            post.moderation_status = 'approved'
            if not post.published_at:
                post.published_at = timezone.now()
        elif action == 'reject':
            post.moderation_status = 'rejected'
            post.status = 'inactive'
        elif action == 'flag':
            post.moderation_status = 'flagged'
        else:
            raise ValidationException(f"无效的审核动作: {action}")
        
        post.moderation_reason = reason
        post.moderated_at = timezone.now()
        post.moderated_by = moderator
        post.save()
        
        # 发布审核事件
        self._publish_post_moderated_event(post, moderator, action)
        
        logger.info(f"帖子审核完成: {post.id} - {action}")
        
        return {
            'post_id': str(post.uuid),
            'action': action,
            'moderation_status': post.moderation_status,
            'moderated_at': post.moderated_at.isoformat()
        }
    
    def get_content_statistics(self, user_id: str = None) -> Dict[str, Any]:
        """
        获取内容统计信息
        
        Args:
            user_id: 用户ID（可选）
            
        Returns:
            Dict: 统计信息
        """
        if user_id:
            # 用户内容统计
            user = self._get_user_by_id(user_id)
            
            post_count = Post.objects.filter(author=user, status='active').count()
            comment_count = Comment.objects.filter(author=user, status='active').count()
            total_likes = Post.objects.filter(author=user, status='active').aggregate(
                total=models.Sum('like_count')
            )['total'] or 0
            total_views = Post.objects.filter(author=user, status='active').aggregate(
                total=models.Sum('view_count')
            )['total'] or 0
            
            return {
                'user_id': str(user.uuid),
                'post_count': post_count,
                'comment_count': comment_count,
                'total_likes_received': total_likes,
                'total_views': total_views
            }
        else:
            # 全站内容统计
            total_posts = Post.objects.filter(status='active').count()
            total_comments = Comment.objects.filter(status='active').count()
            total_categories = ContentCategory.objects.filter(status='active').count()
            
            # 待审核内容
            pending_posts = Post.objects.filter(moderation_status='pending').count()
            
            return {
                'total_posts': total_posts,
                'total_comments': total_comments,
                'total_categories': total_categories,
                'pending_moderation': pending_posts
            }
    
    # 私有方法
    
    def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            return User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
    
    def _get_post_by_id(self, post_id: str):
        """根据ID获取帖子"""
        try:
            return Post.objects.get(uuid=post_id)
        except Post.DoesNotExist:
            raise ResourceNotFoundException(f"帖子不存在: {post_id}", "POST")
    
    def _check_post_creation_permission(self, author, post_data: Dict[str, Any]):
        """检查帖子创建权限"""
        # 检查用户状态
        if author.status != 'active':
            raise ForbiddenException("用户状态异常，无法发布内容")
        
        # 检查发布频率限制
        recent_posts = Post.objects.filter(
            author=author,
            created_at__gte=timezone.now() - timedelta(minutes=5)
        ).count()
        
        if recent_posts >= 3:
            raise ForbiddenException("发布过于频繁，请稍后再试")
    
    def _validate_post_data(self, data: Dict[str, Any]):
        """验证帖子数据"""
        required_fields = ['title', 'content', 'post_type']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )
        
        # 验证标题长度
        title = data['title']
        if len(title) < 5 or len(title) > 200:
            raise ValidationException("标题长度必须在5-200个字符之间")
        
        # 验证内容长度
        content = data['content']
        if len(content) < 10 or len(content) > 50000:
            raise ValidationException("内容长度必须在10-50000个字符之间")

    def _create_post_entity(self, author, data: Dict[str, Any]) -> Post:
        """创建帖子实体"""
        # 获取分类
        category = None
        if data.get('category_id'):
            try:
                category = ContentCategory.objects.get(uuid=data['category_id'])
            except ContentCategory.DoesNotExist:
                pass

        # 确定审核状态
        moderation_status = 'pending'
        if self._is_trusted_user(author):
            moderation_status = 'approved'

        return Post.objects.create(
            title=data['title'],
            content=data['content'],
            author=author,
            post_type=data['post_type'],
            visibility=data.get('visibility', 'public'),
            category=category,
            tags=data.get('tags', []),
            media_urls=data.get('media_urls', []),
            thumbnail_url=data.get('thumbnail_url', ''),
            link_url=data.get('link_url', ''),
            link_title=data.get('link_title', ''),
            link_description=data.get('link_description', ''),
            link_image_url=data.get('link_image_url', ''),
            related_space_id=data.get('related_space_id', ''),
            related_group_id=data.get('related_group_id', ''),
            allow_comments=data.get('allow_comments', True),
            moderation_status=moderation_status,
            published_at=timezone.now() if moderation_status == 'approved' else None,
            created_by=author,
            updated_by=author
        )

    def _process_post_media(self, post: Post, media_urls: List[str]):
        """处理帖子媒体文件"""
        for media_url in media_urls:
            try:
                media_file = MediaFile.objects.get(file_url=media_url)
                media_file.increment_usage_count()
            except MediaFile.DoesNotExist:
                logger.warning(f"媒体文件不存在: {media_url}")
                continue

    def _can_access_post(self, post: Post, user_id: str = None) -> bool:
        """检查是否可以访问帖子"""
        # 检查帖子状态
        if post.status != 'active':
            return False

        # 检查审核状态
        if post.moderation_status != 'approved':
            # 只有作者和审核员可以查看未通过审核的帖子
            if user_id:
                user = self._get_user_by_id(user_id)
                return post.author == user or self._can_moderate_content(user)
            return False

        # 检查可见性
        if post.visibility == 'public':
            return True
        elif post.visibility == 'private':
            return user_id and post.author.uuid == user_id
        elif post.visibility == 'friends':
            if not user_id:
                return False
            # 检查是否为好友关系
            return self._are_friends(post.author.uuid, user_id)

        return True

    def _record_post_view(self, post: Post, user_id: str):
        """记录帖子浏览"""
        user = self._get_user_by_id(user_id)

        # 避免重复记录（同一用户同一天只记录一次）
        today = timezone.now().date()
        if not PostView.objects.filter(
            post=post,
            user=user,
            created_at__date=today
        ).exists():
            PostView.objects.create(
                post=post,
                user=user,
                ip_address='127.0.0.1'  # 实际应用中从请求中获取
            )
            post.increment_view_count()

    def _serialize_post_detail(self, post: Post, user_id: str = None) -> Dict[str, Any]:
        """序列化帖子详情"""
        data = {
            'id': str(post.uuid),
            'title': post.title,
            'content': post.content,
            'author': {
                'id': str(post.author.uuid),
                'username': post.author.username,
                'display_name': post.author.display_name,
                'avatar_url': getattr(post.author.profile, 'avatar_url', '') if hasattr(post.author, 'profile') else ''
            },
            'post_type': post.post_type,
            'visibility': post.visibility,
            'category': {
                'id': str(post.category.uuid),
                'name': post.category.name,
                'slug': post.category.slug
            } if post.category else None,
            'tags': post.tags,
            'media_urls': post.media_urls,
            'thumbnail_url': post.thumbnail_url,
            'statistics': {
                'view_count': post.view_count,
                'like_count': post.like_count,
                'comment_count': post.comment_count,
                'share_count': post.share_count
            },
            'settings': {
                'allow_comments': post.allow_comments,
                'is_pinned': post.is_pinned,
                'is_featured': post.is_featured
            },
            'moderation': {
                'status': post.moderation_status,
                'reason': post.moderation_reason
            },
            'published_at': post.published_at.isoformat() if post.published_at else None,
            'created_at': post.created_at.isoformat(),
            'updated_at': post.updated_at.isoformat()
        }

        # 添加链接信息
        if post.link_url:
            data['link'] = {
                'url': post.link_url,
                'title': post.link_title,
                'description': post.link_description,
                'image_url': post.link_image_url
            }

        # 添加用户相关信息
        if user_id:
            user = self._get_user_by_id(user_id)
            data['user_liked'] = PostLike.objects.filter(post=post, user=user).exists()
            data['user_can_edit'] = post.author == user
            data['user_can_delete'] = post.author == user or self._can_moderate_content(user)

        return data

    def _serialize_post_summary(self, post: Post, user_id: str = None) -> Dict[str, Any]:
        """序列化帖子摘要"""
        data = {
            'id': str(post.uuid),
            'title': post.title,
            'content': post.content[:200] + '...' if len(post.content) > 200 else post.content,
            'author': {
                'id': str(post.author.uuid),
                'username': post.author.username,
                'display_name': post.author.display_name
            },
            'post_type': post.post_type,
            'category': post.category.name if post.category else None,
            'tags': post.tags[:5],  # 只显示前5个标签
            'thumbnail_url': post.thumbnail_url,
            'statistics': {
                'view_count': post.view_count,
                'like_count': post.like_count,
                'comment_count': post.comment_count
            },
            'published_at': post.published_at.isoformat() if post.published_at else None
        }

        # 添加用户点赞状态
        if user_id:
            user = self._get_user_by_id(user_id)
            data['user_liked'] = PostLike.objects.filter(post=post, user=user).exists()

        return data

    def _apply_post_filters(self, queryset, filters: Dict[str, Any]):
        """应用帖子过滤器"""
        if filters.get('category_id'):
            queryset = queryset.filter(category__uuid=filters['category_id'])

        if filters.get('post_type'):
            queryset = queryset.filter(post_type=filters['post_type'])

        if filters.get('author_id'):
            queryset = queryset.filter(author__uuid=filters['author_id'])

        if filters.get('tags'):
            for tag in filters['tags']:
                queryset = queryset.filter(tags__contains=[tag])

        if filters.get('visibility'):
            queryset = queryset.filter(visibility=filters['visibility'])

        if filters.get('date_from'):
            queryset = queryset.filter(published_at__gte=filters['date_from'])

        if filters.get('date_to'):
            queryset = queryset.filter(published_at__lte=filters['date_to'])

        return queryset

    def _check_comment_permission(self, author, post: Post):
        """检查评论权限"""
        # 检查帖子是否允许评论
        if not post.allow_comments:
            raise ForbiddenException("该帖子不允许评论")

        # 检查用户状态
        if author.status != 'active':
            raise ForbiddenException("用户状态异常，无法评论")

        # 检查是否可以访问帖子
        if not self._can_access_post(post, str(author.uuid)):
            raise ForbiddenException("无权访问该帖子")

    def _validate_comment_data(self, data: Dict[str, Any]):
        """验证评论数据"""
        required_fields = ['content']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )

        # 验证内容长度
        content = data['content']
        if len(content.strip()) < 1 or len(content) > 5000:
            raise ValidationException("评论内容长度必须在1-5000个字符之间")

    def _create_comment_entity(self, author, post: Post, data: Dict[str, Any]) -> Comment:
        """创建评论实体"""
        # 获取父评论
        parent = None
        if data.get('parent_id'):
            try:
                parent = Comment.objects.get(uuid=data['parent_id'], post=post)
            except Comment.DoesNotExist:
                raise ValidationException("父评论不存在")

        return Comment.objects.create(
            post=post,
            author=author,
            content=data['content'],
            parent=parent,
            media_urls=data.get('media_urls', []),
            moderation_status='approved',  # 评论默认通过审核
            created_by=author,
            updated_by=author
        )

    def _get_user_interests(self, user) -> List[str]:
        """获取用户兴趣标签"""
        # 基于用户点赞的帖子标签
        liked_posts = Post.objects.filter(
            likes__user=user,
            status='active'
        ).values_list('tags', flat=True)

        # 基于用户评论的帖子标签
        commented_posts = Post.objects.filter(
            comments__author=user,
            status='active'
        ).values_list('tags', flat=True)

        # 合并并统计标签频率
        all_tags = []
        for tags in liked_posts:
            all_tags.extend(tags)
        for tags in commented_posts:
            all_tags.extend(tags)

        # 返回最常见的标签
        from collections import Counter
        tag_counts = Counter(all_tags)
        return [tag for tag, count in tag_counts.most_common(10)]

    def _get_following_posts(self, user) -> List[Post]:
        """获取关注用户的帖子"""
        from apps.social.models import Follow

        following_users = Follow.objects.filter(
            follower=user
        ).values_list('following_id', flat=True)

        return Post.objects.filter(
            author_id__in=following_users,
            status='active',
            moderation_status='approved',
            published_at__isnull=False,
            visibility='public'
        ).order_by('-published_at')[:20]

    def _get_trending_posts(self) -> List[Post]:
        """获取热门帖子"""
        # 计算热度分数（简化版）
        return Post.objects.filter(
            status='active',
            moderation_status='approved',
            published_at__isnull=False,
            visibility='public',
            published_at__gte=timezone.now() - timedelta(days=7)
        ).annotate(
            trending_score=F('like_count') * 2 + F('comment_count') * 3 + F('view_count') * 0.1
        ).order_by('-trending_score')[:20]

    def _get_posts_by_interests(self, interests: List[str], user) -> List[Post]:
        """基于兴趣获取帖子"""
        if not interests:
            return []

        posts = Post.objects.filter(
            status='active',
            moderation_status='approved',
            published_at__isnull=False,
            visibility='public'
        ).exclude(author=user)

        # 匹配标签
        for interest in interests:
            posts = posts.filter(tags__contains=[interest])

        return posts.order_by('-published_at')[:20]

    def _merge_recommendations(self, following_posts, trending_posts,
                             interest_posts, limit: int) -> List[Post]:
        """合并推荐结果"""
        # 简化的合并策略
        recommendations = []

        # 添加关注用户的帖子（权重最高）
        recommendations.extend(following_posts[:limit//3])

        # 添加热门帖子
        for post in trending_posts:
            if post not in recommendations and len(recommendations) < limit:
                recommendations.append(post)

        # 添加兴趣相关帖子
        for post in interest_posts:
            if post not in recommendations and len(recommendations) < limit:
                recommendations.append(post)

        return recommendations[:limit]

    def _is_trusted_user(self, user) -> bool:
        """检查是否为可信用户"""
        # 简化的可信用户判断逻辑
        if user.user_type in ['vip', 'premium']:
            return True

        # 检查用户历史记录
        user_posts = Post.objects.filter(author=user, status='active')
        if user_posts.count() >= 10:
            # 检查审核通过率
            approved_posts = user_posts.filter(moderation_status='approved').count()
            approval_rate = approved_posts / user_posts.count()
            return approval_rate >= 0.9

        return False

    def _can_moderate_content(self, user) -> bool:
        """检查是否可以审核内容"""
        return user.is_staff or user.user_type in ['moderator', 'admin']

    def _are_friends(self, user1_id: str, user2_id: str) -> bool:
        """检查是否为好友关系"""
        from apps.social.models import Friendship

        return Friendship.objects.filter(
            Q(requester__uuid=user1_id, addressee__uuid=user2_id) |
            Q(requester__uuid=user2_id, addressee__uuid=user1_id),
            status='accepted'
        ).exists()

    # 事件发布方法

    def _publish_post_created_event(self, post: Post):
        """发布帖子创建事件"""
        event = DomainEvent(
            event_type='content.post_created',
            aggregate_id=str(post.uuid),
            data={
                'post_id': str(post.uuid),
                'title': post.title,
                'author_id': str(post.author.uuid),
                'author_username': post.author.username,
                'post_type': post.post_type,
                'category': post.category.name if post.category else None,
                'tags': post.tags,
                'moderation_status': post.moderation_status,
                'created_at': post.created_at.isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_comment_created_event(self, comment: Comment):
        """发布评论创建事件"""
        event = DomainEvent(
            event_type='content.comment_created',
            aggregate_id=str(comment.post.uuid),
            data={
                'comment_id': str(comment.uuid),
                'post_id': str(comment.post.uuid),
                'post_title': comment.post.title,
                'author_id': str(comment.author.uuid),
                'author_username': comment.author.username,
                'content': comment.content,
                'is_reply': comment.is_reply,
                'created_at': comment.created_at.isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_post_liked_event(self, post: Post, user):
        """发布帖子点赞事件"""
        event = DomainEvent(
            event_type='content.post_liked',
            aggregate_id=str(post.uuid),
            data={
                'post_id': str(post.uuid),
                'post_title': post.title,
                'post_author_id': str(post.author.uuid),
                'liker_id': str(user.uuid),
                'liker_username': user.username,
                'like_count': post.like_count,
                'liked_at': timezone.now().isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_post_moderated_event(self, post: Post, moderator, action: str):
        """发布帖子审核事件"""
        event = DomainEvent(
            event_type='content.post_moderated',
            aggregate_id=str(post.uuid),
            data={
                'post_id': str(post.uuid),
                'post_title': post.title,
                'author_id': str(post.author.uuid),
                'moderator_id': str(moderator.uuid),
                'moderator_username': moderator.username,
                'action': action,
                'moderation_status': post.moderation_status,
                'reason': post.moderation_reason,
                'moderated_at': post.moderated_at.isoformat()
            }
        )
        event_bus.publish(event)
