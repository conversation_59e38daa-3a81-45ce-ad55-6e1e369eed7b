"""
内容数据序列化器 - 数据转换和验证
职责：处理内容相关API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import (
    Post, Comment, PostLike, CommentLike, ContentCategory,
    MediaFile, PostView, ContentReport
)


class PostCreateSerializer(serializers.Serializer):
    """帖子创建序列化器"""
    
    title = serializers.CharField(
        max_length=200,
        min_length=5,
        help_text="帖子标题，5-200个字符"
    )
    content = serializers.CharField(
        max_length=50000,
        min_length=10,
        help_text="帖子内容，10-50000个字符"
    )
    post_type = serializers.ChoiceField(
        choices=Post.POST_TYPE_CHOICES,
        default='text',
        help_text="帖子类型"
    )
    visibility = serializers.ChoiceField(
        choices=Post.VISIBILITY_CHOICES,
        default='public',
        help_text="可见性"
    )
    category_id = serializers.CharField(
        required=False,
        help_text="分类ID"
    )
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        help_text="标签列表"
    )
    media_urls = serializers.ListField(
        child=serializers.URLField(),
        required=False,
        help_text="媒体文件URL列表"
    )
    thumbnail_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="缩略图URL"
    )
    
    # 链接分享相关字段
    link_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="分享链接URL"
    )
    link_title = serializers.CharField(
        max_length=200,
        required=False,
        allow_blank=True,
        help_text="链接标题"
    )
    link_description = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="链接描述"
    )
    link_image_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="链接图片URL"
    )
    
    # 关联对象
    related_space_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="关联空间ID"
    )
    related_group_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="关联群组ID"
    )
    
    # 设置
    allow_comments = serializers.BooleanField(
        default=True,
        help_text="是否允许评论"
    )
    
    def validate_title(self, value):
        """验证标题"""
        if len(value.strip()) < 5:
            raise serializers.ValidationError("标题不能为空且至少5个字符")
        
        # 检查是否包含敏感词
        sensitive_words = ['spam', 'test', 'admin']
        if any(word in value.lower() for word in sensitive_words):
            raise serializers.ValidationError("标题包含敏感词")
        
        return value.strip()
    
    def validate_tags(self, value):
        """验证标签"""
        if len(value) > 10:
            raise serializers.ValidationError("标签数量不能超过10个")
        
        for tag in value:
            if len(tag.strip()) < 1:
                raise serializers.ValidationError("标签不能为空")
            if len(tag) > 50:
                raise serializers.ValidationError("单个标签长度不能超过50个字符")
        
        return [tag.strip() for tag in value]
    
    def validate_media_urls(self, value):
        """验证媒体URL"""
        if len(value) > 20:
            raise serializers.ValidationError("媒体文件数量不能超过20个")
        
        return value
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                ContentCategory.objects.get(uuid=value, status='active')
            except ContentCategory.DoesNotExist:
                raise serializers.ValidationError("分类不存在或已禁用")
        
        return value
    
    def validate(self, attrs):
        """交叉验证"""
        post_type = attrs['post_type']
        
        # 链接分享类型必须有链接URL
        if post_type == 'link' and not attrs.get('link_url'):
            raise serializers.ValidationError({
                'link_url': '链接分享类型必须提供链接URL'
            })
        
        # 媒体类型必须有媒体文件
        if post_type in ['image', 'video', 'audio'] and not attrs.get('media_urls'):
            raise serializers.ValidationError({
                'media_urls': f'{post_type}类型必须提供媒体文件'
            })
        
        return attrs


class PostSerializer(serializers.ModelSerializer):
    """帖子序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    author = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    statistics = serializers.SerializerMethodField()
    user_liked = serializers.SerializerMethodField()
    user_can_edit = serializers.SerializerMethodField()
    
    class Meta:
        model = Post
        fields = [
            'id', 'title', 'content', 'author', 'post_type', 'visibility',
            'category', 'tags', 'media_urls', 'thumbnail_url',
            'link_url', 'link_title', 'link_description', 'link_image_url',
            'allow_comments', 'is_pinned', 'is_featured',
            'statistics', 'user_liked', 'user_can_edit',
            'moderation_status', 'published_at', 'created_at', 'updated_at'
        ]
        read_only_fields = fields
    
    def get_author(self, obj):
        """获取作者信息"""
        return {
            'id': str(obj.author.uuid),
            'username': obj.author.username,
            'display_name': obj.author.display_name,
            'avatar_url': getattr(obj.author.profile, 'avatar_url', '') if hasattr(obj.author, 'profile') else ''
        }
    
    def get_category(self, obj):
        """获取分类信息"""
        if obj.category:
            return {
                'id': str(obj.category.uuid),
                'name': obj.category.name,
                'slug': obj.category.slug,
                'color': obj.category.color
            }
        return None
    
    def get_statistics(self, obj):
        """获取统计信息"""
        return {
            'view_count': obj.view_count,
            'like_count': obj.like_count,
            'comment_count': obj.comment_count,
            'share_count': obj.share_count
        }
    
    def get_user_liked(self, obj):
        """获取用户点赞状态"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return PostLike.objects.filter(post=obj, user=request.user).exists()
        return False
    
    def get_user_can_edit(self, obj):
        """获取用户编辑权限"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.author == request.user or request.user.is_staff
        return False


class CommentCreateSerializer(serializers.Serializer):
    """评论创建序列化器"""
    
    post_id = serializers.CharField(
        help_text="帖子ID"
    )
    content = serializers.CharField(
        max_length=5000,
        min_length=1,
        help_text="评论内容，1-5000个字符"
    )
    parent_id = serializers.CharField(
        required=False,
        help_text="父评论ID（回复时使用）"
    )
    media_urls = serializers.ListField(
        child=serializers.URLField(),
        required=False,
        help_text="媒体文件URL列表"
    )
    
    def validate_post_id(self, value):
        """验证帖子ID"""
        try:
            Post.objects.get(uuid=value, status='active')
        except Post.DoesNotExist:
            raise serializers.ValidationError("帖子不存在或已删除")
        
        return value
    
    def validate_parent_id(self, value):
        """验证父评论ID"""
        if value:
            try:
                Comment.objects.get(uuid=value, status='active')
            except Comment.DoesNotExist:
                raise serializers.ValidationError("父评论不存在或已删除")
        
        return value
    
    def validate_content(self, value):
        """验证评论内容"""
        content = value.strip()
        if len(content) < 1:
            raise serializers.ValidationError("评论内容不能为空")
        
        return content


class CommentSerializer(serializers.ModelSerializer):
    """评论序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    author = serializers.SerializerMethodField()
    post_title = serializers.CharField(source='post.title', read_only=True)
    parent_comment = serializers.SerializerMethodField()
    statistics = serializers.SerializerMethodField()
    user_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Comment
        fields = [
            'id', 'author', 'post_title', 'content', 'parent_comment',
            'media_urls', 'statistics', 'user_liked',
            'moderation_status', 'created_at', 'updated_at'
        ]
        read_only_fields = fields
    
    def get_author(self, obj):
        """获取作者信息"""
        return {
            'id': str(obj.author.uuid),
            'username': obj.author.username,
            'display_name': obj.author.display_name,
            'avatar_url': getattr(obj.author.profile, 'avatar_url', '') if hasattr(obj.author, 'profile') else ''
        }
    
    def get_parent_comment(self, obj):
        """获取父评论信息"""
        if obj.parent:
            return {
                'id': str(obj.parent.uuid),
                'content': obj.parent.content[:100] + '...' if len(obj.parent.content) > 100 else obj.parent.content,
                'author': obj.parent.author.username
            }
        return None
    
    def get_statistics(self, obj):
        """获取统计信息"""
        return {
            'like_count': obj.like_count,
            'reply_count': obj.reply_count
        }
    
    def get_user_liked(self, obj):
        """获取用户点赞状态"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return CommentLike.objects.filter(comment=obj, user=request.user).exists()
        return False


class ContentCategorySerializer(serializers.ModelSerializer):
    """内容分类序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    full_name = serializers.ReadOnlyField()
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    
    class Meta:
        model = ContentCategory
        fields = [
            'id', 'name', 'description', 'slug', 'full_name',
            'icon_url', 'color', 'sort_order', 'parent_name',
            'post_count', 'status', 'created_at'
        ]
        read_only_fields = fields


class MediaFileSerializer(serializers.ModelSerializer):
    """媒体文件序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    uploader = serializers.SerializerMethodField()
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = MediaFile
        fields = [
            'id', 'uploader', 'file_name', 'file_type', 'file_size',
            'file_size_mb', 'mime_type', 'file_url', 'thumbnail_url',
            'width', 'height', 'duration', 'usage_count', 'created_at'
        ]
        read_only_fields = fields
    
    def get_uploader(self, obj):
        """获取上传者信息"""
        return {
            'id': str(obj.uploader.uuid),
            'username': obj.uploader.username,
            'display_name': obj.uploader.display_name
        }
    
    def get_file_size_mb(self, obj):
        """获取文件大小（MB）"""
        return round(obj.file_size / (1024 * 1024), 2)


class PostSearchSerializer(serializers.Serializer):
    """帖子搜索序列化器"""
    
    q = serializers.CharField(
        min_length=1,
        max_length=100,
        help_text="搜索关键词"
    )
    category_id = serializers.CharField(
        required=False,
        help_text="分类ID过滤"
    )
    post_type = serializers.ChoiceField(
        choices=Post.POST_TYPE_CHOICES,
        required=False,
        help_text="帖子类型过滤"
    )
    author_id = serializers.CharField(
        required=False,
        help_text="作者ID过滤"
    )
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        help_text="标签过滤"
    )
    date_from = serializers.DateTimeField(
        required=False,
        help_text="开始时间"
    )
    date_to = serializers.DateTimeField(
        required=False,
        help_text="结束时间"
    )
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                ContentCategory.objects.get(uuid=value, status='active')
            except ContentCategory.DoesNotExist:
                raise serializers.ValidationError("分类不存在")
        
        return value


class ContentModerationSerializer(serializers.Serializer):
    """内容审核序列化器"""
    
    action = serializers.ChoiceField(
        choices=[
            ('approve', '通过'),
            ('reject', '拒绝'),
            ('flag', '标记')
        ],
        help_text="审核动作"
    )
    reason = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="审核原因"
    )


class ContentReportSerializer(serializers.ModelSerializer):
    """内容举报序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    reporter = serializers.SerializerMethodField()
    reported_content = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentReport
        fields = [
            'id', 'reporter', 'reported_content', 'report_type',
            'description', 'status', 'handled_by', 'handled_at',
            'handle_result', 'created_at'
        ]
        read_only_fields = ['id', 'reporter', 'handled_by', 'handled_at', 'created_at']
    
    def get_reporter(self, obj):
        """获取举报者信息"""
        return {
            'id': str(obj.reporter.uuid),
            'username': obj.reporter.username,
            'display_name': obj.reporter.display_name
        }
    
    def get_reported_content(self, obj):
        """获取被举报内容信息"""
        if obj.post:
            return {
                'type': 'post',
                'id': str(obj.post.uuid),
                'title': obj.post.title,
                'author': obj.post.author.username
            }
        elif obj.comment:
            return {
                'type': 'comment',
                'id': str(obj.comment.uuid),
                'content': obj.comment.content[:100] + '...' if len(obj.comment.content) > 100 else obj.comment.content,
                'author': obj.comment.author.username
            }
        return None


class ContentStatsSerializer(serializers.Serializer):
    """内容统计序列化器"""
    
    user_id = serializers.CharField(read_only=True)
    post_count = serializers.IntegerField(read_only=True)
    comment_count = serializers.IntegerField(read_only=True)
    total_likes_received = serializers.IntegerField(read_only=True)
    total_views = serializers.IntegerField(read_only=True)
    total_posts = serializers.IntegerField(read_only=True)
    total_comments = serializers.IntegerField(read_only=True)
    total_categories = serializers.IntegerField(read_only=True)
    pending_moderation = serializers.IntegerField(read_only=True)
