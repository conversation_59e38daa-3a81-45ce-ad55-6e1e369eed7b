# Generated by Django 5.2 on 2025-07-30 16:06

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('content', models.TextField(verbose_name='评论内容')),
                ('media_urls', models.JSONField(blank=True, default=list, verbose_name='媒体URL列表')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='点赞数')),
                ('reply_count', models.PositiveIntegerField(default=0, verbose_name='回复数')),
                ('moderation_status', models.CharField(choices=[('pending', '待审核'), ('approved', '已通过'), ('rejected', '已拒绝'), ('flagged', '已标记')], default='approved', max_length=20, verbose_name='审核状态')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='content.comment', verbose_name='父级评论')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '评论',
                'verbose_name_plural': '评论',
                'db_table': 'content_comments',
            },
        ),
        migrations.CreateModel(
            name='ContentCategory',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('slug', models.SlugField(unique=True, verbose_name='URL别名')),
                ('icon_url', models.URLField(blank=True, verbose_name='图标URL')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='主题色')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='帖子数量')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='content.contentcategory', verbose_name='父级分类')),
            ],
            options={
                'verbose_name': '内容分类',
                'verbose_name_plural': '内容分类',
                'db_table': 'content_categories',
            },
        ),
        migrations.CreateModel(
            name='MediaFile',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('file_name', models.CharField(max_length=255, verbose_name='文件名')),
                ('file_type', models.CharField(choices=[('image', '图片'), ('video', '视频'), ('audio', '音频'), ('document', '文档')], max_length=20, verbose_name='文件类型')),
                ('file_size', models.PositiveIntegerField(verbose_name='文件大小(字节)')),
                ('mime_type', models.CharField(max_length=100, verbose_name='MIME类型')),
                ('file_url', models.URLField(verbose_name='文件URL')),
                ('thumbnail_url', models.URLField(blank=True, verbose_name='缩略图URL')),
                ('width', models.PositiveIntegerField(blank=True, null=True, verbose_name='宽度')),
                ('height', models.PositiveIntegerField(blank=True, null=True, verbose_name='高度')),
                ('duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='时长(秒)')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='使用次数')),
                ('uploader', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_files', to=settings.AUTH_USER_MODEL, verbose_name='上传者')),
            ],
            options={
                'verbose_name': '媒体文件',
                'verbose_name_plural': '媒体文件',
                'db_table': 'content_media_files',
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('post_type', models.CharField(choices=[('text', '文本帖子'), ('image', '图片帖子'), ('video', '视频帖子'), ('audio', '音频帖子'), ('link', '链接分享'), ('poll', '投票帖子')], default='text', max_length=20, verbose_name='帖子类型')),
                ('visibility', models.CharField(choices=[('public', '公开'), ('friends', '好友可见'), ('private', '仅自己可见'), ('space', '空间内可见')], default='public', max_length=20, verbose_name='可见性')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='标签')),
                ('media_urls', models.JSONField(blank=True, default=list, verbose_name='媒体URL列表')),
                ('thumbnail_url', models.URLField(blank=True, verbose_name='缩略图URL')),
                ('link_url', models.URLField(blank=True, verbose_name='分享链接')),
                ('link_title', models.CharField(blank=True, max_length=200, verbose_name='链接标题')),
                ('link_description', models.TextField(blank=True, verbose_name='链接描述')),
                ('link_image_url', models.URLField(blank=True, verbose_name='链接图片')),
                ('related_space_id', models.CharField(blank=True, max_length=36, verbose_name='关联空间ID')),
                ('related_group_id', models.CharField(blank=True, max_length=36, verbose_name='关联群组ID')),
                ('allow_comments', models.BooleanField(default=True, verbose_name='允许评论')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='是否置顶')),
                ('is_featured', models.BooleanField(default=False, verbose_name='是否精选')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='浏览数')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='点赞数')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数')),
                ('share_count', models.PositiveIntegerField(default=0, verbose_name='分享数')),
                ('moderation_status', models.CharField(choices=[('pending', '待审核'), ('approved', '已通过'), ('rejected', '已拒绝'), ('flagged', '已标记')], default='pending', max_length=20, verbose_name='审核状态')),
                ('moderation_reason', models.TextField(blank=True, verbose_name='审核原因')),
                ('moderated_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='发布时间')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posts', to='content.contentcategory', verbose_name='分类')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('moderated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='moderated_posts', to=settings.AUTH_USER_MODEL, verbose_name='审核者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '帖子',
                'verbose_name_plural': '帖子',
                'db_table': 'content_posts',
            },
        ),
        migrations.CreateModel(
            name='ContentReport',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('report_type', models.CharField(choices=[('spam', '垃圾信息'), ('harassment', '骚扰'), ('hate_speech', '仇恨言论'), ('violence', '暴力内容'), ('adult_content', '成人内容'), ('copyright', '版权侵犯'), ('misinformation', '虚假信息'), ('other', '其他')], max_length=20, verbose_name='举报类型')),
                ('description', models.TextField(verbose_name='举报描述')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('reviewing', '审核中'), ('resolved', '已处理'), ('dismissed', '已驳回')], default='pending', max_length=20, verbose_name='处理状态')),
                ('handled_at', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('handle_result', models.TextField(blank=True, verbose_name='处理结果')),
                ('comment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='content.comment', verbose_name='举报评论')),
                ('handled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='handled_reports', to=settings.AUTH_USER_MODEL, verbose_name='处理者')),
                ('reporter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_reports', to=settings.AUTH_USER_MODEL, verbose_name='举报者')),
                ('post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='content.post', verbose_name='举报帖子')),
            ],
            options={
                'verbose_name': '内容举报',
                'verbose_name_plural': '内容举报',
                'db_table': 'content_reports',
            },
        ),
        migrations.AddField(
            model_name='comment',
            name='post',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='content.post', verbose_name='帖子'),
        ),
        migrations.CreateModel(
            name='PostLike',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='content.post', verbose_name='帖子')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='post_likes', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '帖子点赞',
                'verbose_name_plural': '帖子点赞',
                'db_table': 'content_post_likes',
            },
        ),
        migrations.CreateModel(
            name='PostView',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('referrer', models.URLField(blank=True, verbose_name='来源页面')),
                ('duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='浏览时长(秒)')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='content.post', verbose_name='帖子')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_views', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '帖子浏览记录',
                'verbose_name_plural': '帖子浏览记录',
                'db_table': 'content_post_views',
            },
        ),
        migrations.CreateModel(
            name='CommentLike',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='content.comment', verbose_name='评论')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comment_likes', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '评论点赞',
                'verbose_name_plural': '评论点赞',
                'db_table': 'content_comment_likes',
                'indexes': [models.Index(fields=['comment'], name='content_com_comment_f1e03f_idx'), models.Index(fields=['user'], name='content_com_user_id_676617_idx')],
                'unique_together': {('comment', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='contentcategory',
            index=models.Index(fields=['status'], name='content_cat_status_ba33f0_idx'),
        ),
        migrations.AddIndex(
            model_name='contentcategory',
            index=models.Index(fields=['sort_order'], name='content_cat_sort_or_bcc945_idx'),
        ),
        migrations.AddIndex(
            model_name='contentcategory',
            index=models.Index(fields=['parent'], name='content_cat_parent__fd42ba_idx'),
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['uploader'], name='content_med_uploade_22dd6a_idx'),
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['file_type'], name='content_med_file_ty_c11cf7_idx'),
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['-created_at'], name='content_med_created_66bb86_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['author'], name='content_pos_author__d59805_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['category'], name='content_pos_categor_e93d12_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['post_type'], name='content_pos_post_ty_501870_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['visibility'], name='content_pos_visibil_a1b102_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['status'], name='content_pos_status_71b0c3_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['moderation_status'], name='content_pos_moderat_1da07f_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['-published_at'], name='content_pos_publish_2d1319_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['-created_at'], name='content_pos_created_e93095_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_pinned', '-published_at'], name='content_pos_is_pinn_2cb835_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_featured', '-published_at'], name='content_pos_is_feat_230768_idx'),
        ),
        migrations.AddIndex(
            model_name='contentreport',
            index=models.Index(fields=['reporter'], name='content_rep_reporte_2462fb_idx'),
        ),
        migrations.AddIndex(
            model_name='contentreport',
            index=models.Index(fields=['post'], name='content_rep_post_id_ae5f81_idx'),
        ),
        migrations.AddIndex(
            model_name='contentreport',
            index=models.Index(fields=['comment'], name='content_rep_comment_4aa1cd_idx'),
        ),
        migrations.AddIndex(
            model_name='contentreport',
            index=models.Index(fields=['status'], name='content_rep_status_9fcf4e_idx'),
        ),
        migrations.AddIndex(
            model_name='contentreport',
            index=models.Index(fields=['-created_at'], name='content_rep_created_40cd26_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['post', '-created_at'], name='content_com_post_id_f0a23f_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['author'], name='content_com_author__2394cb_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['parent'], name='content_com_parent__4ab0a2_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['moderation_status'], name='content_com_moderat_b104a3_idx'),
        ),
        migrations.AddIndex(
            model_name='postlike',
            index=models.Index(fields=['post'], name='content_pos_post_id_a38dda_idx'),
        ),
        migrations.AddIndex(
            model_name='postlike',
            index=models.Index(fields=['user'], name='content_pos_user_id_f6eaf0_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='postlike',
            unique_together={('post', 'user')},
        ),
        migrations.AddIndex(
            model_name='postview',
            index=models.Index(fields=['post'], name='content_pos_post_id_378b1b_idx'),
        ),
        migrations.AddIndex(
            model_name='postview',
            index=models.Index(fields=['user'], name='content_pos_user_id_ead2cf_idx'),
        ),
        migrations.AddIndex(
            model_name='postview',
            index=models.Index(fields=['-created_at'], name='content_pos_created_981f6f_idx'),
        ),
    ]
