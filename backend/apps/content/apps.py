"""
内容应用配置 - 内容域应用配置
职责：配置内容应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class ContentConfig(AppConfig):
    """内容应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.content'
    verbose_name = '用户生成内容管理'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        # 初始化内容审核系统
        self.initialize_moderation_system()
        
        logger.info("内容应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import ContentService
            
            # 注册内容服务为单例
            container.register_singleton(ContentService, ContentService)
            
            logger.info("内容服务注册完成")
            
        except Exception as e:
            logger.error(f"内容服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入信号处理器（这会自动注册Django信号）
            from . import signals

            logger.info("内容事件处理器注册完成")

        except Exception as e:
            logger.error(f"内容事件处理器注册失败: {e}")
    
    def initialize_moderation_system(self):
        """初始化内容审核系统"""
        try:
            # 这里可以初始化内容审核相关的配置
            # 例如：敏感词库、审核规则等
            
            logger.info("内容审核系统初始化完成")
            
        except Exception as e:
            logger.error(f"内容审核系统初始化失败: {e}")
