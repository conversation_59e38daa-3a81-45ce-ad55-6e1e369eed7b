"""
内容视图控制器 - 薄层HTTP处理
职责：处理内容相关的HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.core.responses import APIResponse, SuccessMessages
from apps.core.exceptions import handle_exceptions
from .services import ContentService
from .serializers import (
    PostCreateSerializer,
    PostSerializer,
    CommentCreateSerializer,
    CommentSerializer,
    PostSearchSerializer,
    ContentModerationSerializer
)

logger = logging.getLogger(__name__)


class PostViewSet(viewsets.ViewSet):
    """
    帖子视图集
    
    职责：
    1. 处理帖子相关的HTTP请求
    2. 调用内容业务服务
    3. 返回标准化响应
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.content_service = ContentService()
    def create(self, request: Request) -> Response:
        """
        创建帖子
        
        POST /api/v1/posts/
        """
        logger.info(f"创建帖子请求: {request.user.username}")
        
        # 数据验证
        serializer = PostCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.content_service.create_post(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="帖子创建成功"
        )
    def retrieve(self, request: Request, pk=None) -> Response:
        """
        获取帖子详情
        
        GET /api/v1/posts/{id}/
        """
        user_id = str(request.user.uuid) if request.user.is_authenticated else None
        
        # 调用业务服务
        result = self.content_service.get_post_by_id(pk, user_id)
        
        return APIResponse.success(data=result)
    def list(self, request: Request) -> Response:
        """
        获取帖子列表
        
        GET /api/v1/posts/?category_id=xxx&post_type=text&page=1&page_size=20
        """
        # 构建过滤条件
        filters = {
            'category_id': request.query_params.get('category_id'),
            'post_type': request.query_params.get('post_type'),
            'author_id': request.query_params.get('author_id'),
            'visibility': request.query_params.get('visibility', 'public'),
            'user_id': str(request.user.uuid) if request.user.is_authenticated else None
        }
        
        # 移除空值
        filters = {k: v for k, v in filters.items() if v}
        
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)
        
        # 调用业务服务
        result = self.content_service.get_posts(filters, page, page_size)
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    def like(self, request: Request, pk=None) -> Response:
        """
        点赞帖子
        
        POST /api/v1/posts/{id}/like/
        """
        # 调用业务服务
        result = self.content_service.like_post(str(request.user.uuid), pk)
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '操作成功')
        )
    
    @action(detail=True, methods=['post'])
    def unlike(self, request: Request, pk=None) -> Response:
        """
        取消点赞帖子
        
        POST /api/v1/posts/{id}/unlike/
        """
        # 调用业务服务
        result = self.content_service.unlike_post(str(request.user.uuid), pk)
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '操作成功')
        )
    
    @action(detail=False, methods=['get'])
    def search(self, request: Request) -> Response:
        """
        搜索帖子
        
        GET /api/v1/posts/search/?q=keyword&category_id=xxx&page=1&page_size=20
        """
        query = request.query_params.get('q', '').strip()
        if not query:
            return APIResponse.validation_error(
                errors={'q': ['搜索关键词不能为空']},
                message="参数验证失败"
            )
        
        # 构建过滤条件
        filters = {
            'category_id': request.query_params.get('category_id'),
            'post_type': request.query_params.get('post_type'),
            'author_id': request.query_params.get('author_id'),
            'user_id': str(request.user.uuid) if request.user.is_authenticated else None
        }
        
        # 移除空值
        filters = {k: v for k, v in filters.items() if v}
        
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        
        # 调用业务服务
        result = self.content_service.search_posts(query, filters, page, page_size)
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def recommended(self, request: Request) -> Response:
        """
        获取推荐帖子
        
        GET /api/v1/posts/recommended/?limit=10
        """
        limit = min(int(request.query_params.get('limit', 10)), 50)
        
        # 调用业务服务
        result = self.content_service.get_recommended_posts(
            str(request.user.uuid),
            limit
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def moderate(self, request: Request, pk=None) -> Response:
        """
        审核帖子
        
        POST /api/v1/posts/{id}/moderate/
        """
        # 数据验证
        serializer = ContentModerationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.content_service.moderate_post(
            str(request.user.uuid),
            pk,
            serializer.validated_data['action'],
            serializer.validated_data.get('reason', '')
        )
        
        return APIResponse.success(
            data=result,
            message="审核完成"
        )


class CommentViewSet(viewsets.ViewSet):
    """评论视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.content_service = ContentService()
    def create(self, request: Request) -> Response:
        """
        创建评论
        
        POST /api/v1/comments/
        """
        logger.info(f"创建评论请求: {request.user.username}")
        
        # 数据验证
        serializer = CommentCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.content_service.create_comment(
            str(request.user.uuid),
            serializer.validated_data['post_id'],
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="评论创建成功"
        )
    
    @action(detail=False, methods=['get'])
    def by_post(self, request: Request) -> Response:
        """
        获取帖子的评论列表
        
        GET /api/v1/comments/by_post/?post_id=xxx&page=1&page_size=20
        """
        post_id = request.query_params.get('post_id')
        if not post_id:
            return APIResponse.validation_error(
                errors={'post_id': ['帖子ID不能为空']},
                message="参数验证失败"
            )
        
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)
        
        # TODO: 实现获取帖子评论列表
        return APIResponse.success(message="获取帖子评论功能开发中")


class ContentCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """内容分类视图集"""
    
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    def list_categories(self, request: Request) -> Response:
        """
        获取内容分类列表
        
        GET /api/v1/content-categories/list_categories/
        """
        from .models import ContentCategory
        
        categories = ContentCategory.objects.filter(
            status='active'
        ).order_by('sort_order', 'name')
        
        data = []
        for category in categories:
            category_data = {
                'id': str(category.uuid),
                'name': category.name,
                'description': category.description,
                'slug': category.slug,
                'icon_url': category.icon_url,
                'color': category.color,
                'post_count': category.post_count,
                'parent_id': str(category.parent.uuid) if category.parent else None
            }
            data.append(category_data)
        
        return APIResponse.success(data=data)


class ContentStatsViewSet(viewsets.ViewSet):
    """内容统计视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.content_service = ContentService()
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 10))  # 缓存10分钟
    def user_stats(self, request: Request) -> Response:
        """
        获取用户内容统计
        
        GET /api/v1/content-stats/user_stats/
        """
        # 调用业务服务
        result = self.content_service.get_content_statistics(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    def platform_stats(self, request: Request) -> Response:
        """
        获取平台内容统计（需要管理员权限）
        
        GET /api/v1/content-stats/platform_stats/
        """
        # 检查权限
        if not request.user.is_staff:
            return APIResponse.forbidden(message="需要管理员权限")
        
        # 调用业务服务
        result = self.content_service.get_content_statistics()
        
        return APIResponse.success(data=result)
