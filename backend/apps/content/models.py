"""
内容域数据模型 - 高内聚的用户生成内容相关模型
职责：定义帖子、评论、媒体文件、内容分类、审核状态等数据模型
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, AuditModel, StatusModel, MetadataModel
from django.contrib.auth import get_user_model

User = get_user_model()


class ContentCategory(StatusModel):
    """内容分类模型"""
    
    name = models.CharField(max_length=50, unique=True, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    slug = models.SlugField(max_length=50, unique=True, verbose_name='URL别名')
    
    # 分类属性
    icon_url = models.URLField(blank=True, verbose_name='图标URL')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='主题色')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    
    # 父级分类
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父级分类'
    )
    
    # 统计信息
    post_count = models.PositiveIntegerField(default=0, verbose_name='帖子数量')
    
    class Meta:
        db_table = 'content_categories'
        verbose_name = '内容分类'
        verbose_name_plural = '内容分类'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['sort_order']),
            models.Index(fields=['parent']),
        ]
    
    def __str__(self):
        return self.name
    
    @property
    def full_name(self):
        """获取完整分类名称"""
        if self.parent:
            return f"{self.parent.name} > {self.name}"
        return self.name
    
    def increment_post_count(self):
        """增加帖子数量"""
        self.post_count += 1
        self.save(update_fields=['post_count'])
        
        # 同时更新父级分类
        if self.parent:
            self.parent.increment_post_count()


class Post(AuditModel, StatusModel, MetadataModel):
    """帖子模型"""
    
    POST_TYPE_CHOICES = [
        ('text', '文本帖子'),
        ('image', '图片帖子'),
        ('video', '视频帖子'),
        ('audio', '音频帖子'),
        ('link', '链接分享'),
        ('poll', '投票帖子'),
    ]
    
    VISIBILITY_CHOICES = [
        ('public', '公开'),
        ('friends', '好友可见'),
        ('private', '仅自己可见'),
        ('space', '空间内可见'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='posts',
        verbose_name='作者'
    )
    
    # 帖子属性
    post_type = models.CharField(
        max_length=20,
        choices=POST_TYPE_CHOICES,
        default='text',
        verbose_name='帖子类型'
    )
    visibility = models.CharField(
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default='public',
        verbose_name='可见性'
    )
    
    # 分类和标签
    category = models.ForeignKey(
        ContentCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posts',
        verbose_name='分类'
    )
    tags = models.JSONField(default=list, blank=True, verbose_name='标签')
    
    # 媒体内容
    media_urls = models.JSONField(default=list, blank=True, verbose_name='媒体URL列表')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    
    # 链接分享
    link_url = models.URLField(blank=True, verbose_name='分享链接')
    link_title = models.CharField(max_length=200, blank=True, verbose_name='链接标题')
    link_description = models.TextField(blank=True, verbose_name='链接描述')
    link_image_url = models.URLField(blank=True, verbose_name='链接图片')
    
    # 关联对象
    related_space_id = models.CharField(max_length=36, blank=True, verbose_name='关联空间ID')
    related_group_id = models.CharField(max_length=36, blank=True, verbose_name='关联群组ID')
    
    # 帖子设置
    allow_comments = models.BooleanField(default=True, verbose_name='允许评论')
    is_pinned = models.BooleanField(default=False, verbose_name='是否置顶')
    is_featured = models.BooleanField(default=False, verbose_name='是否精选')
    
    # 统计信息
    view_count = models.PositiveIntegerField(default=0, verbose_name='浏览数')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    comment_count = models.PositiveIntegerField(default=0, verbose_name='评论数')
    share_count = models.PositiveIntegerField(default=0, verbose_name='分享数')
    
    # 审核状态
    moderation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待审核'),
            ('approved', '已通过'),
            ('rejected', '已拒绝'),
            ('flagged', '已标记'),
        ],
        default='pending',
        verbose_name='审核状态'
    )
    moderation_reason = models.TextField(blank=True, verbose_name='审核原因')
    moderated_at = models.DateTimeField(null=True, blank=True, verbose_name='审核时间')
    moderated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='moderated_posts',
        verbose_name='审核者'
    )
    
    # 发布时间
    published_at = models.DateTimeField(null=True, blank=True, verbose_name='发布时间')
    
    class Meta:
        db_table = 'content_posts'
        verbose_name = '帖子'
        verbose_name_plural = '帖子'
        indexes = [
            models.Index(fields=['author']),
            models.Index(fields=['category']),
            models.Index(fields=['post_type']),
            models.Index(fields=['visibility']),
            models.Index(fields=['status']),
            models.Index(fields=['moderation_status']),
            models.Index(fields=['-published_at']),
            models.Index(fields=['-created_at']),
            models.Index(fields=['is_pinned', '-published_at']),
            models.Index(fields=['is_featured', '-published_at']),
        ]
    
    def __str__(self):
        return f"{self.title} by {self.author.username}"
    
    @property
    def is_published(self):
        """检查是否已发布"""
        return (
            self.status == 'active' and 
            self.moderation_status == 'approved' and 
            self.published_at is not None
        )
    
    def increment_view_count(self):
        """增加浏览数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])
    
    def increment_like_count(self):
        """增加点赞数"""
        self.like_count += 1
        self.save(update_fields=['like_count'])
    
    def decrement_like_count(self):
        """减少点赞数"""
        if self.like_count > 0:
            self.like_count -= 1
            self.save(update_fields=['like_count'])
    
    def increment_comment_count(self):
        """增加评论数"""
        self.comment_count += 1
        self.save(update_fields=['comment_count'])
    
    def increment_share_count(self):
        """增加分享数"""
        self.share_count += 1
        self.save(update_fields=['share_count'])


class Comment(AuditModel, StatusModel, MetadataModel):
    """评论模型"""
    
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='帖子'
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='作者'
    )
    content = models.TextField(verbose_name='评论内容')
    
    # 回复关系
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='父级评论'
    )
    
    # 媒体内容
    media_urls = models.JSONField(default=list, blank=True, verbose_name='媒体URL列表')
    
    # 统计信息
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    reply_count = models.PositiveIntegerField(default=0, verbose_name='回复数')
    
    # 审核状态
    moderation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待审核'),
            ('approved', '已通过'),
            ('rejected', '已拒绝'),
            ('flagged', '已标记'),
        ],
        default='approved',  # 评论默认通过
        verbose_name='审核状态'
    )
    
    class Meta:
        db_table = 'content_comments'
        verbose_name = '评论'
        verbose_name_plural = '评论'
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['author']),
            models.Index(fields=['parent']),
            models.Index(fields=['moderation_status']),
        ]
    
    def __str__(self):
        return f"Comment by {self.author.username} on {self.post.title}"
    
    @property
    def is_reply(self):
        """检查是否为回复"""
        return self.parent is not None
    
    def increment_like_count(self):
        """增加点赞数"""
        self.like_count += 1
        self.save(update_fields=['like_count'])
    
    def decrement_like_count(self):
        """减少点赞数"""
        if self.like_count > 0:
            self.like_count -= 1
            self.save(update_fields=['like_count'])
    
    def increment_reply_count(self):
        """增加回复数"""
        self.reply_count += 1
        self.save(update_fields=['reply_count'])


class PostLike(BaseModel):
    """帖子点赞模型"""
    
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='likes',
        verbose_name='帖子'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='post_likes',
        verbose_name='用户'
    )
    
    class Meta:
        db_table = 'content_post_likes'
        verbose_name = '帖子点赞'
        verbose_name_plural = '帖子点赞'
        unique_together = ['post', 'user']
        indexes = [
            models.Index(fields=['post']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"{self.user.username} likes {self.post.title}"


class CommentLike(BaseModel):
    """评论点赞模型"""
    
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='likes',
        verbose_name='评论'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comment_likes',
        verbose_name='用户'
    )
    
    class Meta:
        db_table = 'content_comment_likes'
        verbose_name = '评论点赞'
        verbose_name_plural = '评论点赞'
        unique_together = ['comment', 'user']
        indexes = [
            models.Index(fields=['comment']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"{self.user.username} likes comment {self.comment.id}"


class MediaFile(MetadataModel):
    """媒体文件模型"""
    
    FILE_TYPE_CHOICES = [
        ('image', '图片'),
        ('video', '视频'),
        ('audio', '音频'),
        ('document', '文档'),
    ]
    
    uploader = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='uploaded_files',
        verbose_name='上传者'
    )
    
    # 文件信息
    file_name = models.CharField(max_length=255, verbose_name='文件名')
    file_type = models.CharField(
        max_length=20,
        choices=FILE_TYPE_CHOICES,
        verbose_name='文件类型'
    )
    file_size = models.PositiveIntegerField(verbose_name='文件大小(字节)')
    mime_type = models.CharField(max_length=100, verbose_name='MIME类型')
    
    # 文件URL
    file_url = models.URLField(verbose_name='文件URL')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    
    # 图片/视频属性
    width = models.PositiveIntegerField(null=True, blank=True, verbose_name='宽度')
    height = models.PositiveIntegerField(null=True, blank=True, verbose_name='高度')
    duration = models.PositiveIntegerField(null=True, blank=True, verbose_name='时长(秒)')
    
    # 使用统计
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    
    class Meta:
        db_table = 'content_media_files'
        verbose_name = '媒体文件'
        verbose_name_plural = '媒体文件'
        indexes = [
            models.Index(fields=['uploader']),
            models.Index(fields=['file_type']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.file_name} ({self.file_type})"
    
    def increment_usage_count(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class PostView(BaseModel):
    """帖子浏览记录模型"""
    
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='views',
        verbose_name='帖子'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='post_views',
        verbose_name='用户'
    )
    
    # 浏览信息
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')
    referrer = models.URLField(blank=True, verbose_name='来源页面')
    
    # 浏览时长
    duration = models.PositiveIntegerField(null=True, blank=True, verbose_name='浏览时长(秒)')
    
    class Meta:
        db_table = 'content_post_views'
        verbose_name = '帖子浏览记录'
        verbose_name_plural = '帖子浏览记录'
        indexes = [
            models.Index(fields=['post']),
            models.Index(fields=['user']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        user_info = self.user.username if self.user else self.ip_address
        return f"{user_info} viewed {self.post.title}"


class ContentReport(BaseModel):
    """内容举报模型"""
    
    REPORT_TYPE_CHOICES = [
        ('spam', '垃圾信息'),
        ('harassment', '骚扰'),
        ('hate_speech', '仇恨言论'),
        ('violence', '暴力内容'),
        ('adult_content', '成人内容'),
        ('copyright', '版权侵犯'),
        ('misinformation', '虚假信息'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('reviewing', '审核中'),
        ('resolved', '已处理'),
        ('dismissed', '已驳回'),
    ]
    
    reporter = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='content_reports',
        verbose_name='举报者'
    )
    
    # 举报对象
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reports',
        verbose_name='举报帖子'
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reports',
        verbose_name='举报评论'
    )
    
    # 举报信息
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPE_CHOICES,
        verbose_name='举报类型'
    )
    description = models.TextField(verbose_name='举报描述')
    
    # 处理状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='处理状态'
    )
    handled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='handled_reports',
        verbose_name='处理者'
    )
    handled_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    handle_result = models.TextField(blank=True, verbose_name='处理结果')
    
    class Meta:
        db_table = 'content_reports'
        verbose_name = '内容举报'
        verbose_name_plural = '内容举报'
        indexes = [
            models.Index(fields=['reporter']),
            models.Index(fields=['post']),
            models.Index(fields=['comment']),
            models.Index(fields=['status']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        target = self.post.title if self.post else f"Comment {self.comment.id}"
        return f"Report by {self.reporter.username}: {target}"
