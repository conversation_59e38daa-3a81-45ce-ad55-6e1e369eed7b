"""
消息域数据模型 - 高内聚的实时通信相关模型
职责：定义消息、会话、消息状态、在线状态等数据模型
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, AuditModel, StatusModel, MetadataModel
from django.contrib.auth import get_user_model

User = get_user_model()


class Conversation(MetadataModel):
    """会话模型"""
    
    CONVERSATION_TYPE_CHOICES = [
        ('private', '私聊'),
        ('group', '群聊'),
        ('system', '系统消息'),
        ('space', '空间聊天'),
    ]
    
    name = models.CharField(max_length=100, blank=True, verbose_name='会话名称')
    conversation_type = models.CharField(
        max_length=20,
        choices=CONVERSATION_TYPE_CHOICES,
        verbose_name='会话类型'
    )
    
    # 会话参与者
    participants = models.ManyToManyField(
        User,
        through='ConversationParticipant',
        related_name='conversations',
        verbose_name='参与者'
    )
    
    # 会话设置
    is_active = models.BooleanField(default=True, verbose_name='是否活跃')
    is_archived = models.BooleanField(default=False, verbose_name='是否归档')
    is_muted = models.BooleanField(default=False, verbose_name='是否静音')
    
    # 最后消息信息
    last_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='last_message_conversations',
        verbose_name='最后一条消息'
    )
    last_message_at = models.DateTimeField(null=True, blank=True, verbose_name='最后消息时间')
    
    # 统计信息
    message_count = models.PositiveIntegerField(default=0, verbose_name='消息数量')
    
    # 关联对象（可选）
    related_space_id = models.CharField(max_length=36, blank=True, verbose_name='关联空间ID')
    related_group_id = models.CharField(max_length=36, blank=True, verbose_name='关联群组ID')
    
    class Meta:
        db_table = 'messaging_conversations'
        verbose_name = '会话'
        verbose_name_plural = '会话'
        indexes = [
            models.Index(fields=['conversation_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['-last_message_at']),
            models.Index(fields=['related_space_id']),
            models.Index(fields=['related_group_id']),
        ]
    
    def __str__(self):
        return f"{self.name or f'{self.conversation_type}会话'} ({self.id})"
    
    @property
    def display_name(self):
        """获取显示名称"""
        if self.name:
            return self.name
        
        if self.conversation_type == 'private':
            # 私聊显示对方名称
            participants = self.participants.all()[:2]
            if len(participants) == 2:
                return f"{participants[0].display_name} & {participants[1].display_name}"
        
        return f"{self.get_conversation_type_display()}会话"
    
    def add_participant(self, user, role='member'):
        """添加参与者"""
        participant, created = ConversationParticipant.objects.get_or_create(
            conversation=self,
            user=user,
            defaults={'role': role, 'status': 'active'}
        )
        return participant
    
    def remove_participant(self, user):
        """移除参与者"""
        ConversationParticipant.objects.filter(
            conversation=self,
            user=user
        ).update(status='left')
    
    def increment_message_count(self):
        """增加消息数量"""
        self.message_count += 1
        self.save(update_fields=['message_count'])


class ConversationParticipant(BaseModel):
    """会话参与者模型"""
    
    ROLE_CHOICES = [
        ('owner', '所有者'),
        ('admin', '管理员'),
        ('member', '成员'),
    ]
    
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('left', '已离开'),
        ('kicked', '被踢出'),
        ('banned', '被禁言'),
    ]
    
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='conversation_participants',
        verbose_name='会话'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='conversation_participations',
        verbose_name='用户'
    )
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='member',
        verbose_name='角色'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name='状态'
    )
    
    # 个人设置
    is_muted = models.BooleanField(default=False, verbose_name='是否静音')
    is_pinned = models.BooleanField(default=False, verbose_name='是否置顶')
    
    # 阅读状态
    last_read_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='read_by_participants',
        verbose_name='最后阅读消息'
    )
    last_read_at = models.DateTimeField(null=True, blank=True, verbose_name='最后阅读时间')
    unread_count = models.PositiveIntegerField(default=0, verbose_name='未读数量')
    
    # 加入信息
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name='加入时间')
    left_at = models.DateTimeField(null=True, blank=True, verbose_name='离开时间')
    
    class Meta:
        db_table = 'messaging_conversation_participants'
        verbose_name = '会话参与者'
        verbose_name_plural = '会话参与者'
        unique_together = ['conversation', 'user']
        indexes = [
            models.Index(fields=['conversation', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['unread_count']),
            models.Index(fields=['is_pinned']),
        ]
    
    def __str__(self):
        return f"{self.user.username} in {self.conversation}"
    
    def mark_as_read(self, message=None):
        """标记为已读"""
        if message:
            self.last_read_message = message
            self.last_read_at = timezone.now()
        self.unread_count = 0
        self.save(update_fields=['last_read_message', 'last_read_at', 'unread_count'])
    
    def increment_unread_count(self):
        """增加未读数量"""
        self.unread_count += 1
        self.save(update_fields=['unread_count'])


class Message(MetadataModel):
    """消息模型"""
    
    MESSAGE_TYPE_CHOICES = [
        ('text', '文本消息'),
        ('image', '图片消息'),
        ('video', '视频消息'),
        ('audio', '音频消息'),
        ('file', '文件消息'),
        ('location', '位置消息'),
        ('system', '系统消息'),
        ('emoji', '表情消息'),
        ('reply', '回复消息'),
    ]
    
    STATUS_CHOICES = [
        ('sent', '已发送'),
        ('delivered', '已送达'),
        ('read', '已读'),
        ('failed', '发送失败'),
        ('deleted', '已删除'),
    ]
    
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name='会话'
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name='发送者'
    )
    
    # 消息内容
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPE_CHOICES,
        default='text',
        verbose_name='消息类型'
    )
    content = models.TextField(verbose_name='消息内容')
    
    # 媒体文件
    media_url = models.URLField(blank=True, verbose_name='媒体文件URL')
    media_thumbnail_url = models.URLField(blank=True, verbose_name='媒体缩略图URL')
    media_size = models.PositiveIntegerField(null=True, blank=True, verbose_name='媒体文件大小')
    media_duration = models.PositiveIntegerField(null=True, blank=True, verbose_name='媒体时长(秒)')
    
    # 回复消息
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='回复的消息'
    )
    
    # 消息状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='sent',
        verbose_name='状态'
    )
    
    # 时间信息
    sent_at = models.DateTimeField(auto_now_add=True, verbose_name='发送时间')
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='送达时间')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    # 消息特性
    is_edited = models.BooleanField(default=False, verbose_name='是否已编辑')
    is_pinned = models.BooleanField(default=False, verbose_name='是否置顶')
    is_important = models.BooleanField(default=False, verbose_name='是否重要')
    
    # 统计信息
    read_count = models.PositiveIntegerField(default=0, verbose_name='阅读数量')
    reaction_count = models.PositiveIntegerField(default=0, verbose_name='反应数量')
    
    class Meta:
        db_table = 'messaging_messages'
        verbose_name = '消息'
        verbose_name_plural = '消息'
        indexes = [
            models.Index(fields=['conversation', '-sent_at']),
            models.Index(fields=['sender']),
            models.Index(fields=['message_type']),
            models.Index(fields=['status']),
            models.Index(fields=['reply_to']),
            models.Index(fields=['is_pinned']),
        ]
    
    def __str__(self):
        return f"{self.sender.username}: {self.content[:50]}..."
    
    def mark_as_delivered(self):
        """标记为已送达"""
        if self.status == 'sent':
            self.status = 'delivered'
            self.delivered_at = timezone.now()
            self.save(update_fields=['status', 'delivered_at'])
    
    def mark_as_read(self):
        """标记为已读"""
        if self.status in ['sent', 'delivered']:
            self.status = 'read'
            self.read_at = timezone.now()
            self.read_count += 1
            self.save(update_fields=['status', 'read_at', 'read_count'])
    
    def soft_delete(self):
        """软删除消息"""
        self.status = 'deleted'
        self.content = '[此消息已被删除]'
        self.save(update_fields=['status', 'content'])


class MessageReaction(BaseModel):
    """消息反应模型"""
    
    REACTION_TYPE_CHOICES = [
        ('like', '👍'),
        ('love', '❤️'),
        ('laugh', '😂'),
        ('wow', '😮'),
        ('sad', '😢'),
        ('angry', '😠'),
    ]
    
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='reactions',
        verbose_name='消息'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='message_reactions',
        verbose_name='用户'
    )
    reaction_type = models.CharField(
        max_length=20,
        choices=REACTION_TYPE_CHOICES,
        verbose_name='反应类型'
    )
    
    class Meta:
        db_table = 'messaging_message_reactions'
        verbose_name = '消息反应'
        verbose_name_plural = '消息反应'
        unique_together = ['message', 'user', 'reaction_type']
        indexes = [
            models.Index(fields=['message']),
            models.Index(fields=['user']),
            models.Index(fields=['reaction_type']),
        ]
    
    def __str__(self):
        return f"{self.user.username} {self.get_reaction_type_display()} {self.message.id}"


class UserOnlineStatus(BaseModel):
    """用户在线状态模型"""
    
    STATUS_CHOICES = [
        ('online', '在线'),
        ('away', '离开'),
        ('busy', '忙碌'),
        ('invisible', '隐身'),
        ('offline', '离线'),
    ]
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='online_status',
        verbose_name='用户'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='offline',
        verbose_name='状态'
    )
    
    # 状态信息
    status_message = models.CharField(max_length=100, blank=True, verbose_name='状态消息')
    last_seen_at = models.DateTimeField(auto_now=True, verbose_name='最后在线时间')
    
    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name='设备类型')
    device_info = models.JSONField(default=dict, verbose_name='设备信息')
    
    class Meta:
        db_table = 'messaging_user_online_status'
        verbose_name = '用户在线状态'
        verbose_name_plural = '用户在线状态'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['last_seen_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_status_display()}"
    
    def set_online(self, device_type='web', device_info=None):
        """设置为在线"""
        self.status = 'online'
        self.device_type = device_type
        self.device_info = device_info or {}
        self.save()
    
    def set_offline(self):
        """设置为离线"""
        self.status = 'offline'
        self.save()


class MessageDeliveryStatus(BaseModel):
    """消息送达状态模型"""
    
    STATUS_CHOICES = [
        ('pending', '待送达'),
        ('delivered', '已送达'),
        ('read', '已读'),
        ('failed', '失败'),
    ]
    
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='delivery_statuses',
        verbose_name='消息'
    )
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='message_delivery_statuses',
        verbose_name='接收者'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    
    # 时间记录
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='送达时间')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    # 失败信息
    failure_reason = models.CharField(max_length=200, blank=True, verbose_name='失败原因')
    retry_count = models.PositiveIntegerField(default=0, verbose_name='重试次数')
    
    class Meta:
        db_table = 'messaging_message_delivery_status'
        verbose_name = '消息送达状态'
        verbose_name_plural = '消息送达状态'
        unique_together = ['message', 'recipient']
        indexes = [
            models.Index(fields=['message']),
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.message.id} -> {self.recipient.username} ({self.status})"
    
    def mark_delivered(self):
        """标记为已送达"""
        if self.status == 'pending':
            self.status = 'delivered'
            self.delivered_at = timezone.now()
            self.save()
    
    def mark_read(self):
        """标记为已读"""
        if self.status in ['pending', 'delivered']:
            self.status = 'read'
            self.read_at = timezone.now()
            self.save()
