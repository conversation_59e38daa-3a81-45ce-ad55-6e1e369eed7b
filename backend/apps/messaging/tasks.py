"""
消息异步任务 - 后台处理任务
职责：处理消息相关的异步任务，如消息推送、离线消息处理、消息清理等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_message_push_notification(self, message_id: int, recipient_ids: list):
    """
    发送消息推送通知
    
    Args:
        message_id: 消息ID
        recipient_ids: 接收者ID列表
    """
    try:
        from .models import Message, MessageDeliveryStatus
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        message = Message.objects.select_related('sender', 'conversation').get(id=message_id)
        
        for recipient_id in recipient_ids:
            try:
                recipient = User.objects.get(id=recipient_id)
                
                # 检查用户通知偏好
                if hasattr(recipient, 'preferences'):
                    notification_settings = recipient.preferences.notification_settings
                    if not notification_settings.get('message_notifications', True):
                        continue
                
                # 检查用户是否在线
                online_status = getattr(recipient, 'online_status', None)
                if online_status and online_status.status == 'online':
                    # 用户在线，发送实时推送
                    self._send_realtime_push(message, recipient)
                else:
                    # 用户离线，发送离线通知
                    self._send_offline_notification(message, recipient)
                
                # 更新送达状态
                MessageDeliveryStatus.objects.filter(
                    message=message,
                    recipient=recipient
                ).update(
                    status='delivered',
                    delivered_at=timezone.now()
                )
                
            except User.DoesNotExist:
                logger.warning(f"接收者不存在: {recipient_id}")
                continue
            except Exception as e:
                logger.error(f"发送推送通知给用户 {recipient_id} 失败: {e}")
                continue
        
        logger.info(f"消息推送通知发送完成: {message_id}")
        
    except Exception as exc:
        logger.error(f"发送消息推送通知失败: {message_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


def _send_realtime_push(message, recipient):
    """发送实时推送"""
    # 这里可以集成WebSocket、Firebase、APNs等实时推送服务
    push_data = {
        'type': 'new_message',
        'message_id': str(message.uuid),
        'conversation_id': str(message.conversation.uuid),
        'sender': message.sender.username,
        'content': message.content[:100] + '...' if len(message.content) > 100 else message.content,
        'message_type': message.message_type,
        'sent_at': message.sent_at.isoformat()
    }
    
    # 发送到WebSocket频道
    # channel_layer.group_send(f"user_{recipient.id}", push_data)
    
    logger.info(f"实时推送发送: {recipient.username}")


def _send_offline_notification(message, recipient):
    """发送离线通知"""
    # 发送邮件通知
    if recipient.email:
        try:
            subject = f'来自 {message.sender.username} 的新消息'
            html_message = render_to_string('emails/new_message.html', {
                'message': message,
                'sender': message.sender,
                'recipient': recipient,
                'conversation': message.conversation,
                'site_name': 'SOIC',
                'site_url': settings.FRONTEND_URL,
                'conversation_url': f"{settings.FRONTEND_URL}/messages/{message.conversation.uuid}"
            })
            
            send_mail(
                subject=subject,
                message='',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient.email],
                html_message=html_message,
                fail_silently=True
            )
            
            logger.info(f"离线邮件通知发送: {recipient.email}")
            
        except Exception as e:
            logger.error(f"发送离线邮件通知失败: {e}")


@shared_task
def process_offline_messages(user_id: int):
    """
    处理离线消息
    
    Args:
        user_id: 用户ID
    """
    try:
        from .models import MessageDeliveryStatus, UserOnlineStatus
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 获取用户的离线消息
        offline_messages = MessageDeliveryStatus.objects.filter(
            recipient=user,
            status='pending'
        ).select_related('message', 'message__sender', 'message__conversation')
        
        processed_count = 0
        for delivery_status in offline_messages:
            try:
                # 标记为已送达
                delivery_status.mark_delivered()
                
                # 发送推送通知
                send_message_push_notification.delay(
                    delivery_status.message.id,
                    [user.id]
                )
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"处理离线消息失败: {delivery_status.id}, 错误: {e}")
                continue
        
        logger.info(f"用户 {user.username} 离线消息处理完成: {processed_count}条")
        
        return processed_count
        
    except Exception as exc:
        logger.error(f"处理离线消息失败: {user_id}, 错误: {exc}")
        return 0


@shared_task
def cleanup_old_messages():
    """
    清理旧消息
    """
    try:
        from .models import Message, MessageDeliveryStatus
        
        # 清理30天前的已删除消息
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_messages = Message.objects.filter(
            status='deleted',
            updated_at__lt=cutoff_date
        )
        
        deleted_count = deleted_messages.count()
        deleted_messages.delete()
        
        # 清理90天前的送达状态记录
        old_cutoff_date = timezone.now() - timedelta(days=90)
        old_delivery_statuses = MessageDeliveryStatus.objects.filter(
            created_at__lt=old_cutoff_date
        )
        
        delivery_deleted_count = old_delivery_statuses.count()
        old_delivery_statuses.delete()
        
        logger.info(f"消息清理完成: 删除了 {deleted_count} 条消息，{delivery_deleted_count} 条送达记录")
        
        return {
            'deleted_messages': deleted_count,
            'deleted_delivery_statuses': delivery_deleted_count
        }
        
    except Exception as exc:
        logger.error(f"清理旧消息失败: {exc}")
        return {'deleted_messages': 0, 'deleted_delivery_statuses': 0}


@shared_task
def update_conversation_statistics():
    """
    更新会话统计信息
    """
    try:
        from .models import Conversation, Message, ConversationParticipant
        
        # 更新会话消息数量
        conversations = Conversation.objects.filter(is_active=True)
        
        for conversation in conversations:
            try:
                # 统计消息数量
                message_count = Message.objects.filter(
                    conversation=conversation,
                    status__in=['sent', 'delivered', 'read']
                ).count()
                
                # 获取最后一条消息
                last_message = Message.objects.filter(
                    conversation=conversation,
                    status__in=['sent', 'delivered', 'read']
                ).order_by('-sent_at').first()
                
                # 更新会话信息
                conversation.message_count = message_count
                if last_message:
                    conversation.last_message = last_message
                    conversation.last_message_at = last_message.sent_at
                
                conversation.save(update_fields=['message_count', 'last_message', 'last_message_at'])
                
            except Exception as e:
                logger.error(f"更新会话 {conversation.id} 统计失败: {e}")
                continue
        
        logger.info(f"会话统计更新完成: 处理了 {len(conversations)} 个会话")
        
    except Exception as exc:
        logger.error(f"更新会话统计失败: {exc}")


@shared_task
def sync_user_online_status():
    """
    同步用户在线状态
    """
    try:
        from .models import UserOnlineStatus
        
        # 将超过5分钟没有活动的用户标记为离线
        inactive_threshold = timezone.now() - timedelta(minutes=5)
        
        updated_count = UserOnlineStatus.objects.filter(
            status__in=['online', 'away'],
            last_seen_at__lt=inactive_threshold
        ).update(status='offline')
        
        logger.info(f"在线状态同步完成: {updated_count} 个用户被标记为离线")
        
        return updated_count
        
    except Exception as exc:
        logger.error(f"同步用户在线状态失败: {exc}")
        return 0


@shared_task
def generate_conversation_summary(conversation_id: int, days: int = 7):
    """
    生成会话摘要
    
    Args:
        conversation_id: 会话ID
        days: 统计天数
    """
    try:
        from .models import Conversation, Message, ConversationParticipant
        
        conversation = Conversation.objects.get(id=conversation_id)
        
        # 统计时间范围
        start_date = timezone.now() - timedelta(days=days)
        
        # 统计消息数量
        messages = Message.objects.filter(
            conversation=conversation,
            sent_at__gte=start_date,
            status__in=['sent', 'delivered', 'read']
        )
        
        total_messages = messages.count()
        
        # 统计各类型消息
        message_types = messages.values('message_type').annotate(
            count=Count('id')
        )
        
        # 统计参与者活跃度
        participant_stats = messages.values('sender__username').annotate(
            message_count=Count('id')
        ).order_by('-message_count')
        
        # 统计活跃时间段
        hourly_stats = messages.extra(
            select={'hour': 'EXTRACT(hour FROM sent_at)'}
        ).values('hour').annotate(
            count=Count('id')
        ).order_by('hour')
        
        # 生成摘要
        summary = {
            'conversation_id': str(conversation.uuid),
            'conversation_name': conversation.display_name,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': timezone.now().isoformat(),
                'days': days
            },
            'statistics': {
                'total_messages': total_messages,
                'message_types': {item['message_type']: item['count'] for item in message_types},
                'participant_stats': list(participant_stats),
                'hourly_distribution': {str(item['hour']): item['count'] for item in hourly_stats}
            },
            'generated_at': timezone.now().isoformat()
        }
        
        # 存储摘要到会话元数据
        conversation.set_metadata(f'summary_{days}d', summary)
        
        logger.info(f"会话摘要生成完成: {conversation.display_name}")
        
        return summary
        
    except Exception as exc:
        logger.error(f"生成会话摘要失败: {conversation_id}, 错误: {exc}")
        return None


@shared_task
def backup_conversation_data(conversation_id: int):
    """
    备份会话数据
    
    Args:
        conversation_id: 会话ID
    """
    try:
        from .models import Conversation, Message, ConversationParticipant
        import json
        
        conversation = Conversation.objects.get(id=conversation_id)
        
        # 收集会话数据
        backup_data = {
            'conversation': {
                'id': str(conversation.uuid),
                'name': conversation.name,
                'conversation_type': conversation.conversation_type,
                'is_active': conversation.is_active,
                'message_count': conversation.message_count,
                'created_at': conversation.created_at.isoformat()
            },
            'participants': [],
            'messages': []
        }
        
        # 收集参与者数据
        participants = ConversationParticipant.objects.filter(
            conversation=conversation
        ).select_related('user')
        
        for participant in participants:
            participant_data = {
                'user_id': str(participant.user.uuid),
                'username': participant.user.username,
                'role': participant.role,
                'status': participant.status,
                'joined_at': participant.joined_at.isoformat()
            }
            backup_data['participants'].append(participant_data)
        
        # 收集消息数据
        messages = Message.objects.filter(
            conversation=conversation,
            status__in=['sent', 'delivered', 'read']
        ).select_related('sender').order_by('sent_at')
        
        for message in messages:
            message_data = {
                'id': str(message.uuid),
                'sender_username': message.sender.username,
                'message_type': message.message_type,
                'content': message.content,
                'sent_at': message.sent_at.isoformat(),
                'is_important': message.is_important
            }
            
            if message.media_url:
                message_data['media'] = {
                    'url': message.media_url,
                    'thumbnail_url': message.media_thumbnail_url,
                    'size': message.media_size
                }
            
            backup_data['messages'].append(message_data)
        
        # 生成备份文件名
        backup_filename = f"conversation_backup_{conversation.uuid}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 保存备份数据（这里可以保存到文件系统或云存储）
        # with open(f"/backups/{backup_filename}", 'w') as f:
        #     json.dump(backup_data, f, indent=2, ensure_ascii=False)
        
        # 更新会话元数据
        conversation.set_metadata('last_backup', {
            'filename': backup_filename,
            'timestamp': timezone.now().isoformat(),
            'message_count': len(backup_data['messages']),
            'participant_count': len(backup_data['participants'])
        })
        
        logger.info(f"会话备份完成: {conversation.display_name} -> {backup_filename}")
        
        return backup_filename
        
    except Exception as exc:
        logger.error(f"备份会话数据失败: {conversation_id}, 错误: {exc}")
        return None


# 事件处理器

from apps.core.events import event_handler

@event_handler('messaging.message_sent', async_handler=True)
def handle_message_sent(event):
    """处理消息发送事件"""
    message_id = event.data.get('message_id')
    conversation_id = event.data.get('conversation_id')
    
    if message_id and conversation_id:
        # 获取会话参与者
        from .models import ConversationParticipant
        
        participants = ConversationParticipant.objects.filter(
            conversation__uuid=conversation_id,
            status='active'
        ).exclude(
            user__uuid=event.data.get('sender_id')
        ).values_list('user_id', flat=True)
        
        # 发送推送通知
        if participants:
            send_message_push_notification.delay(int(message_id), list(participants))


@event_handler('messaging.user_status_changed', async_handler=True)
def handle_user_status_changed(event):
    """处理用户状态变更事件"""
    user_id = event.data.get('user_id')
    new_status = event.data.get('new_status')
    
    if user_id and new_status == 'online':
        # 用户上线，处理离线消息
        process_offline_messages.delay(int(user_id))


@event_handler('messaging.conversation_created', async_handler=True)
def handle_conversation_created(event):
    """处理会话创建事件"""
    conversation_id = event.data.get('conversation_id')
    
    if conversation_id:
        # 初始化会话统计
        update_conversation_statistics.delay()


@event_handler('messaging.message_read', async_handler=True)
def handle_message_read(event):
    """处理消息已读事件"""
    # 可以在这里添加已读回执的处理逻辑
    pass
