"""
消息权限控制 - 访问控制和权限验证
职责：定义消息相关的权限类，控制API访问权限
"""

from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView
from django.contrib.auth.models import AnonymousUser
from .models import Conversation, ConversationParticipant, Message


class CanAccessConversation(permissions.BasePermission):
    """
    可以访问会话的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        # 检查是否为会话参与者
        return ConversationParticipant.objects.filter(
            conversation=conversation,
            user=request.user,
            status='active'
        ).exists()


class CanSendMessage(permissions.BasePermission):
    """
    可以发送消息的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        # 检查会话是否活跃
        if not conversation.is_active:
            return False
        
        # 检查是否为活跃参与者
        try:
            participant = ConversationParticipant.objects.get(
                conversation=conversation,
                user=request.user,
                status='active'
            )
            
            # 检查是否被禁言
            return participant.status != 'banned'
            
        except ConversationParticipant.DoesNotExist:
            return False


class CanEditMessage(permissions.BasePermission):
    """
    可以编辑消息的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        message = obj if isinstance(obj, Message) else obj
        
        # 只有消息发送者可以编辑
        if message.sender != request.user:
            return False
        
        # 检查消息状态
        if message.status == 'deleted':
            return False
        
        # 检查时间限制（例如：只能在发送后30分钟内编辑）
        from django.utils import timezone
        from datetime import timedelta
        
        edit_deadline = message.sent_at + timedelta(minutes=30)
        if timezone.now() > edit_deadline:
            return False
        
        return True


class CanDeleteMessage(permissions.BasePermission):
    """
    可以删除消息的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        message = obj if isinstance(obj, Message) else obj
        
        # 消息发送者可以删除
        if message.sender == request.user:
            return True
        
        # 会话管理员可以删除
        try:
            participant = ConversationParticipant.objects.get(
                conversation=message.conversation,
                user=request.user,
                status='active'
            )
            return participant.role in ['owner', 'admin']
            
        except ConversationParticipant.DoesNotExist:
            return False


class CanManageConversation(permissions.BasePermission):
    """
    可以管理会话的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        try:
            participant = ConversationParticipant.objects.get(
                conversation=conversation,
                user=request.user,
                status='active'
            )
            
            # 所有者和管理员可以管理会话
            return participant.role in ['owner', 'admin']
            
        except ConversationParticipant.DoesNotExist:
            return False


class CanInviteToConversation(permissions.BasePermission):
    """
    可以邀请用户加入会话的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        # 私聊不能邀请
        if conversation.conversation_type == 'private':
            return False
        
        try:
            participant = ConversationParticipant.objects.get(
                conversation=conversation,
                user=request.user,
                status='active'
            )
            
            # 所有者、管理员和普通成员（如果允许）可以邀请
            if participant.role in ['owner', 'admin']:
                return True
            
            # 检查会话设置是否允许成员邀请
            return conversation.allow_member_invite if hasattr(conversation, 'allow_member_invite') else False
            
        except ConversationParticipant.DoesNotExist:
            return False


class IsConversationOwner(permissions.BasePermission):
    """
    会话所有者权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        try:
            participant = ConversationParticipant.objects.get(
                conversation=conversation,
                user=request.user,
                status='active'
            )
            return participant.role == 'owner'
            
        except ConversationParticipant.DoesNotExist:
            return False


class CanViewMessageHistory(permissions.BasePermission):
    """
    可以查看消息历史的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        conversation = obj if isinstance(obj, Conversation) else obj.conversation
        
        # 检查是否为会话参与者
        participant = ConversationParticipant.objects.filter(
            conversation=conversation,
            user=request.user
        ).first()
        
        if not participant:
            return False
        
        # 活跃参与者可以查看
        if participant.status == 'active':
            return True
        
        # 已离开的参与者只能查看加入期间的消息
        if participant.status == 'left':
            # 这里可以添加更复杂的逻辑来限制查看范围
            return True
        
        return False


# 权限检查函数

def can_access_conversation(user, conversation: Conversation) -> bool:
    """
    检查用户是否可以访问会话
    
    Args:
        user: 用户对象
        conversation: 会话对象
        
    Returns:
        bool: 是否可以访问
    """
    if isinstance(user, AnonymousUser):
        return False
    
    return ConversationParticipant.objects.filter(
        conversation=conversation,
        user=user,
        status='active'
    ).exists()


def can_send_message_to_conversation(user, conversation: Conversation) -> bool:
    """
    检查用户是否可以向会话发送消息
    
    Args:
        user: 用户对象
        conversation: 会话对象
        
    Returns:
        bool: 是否可以发送消息
    """
    if isinstance(user, AnonymousUser):
        return False
    
    if not conversation.is_active:
        return False
    
    try:
        participant = ConversationParticipant.objects.get(
            conversation=conversation,
            user=user,
            status='active'
        )
        return participant.status != 'banned'
        
    except ConversationParticipant.DoesNotExist:
        return False


def can_edit_message(user, message: Message) -> bool:
    """
    检查用户是否可以编辑消息
    
    Args:
        user: 用户对象
        message: 消息对象
        
    Returns:
        bool: 是否可以编辑
    """
    if isinstance(user, AnonymousUser):
        return False
    
    # 只有消息发送者可以编辑
    if message.sender != user:
        return False
    
    # 检查消息状态
    if message.status == 'deleted':
        return False
    
    # 检查时间限制
    from django.utils import timezone
    from datetime import timedelta
    
    edit_deadline = message.sent_at + timedelta(minutes=30)
    return timezone.now() <= edit_deadline


def can_delete_message(user, message: Message) -> bool:
    """
    检查用户是否可以删除消息
    
    Args:
        user: 用户对象
        message: 消息对象
        
    Returns:
        bool: 是否可以删除
    """
    if isinstance(user, AnonymousUser):
        return False
    
    # 消息发送者可以删除
    if message.sender == user:
        return True
    
    # 会话管理员可以删除
    try:
        participant = ConversationParticipant.objects.get(
            conversation=message.conversation,
            user=user,
            status='active'
        )
        return participant.role in ['owner', 'admin']
        
    except ConversationParticipant.DoesNotExist:
        return False


def can_manage_conversation(user, conversation: Conversation) -> bool:
    """
    检查用户是否可以管理会话
    
    Args:
        user: 用户对象
        conversation: 会话对象
        
    Returns:
        bool: 是否可以管理
    """
    if isinstance(user, AnonymousUser):
        return False
    
    try:
        participant = ConversationParticipant.objects.get(
            conversation=conversation,
            user=user,
            status='active'
        )
        return participant.role in ['owner', 'admin']
        
    except ConversationParticipant.DoesNotExist:
        return False


def get_user_conversation_role(user, conversation: Conversation) -> str:
    """
    获取用户在会话中的角色
    
    Args:
        user: 用户对象
        conversation: 会话对象
        
    Returns:
        str: 用户角色，如果不是成员返回None
    """
    if isinstance(user, AnonymousUser):
        return None
    
    try:
        participant = ConversationParticipant.objects.get(
            conversation=conversation,
            user=user,
            status='active'
        )
        return participant.role
        
    except ConversationParticipant.DoesNotExist:
        return None


def get_user_conversation_permissions(user, conversation: Conversation) -> dict:
    """
    获取用户在会话中的权限
    
    Args:
        user: 用户对象
        conversation: 会话对象
        
    Returns:
        dict: 权限字典
    """
    if isinstance(user, AnonymousUser):
        return {
            'can_access': False,
            'can_send_message': False,
            'can_manage': False,
            'can_invite': False,
            'role': None
        }
    
    try:
        participant = ConversationParticipant.objects.get(
            conversation=conversation,
            user=user,
            status='active'
        )
        
        is_owner_or_admin = participant.role in ['owner', 'admin']
        
        return {
            'can_access': True,
            'can_send_message': participant.status != 'banned' and conversation.is_active,
            'can_manage': is_owner_or_admin,
            'can_invite': is_owner_or_admin or (
                conversation.conversation_type != 'private' and 
                getattr(conversation, 'allow_member_invite', False)
            ),
            'role': participant.role
        }
        
    except ConversationParticipant.DoesNotExist:
        return {
            'can_access': False,
            'can_send_message': False,
            'can_manage': False,
            'can_invite': False,
            'role': None
        }
