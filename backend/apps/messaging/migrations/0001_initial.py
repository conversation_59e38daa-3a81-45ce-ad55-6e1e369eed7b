# Generated by Django 5.2 on 2025-07-30 16:06

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='会话名称')),
                ('conversation_type', models.CharField(choices=[('private', '私聊'), ('group', '群聊'), ('system', '系统消息'), ('space', '空间聊天')], max_length=20, verbose_name='会话类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('is_archived', models.BooleanField(default=False, verbose_name='是否归档')),
                ('is_muted', models.BooleanField(default=False, verbose_name='是否静音')),
                ('last_message_at', models.DateTimeField(blank=True, null=True, verbose_name='最后消息时间')),
                ('message_count', models.PositiveIntegerField(default=0, verbose_name='消息数量')),
                ('related_space_id', models.CharField(blank=True, max_length=36, verbose_name='关联空间ID')),
                ('related_group_id', models.CharField(blank=True, max_length=36, verbose_name='关联群组ID')),
            ],
            options={
                'verbose_name': '会话',
                'verbose_name_plural': '会话',
                'db_table': 'messaging_conversations',
            },
        ),
        migrations.CreateModel(
            name='ConversationParticipant',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('role', models.CharField(choices=[('owner', '所有者'), ('admin', '管理员'), ('member', '成员')], default='member', max_length=20, verbose_name='角色')),
                ('status', models.CharField(choices=[('active', '活跃'), ('left', '已离开'), ('kicked', '被踢出'), ('banned', '被禁言')], default='active', max_length=20, verbose_name='状态')),
                ('is_muted', models.BooleanField(default=False, verbose_name='是否静音')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='是否置顶')),
                ('last_read_at', models.DateTimeField(blank=True, null=True, verbose_name='最后阅读时间')),
                ('unread_count', models.PositiveIntegerField(default=0, verbose_name='未读数量')),
                ('joined_at', models.DateTimeField(auto_now_add=True, verbose_name='加入时间')),
                ('left_at', models.DateTimeField(blank=True, null=True, verbose_name='离开时间')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_participants', to='messaging.conversation', verbose_name='会话')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_participations', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '会话参与者',
                'verbose_name_plural': '会话参与者',
                'db_table': 'messaging_conversation_participants',
            },
        ),
        migrations.AddField(
            model_name='conversation',
            name='participants',
            field=models.ManyToManyField(related_name='conversations', through='messaging.ConversationParticipant', to=settings.AUTH_USER_MODEL, verbose_name='参与者'),
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('message_type', models.CharField(choices=[('text', '文本消息'), ('image', '图片消息'), ('video', '视频消息'), ('audio', '音频消息'), ('file', '文件消息'), ('location', '位置消息'), ('system', '系统消息'), ('emoji', '表情消息'), ('reply', '回复消息')], default='text', max_length=20, verbose_name='消息类型')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('media_url', models.URLField(blank=True, verbose_name='媒体文件URL')),
                ('media_thumbnail_url', models.URLField(blank=True, verbose_name='媒体缩略图URL')),
                ('media_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='媒体文件大小')),
                ('media_duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='媒体时长(秒)')),
                ('status', models.CharField(choices=[('sent', '已发送'), ('delivered', '已送达'), ('read', '已读'), ('failed', '发送失败'), ('deleted', '已删除')], default='sent', max_length=20, verbose_name='状态')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='发送时间')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='送达时间')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='阅读时间')),
                ('is_edited', models.BooleanField(default=False, verbose_name='是否已编辑')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='是否置顶')),
                ('is_important', models.BooleanField(default=False, verbose_name='是否重要')),
                ('read_count', models.PositiveIntegerField(default=0, verbose_name='阅读数量')),
                ('reaction_count', models.PositiveIntegerField(default=0, verbose_name='反应数量')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='messaging.conversation', verbose_name='会话')),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='messaging.message', verbose_name='回复的消息')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL, verbose_name='发送者')),
            ],
            options={
                'verbose_name': '消息',
                'verbose_name_plural': '消息',
                'db_table': 'messaging_messages',
            },
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='last_read_message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='read_by_participants', to='messaging.message', verbose_name='最后阅读消息'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='last_message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='last_message_conversations', to='messaging.message', verbose_name='最后一条消息'),
        ),
        migrations.CreateModel(
            name='MessageDeliveryStatus',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('pending', '待送达'), ('delivered', '已送达'), ('read', '已读'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='送达时间')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='阅读时间')),
                ('failure_reason', models.CharField(blank=True, max_length=200, verbose_name='失败原因')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='重试次数')),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_statuses', to='messaging.message', verbose_name='消息')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_delivery_statuses', to=settings.AUTH_USER_MODEL, verbose_name='接收者')),
            ],
            options={
                'verbose_name': '消息送达状态',
                'verbose_name_plural': '消息送达状态',
                'db_table': 'messaging_message_delivery_status',
            },
        ),
        migrations.CreateModel(
            name='MessageReaction',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('reaction_type', models.CharField(choices=[('like', '👍'), ('love', '❤️'), ('laugh', '😂'), ('wow', '😮'), ('sad', '😢'), ('angry', '😠')], max_length=20, verbose_name='反应类型')),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='messaging.message', verbose_name='消息')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_reactions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '消息反应',
                'verbose_name_plural': '消息反应',
                'db_table': 'messaging_message_reactions',
            },
        ),
        migrations.CreateModel(
            name='UserOnlineStatus',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('online', '在线'), ('away', '离开'), ('busy', '忙碌'), ('invisible', '隐身'), ('offline', '离线')], default='offline', max_length=20, verbose_name='状态')),
                ('status_message', models.CharField(blank=True, max_length=100, verbose_name='状态消息')),
                ('last_seen_at', models.DateTimeField(auto_now=True, verbose_name='最后在线时间')),
                ('device_type', models.CharField(blank=True, max_length=50, verbose_name='设备类型')),
                ('device_info', models.JSONField(default=dict, verbose_name='设备信息')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='online_status', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户在线状态',
                'verbose_name_plural': '用户在线状态',
                'db_table': 'messaging_user_online_status',
            },
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['conversation', '-sent_at'], name='messaging_m_convers_117f15_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender'], name='messaging_m_sender__bd59d4_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['message_type'], name='messaging_m_message_8bc761_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['status'], name='messaging_m_status_fff296_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['reply_to'], name='messaging_m_reply_t_bcbe67_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['is_pinned'], name='messaging_m_is_pinn_4e2ca2_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['conversation', 'status'], name='messaging_c_convers_d4d4e3_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['user', 'status'], name='messaging_c_user_id_34ffe8_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['unread_count'], name='messaging_c_unread__e328bc_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['is_pinned'], name='messaging_c_is_pinn_3ee04a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='conversationparticipant',
            unique_together={('conversation', 'user')},
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['conversation_type'], name='messaging_c_convers_98bb44_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['is_active'], name='messaging_c_is_acti_3b2de5_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['-last_message_at'], name='messaging_c_last_me_32fa48_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['related_space_id'], name='messaging_c_related_0f66b4_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['related_group_id'], name='messaging_c_related_021310_idx'),
        ),
        migrations.AddIndex(
            model_name='messagedeliverystatus',
            index=models.Index(fields=['message'], name='messaging_m_message_edc5d7_idx'),
        ),
        migrations.AddIndex(
            model_name='messagedeliverystatus',
            index=models.Index(fields=['recipient', 'status'], name='messaging_m_recipie_7584a9_idx'),
        ),
        migrations.AddIndex(
            model_name='messagedeliverystatus',
            index=models.Index(fields=['status'], name='messaging_m_status_d621a9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagedeliverystatus',
            unique_together={('message', 'recipient')},
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['message'], name='messaging_m_message_75bab5_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['user'], name='messaging_m_user_id_8d6555_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereaction',
            index=models.Index(fields=['reaction_type'], name='messaging_m_reactio_f2f304_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagereaction',
            unique_together={('message', 'user', 'reaction_type')},
        ),
        migrations.AddIndex(
            model_name='useronlinestatus',
            index=models.Index(fields=['status'], name='messaging_u_status_862982_idx'),
        ),
        migrations.AddIndex(
            model_name='useronlinestatus',
            index=models.Index(fields=['last_seen_at'], name='messaging_u_last_se_e3e188_idx'),
        ),
    ]
