"""
消息应用配置 - 消息域应用配置
职责：配置消息应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class MessagingConfig(AppConfig):
    """消息应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.messaging'
    verbose_name = '实时消息通信'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        # 初始化WebSocket支持
        self.initialize_websocket_support()
        
        logger.info("消息应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import MessagingService
            
            # 注册消息服务为单例
            container.register_singleton(MessagingService, MessagingService)
            
            logger.info("消息服务注册完成")
            
        except Exception as e:
            logger.error(f"消息服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入信号处理器（这会自动注册Django信号）
            from . import signals

            logger.info("消息事件处理器注册完成")

        except Exception as e:
            logger.error(f"消息事件处理器注册失败: {e}")
    
    def initialize_websocket_support(self):
        """初始化WebSocket支持"""
        try:
            # 这里可以初始化WebSocket相关的配置
            # 例如：Django Channels的路由配置
            
            logger.info("WebSocket支持初始化完成")
            
        except Exception as e:
            logger.error(f"WebSocket支持初始化失败: {e}")
