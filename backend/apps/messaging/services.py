"""
消息业务服务 - 高内聚的实时通信业务逻辑
职责：处理消息发送、会话管理、在线状态、消息同步等核心业务逻辑
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Max, F
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent
from apps.core.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    ForbiddenException,
    ConflictException
)
from apps.core.dependencies import inject
from .models import (
    Conversation, ConversationParticipant, Message, MessageReaction,
    UserOnlineStatus, MessageDeliveryStatus
)

logger = logging.getLogger(__name__)


class MessagingService:
    """
    消息业务服务 - 高内聚的消息相关业务逻辑
    
    职责：
    1. 消息发送和接收
    2. 会话创建和管理
    3. 在线状态管理
    4. 消息状态跟踪
    5. 实时通信协调
    """
    
    @transaction.atomic
    def send_message(self, sender_id: str, conversation_id: str, 
                    message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送消息
        
        Args:
            sender_id: 发送者ID
            conversation_id: 会话ID
            message_data: 消息数据
            
        Returns:
            Dict: 发送结果
            
        Raises:
            ValidationException: 数据验证失败
            ForbiddenException: 无权发送消息
        """
        logger.info(f"发送消息: {sender_id} -> {conversation_id}")
        
        # 验证发送者和会话
        sender, conversation = self._validate_sender_and_conversation(sender_id, conversation_id)
        
        # 检查发送权限
        self._check_send_permission(sender, conversation)
        
        # 验证消息数据
        self._validate_message_data(message_data)
        
        # 创建消息
        message = self._create_message(sender, conversation, message_data)
        
        # 更新会话信息
        self._update_conversation_last_message(conversation, message)
        
        # 更新参与者未读数
        self._update_participants_unread_count(conversation, message, sender)
        
        # 创建送达状态记录
        self._create_delivery_statuses(message, conversation)
        
        # 发布消息发送事件
        self._publish_message_sent_event(message)
        
        logger.info(f"消息发送成功: {message.id}")
        
        return {
            'message_id': str(message.uuid),
            'conversation_id': str(conversation.uuid),
            'sent_at': message.sent_at.isoformat(),
            'status': message.status
        }
    
    def get_conversation_messages(self, user_id: str, conversation_id: str,
                                page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        获取会话消息列表
        
        Args:
            user_id: 用户ID
            conversation_id: 会话ID
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 消息列表
        """
        try:
            conversation = Conversation.objects.get(uuid=conversation_id)
        except Conversation.DoesNotExist:
            raise ResourceNotFoundException(f"会话不存在: {conversation_id}", "CONVERSATION")
        
        # 权限检查
        if not self._can_access_conversation(user_id, conversation):
            raise ForbiddenException("无权访问该会话")
        
        # 获取消息列表
        messages = Message.objects.filter(
            conversation=conversation,
            status__in=['sent', 'delivered', 'read']
        ).select_related('sender', 'reply_to').order_by('-sent_at')
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(messages, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化消息
        message_list = []
        for message in page_obj.object_list:
            message_data = self._serialize_message(message, user_id)
            message_list.append(message_data)
        
        # 标记消息为已读
        self._mark_messages_as_read(user_id, conversation, page_obj.object_list)
        
        return {
            'messages': message_list,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
    
    @transaction.atomic
    def create_conversation(self, creator_id: str, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建会话
        
        Args:
            creator_id: 创建者ID
            conversation_data: 会话数据
            
        Returns:
            Dict: 创建的会话信息
        """
        logger.info(f"创建会话: {creator_id}")
        
        # 验证创建者
        creator = self._get_user_by_id(creator_id)
        
        # 验证会话数据
        self._validate_conversation_data(conversation_data)
        
        # 检查是否为私聊且已存在
        if conversation_data['conversation_type'] == 'private':
            existing_conversation = self._find_existing_private_conversation(
                creator_id, conversation_data.get('participant_ids', [])
            )
            if existing_conversation:
                return {
                    'conversation_id': str(existing_conversation.uuid),
                    'existed': True,
                    'message': '私聊会话已存在'
                }
        
        # 创建会话
        conversation = self._create_conversation_entity(creator, conversation_data)
        
        # 添加参与者
        self._add_conversation_participants(conversation, creator, conversation_data)
        
        # 发布会话创建事件
        self._publish_conversation_created_event(conversation)
        
        logger.info(f"会话创建成功: {conversation.id}")
        
        return {
            'conversation_id': str(conversation.uuid),
            'name': conversation.display_name,
            'conversation_type': conversation.conversation_type,
            'participant_count': conversation.participants.count(),
            'created_at': conversation.created_at.isoformat()
        }
    
    def get_user_conversations(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List: 会话列表
        """
        user = self._get_user_by_id(user_id)
        
        # 获取用户参与的会话
        participations = ConversationParticipant.objects.filter(
            user=user,
            status='active'
        ).select_related('conversation').prefetch_related(
            'conversation__participants'
        ).order_by('-conversation__last_message_at')
        
        conversations = []
        for participation in participations:
            conversation_data = self._serialize_conversation(participation.conversation, user_id)
            conversation_data['user_participation'] = {
                'role': participation.role,
                'is_muted': participation.is_muted,
                'is_pinned': participation.is_pinned,
                'unread_count': participation.unread_count,
                'last_read_at': participation.last_read_at.isoformat() if participation.last_read_at else None
            }
            conversations.append(conversation_data)
        
        return conversations
    
    @transaction.atomic
    def update_online_status(self, user_id: str, status: str, 
                           device_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        更新用户在线状态
        
        Args:
            user_id: 用户ID
            status: 在线状态
            device_info: 设备信息
            
        Returns:
            Dict: 状态更新结果
        """
        user = self._get_user_by_id(user_id)
        
        # 获取或创建在线状态
        online_status, created = UserOnlineStatus.objects.get_or_create(
            user=user,
            defaults={
                'status': status,
                'device_info': device_info or {}
            }
        )
        
        if not created:
            old_status = online_status.status
            online_status.status = status
            if device_info:
                online_status.device_info = device_info
            online_status.save()
            
            # 如果状态发生变化，发布事件
            if old_status != status:
                self._publish_user_status_changed_event(user, old_status, status)
        
        logger.info(f"用户状态更新: {user.username} -> {status}")
        
        return {
            'user_id': str(user.uuid),
            'status': status,
            'last_seen_at': online_status.last_seen_at.isoformat()
        }
    
    def mark_message_as_read(self, user_id: str, message_id: str) -> Dict[str, Any]:
        """
        标记消息为已读
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            
        Returns:
            Dict: 标记结果
        """
        try:
            message = Message.objects.get(uuid=message_id)
        except Message.DoesNotExist:
            raise ResourceNotFoundException(f"消息不存在: {message_id}", "MESSAGE")
        
        user = self._get_user_by_id(user_id)
        
        # 检查权限
        if not self._can_access_conversation(user_id, message.conversation):
            raise ForbiddenException("无权访问该消息")
        
        # 更新送达状态
        delivery_status = MessageDeliveryStatus.objects.filter(
            message=message,
            recipient=user
        ).first()
        
        if delivery_status:
            delivery_status.mark_read()
        
        # 更新参与者已读状态
        participant = ConversationParticipant.objects.filter(
            conversation=message.conversation,
            user=user,
            status='active'
        ).first()
        
        if participant:
            participant.mark_as_read(message)
        
        # 发布消息已读事件
        self._publish_message_read_event(message, user)
        
        return {
            'message_id': str(message.uuid),
            'read_at': timezone.now().isoformat(),
            'status': 'read'
        }
    
    @transaction.atomic
    def add_message_reaction(self, user_id: str, message_id: str, 
                           reaction_type: str) -> Dict[str, Any]:
        """
        添加消息反应
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            reaction_type: 反应类型
            
        Returns:
            Dict: 反应结果
        """
        try:
            message = Message.objects.get(uuid=message_id)
        except Message.DoesNotExist:
            raise ResourceNotFoundException(f"消息不存在: {message_id}", "MESSAGE")
        
        user = self._get_user_by_id(user_id)
        
        # 检查权限
        if not self._can_access_conversation(user_id, message.conversation):
            raise ForbiddenException("无权访问该消息")
        
        # 创建或更新反应
        reaction, created = MessageReaction.objects.get_or_create(
            message=message,
            user=user,
            reaction_type=reaction_type
        )
        
        if created:
            # 更新消息反应数量
            message.reaction_count = message.reactions.count()
            message.save(update_fields=['reaction_count'])
            
            # 发布反应添加事件
            self._publish_message_reaction_event(message, user, reaction_type, 'added')
        
        return {
            'message_id': str(message.uuid),
            'reaction_type': reaction_type,
            'created': created
        }
    
    def search_messages(self, user_id: str, query: str, conversation_id: str = None,
                       page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        搜索消息
        
        Args:
            user_id: 用户ID
            query: 搜索关键词
            conversation_id: 会话ID（可选）
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 搜索结果
        """
        user = self._get_user_by_id(user_id)
        
        # 获取用户可访问的会话
        accessible_conversations = Conversation.objects.filter(
            participants=user
        ).values_list('id', flat=True)
        
        # 构建搜索查询
        queryset = Message.objects.filter(
            conversation_id__in=accessible_conversations,
            status__in=['sent', 'delivered', 'read'],
            content__icontains=query
        ).select_related('sender', 'conversation')
        
        if conversation_id:
            queryset = queryset.filter(conversation__uuid=conversation_id)
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset.order_by('-sent_at'), page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化结果
        results = []
        for message in page_obj.object_list:
            message_data = self._serialize_message(message, user_id)
            message_data['conversation'] = {
                'id': str(message.conversation.uuid),
                'name': message.conversation.display_name,
                'type': message.conversation.conversation_type
            }
            results.append(message_data)
        
        return {
            'messages': results,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            },
            'query': query
        }
    
    def get_conversation_statistics(self, user_id: str, conversation_id: str) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Args:
            user_id: 用户ID
            conversation_id: 会话ID
            
        Returns:
            Dict: 统计信息
        """
        try:
            conversation = Conversation.objects.get(uuid=conversation_id)
        except Conversation.DoesNotExist:
            raise ResourceNotFoundException(f"会话不存在: {conversation_id}", "CONVERSATION")
        
        # 权限检查
        if not self._can_access_conversation(user_id, conversation):
            raise ForbiddenException("无权访问该会话")
        
        # 统计消息数量
        total_messages = conversation.messages.filter(
            status__in=['sent', 'delivered', 'read']
        ).count()
        
        # 统计各类型消息数量
        message_types = conversation.messages.filter(
            status__in=['sent', 'delivered', 'read']
        ).values('message_type').annotate(count=Count('id'))
        
        # 统计参与者活跃度
        participant_stats = ConversationParticipant.objects.filter(
            conversation=conversation,
            status='active'
        ).annotate(
            message_count=Count('user__sent_messages', filter=Q(user__sent_messages__conversation=conversation))
        ).values('user__username', 'message_count', 'unread_count')
        
        return {
            'conversation_id': str(conversation.uuid),
            'total_messages': total_messages,
            'participant_count': conversation.participants.filter(
                conversation_participations__status='active'
            ).count(),
            'message_types': {item['message_type']: item['count'] for item in message_types},
            'participant_stats': list(participant_stats),
            'created_at': conversation.created_at.isoformat(),
            'last_message_at': conversation.last_message_at.isoformat() if conversation.last_message_at else None
        }
    
    # 私有方法
    
    def _validate_sender_and_conversation(self, sender_id: str, conversation_id: str):
        """验证发送者和会话"""
        sender = self._get_user_by_id(sender_id)
        
        try:
            conversation = Conversation.objects.get(uuid=conversation_id)
        except Conversation.DoesNotExist:
            raise ResourceNotFoundException(f"会话不存在: {conversation_id}", "CONVERSATION")
        
        return sender, conversation
    
    def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            return User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
    
    def _check_send_permission(self, sender, conversation):
        """检查发送权限"""
        # 检查是否为会话参与者
        if not ConversationParticipant.objects.filter(
            conversation=conversation,
            user=sender,
            status='active'
        ).exists():
            raise ForbiddenException("您不是该会话的参与者")
        
        # 检查会话是否活跃
        if not conversation.is_active:
            raise ForbiddenException("会话已被禁用")
    
    def _validate_message_data(self, data: Dict[str, Any]):
        """验证消息数据"""
        required_fields = ['message_type', 'content']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )
        
        # 验证消息内容长度
        content = data['content']
        if len(content) > 10000:
            raise ValidationException("消息内容过长，最多10000个字符")
    
    def _create_message(self, sender, conversation, data: Dict[str, Any]) -> Message:
        """创建消息"""
        return Message.objects.create(
            conversation=conversation,
            sender=sender,
            message_type=data['message_type'],
            content=data['content'],
            media_url=data.get('media_url', ''),
            media_thumbnail_url=data.get('media_thumbnail_url', ''),
            media_size=data.get('media_size'),
            media_duration=data.get('media_duration'),
            reply_to_id=data.get('reply_to_id'),
            is_important=data.get('is_important', False)
        )

    def _update_conversation_last_message(self, conversation, message):
        """更新会话最后消息"""
        conversation.last_message = message
        conversation.last_message_at = message.sent_at
        conversation.increment_message_count()

    def _update_participants_unread_count(self, conversation, message, sender):
        """更新参与者未读数"""
        ConversationParticipant.objects.filter(
            conversation=conversation,
            status='active'
        ).exclude(user=sender).update(
            unread_count=F('unread_count') + 1
        )

    def _create_delivery_statuses(self, message, conversation):
        """创建送达状态记录"""
        participants = ConversationParticipant.objects.filter(
            conversation=conversation,
            status='active'
        ).exclude(user=message.sender)

        delivery_statuses = []
        for participant in participants:
            delivery_statuses.append(MessageDeliveryStatus(
                message=message,
                recipient=participant.user,
                status='pending'
            ))

        if delivery_statuses:
            MessageDeliveryStatus.objects.bulk_create(delivery_statuses)

    def _can_access_conversation(self, user_id: str, conversation) -> bool:
        """检查是否可以访问会话"""
        return ConversationParticipant.objects.filter(
            conversation=conversation,
            user__uuid=user_id,
            status='active'
        ).exists()

    def _serialize_message(self, message, user_id: str) -> Dict[str, Any]:
        """序列化消息"""
        data = {
            'id': str(message.uuid),
            'conversation_id': str(message.conversation.uuid),
            'sender': {
                'id': str(message.sender.uuid),
                'username': message.sender.username,
                'display_name': message.sender.display_name
            },
            'message_type': message.message_type,
            'content': message.content,
            'status': message.status,
            'sent_at': message.sent_at.isoformat(),
            'is_edited': message.is_edited,
            'is_pinned': message.is_pinned,
            'is_important': message.is_important,
            'read_count': message.read_count,
            'reaction_count': message.reaction_count
        }

        # 添加媒体信息
        if message.media_url:
            data['media'] = {
                'url': message.media_url,
                'thumbnail_url': message.media_thumbnail_url,
                'size': message.media_size,
                'duration': message.media_duration
            }

        # 添加回复信息
        if message.reply_to:
            data['reply_to'] = {
                'id': str(message.reply_to.uuid),
                'content': message.reply_to.content[:100] + '...' if len(message.reply_to.content) > 100 else message.reply_to.content,
                'sender': message.reply_to.sender.username
            }

        # 添加反应信息
        reactions = message.reactions.values('reaction_type').annotate(count=Count('id'))
        data['reactions'] = {item['reaction_type']: item['count'] for item in reactions}

        return data

    def _serialize_conversation(self, conversation, user_id: str) -> Dict[str, Any]:
        """序列化会话"""
        data = {
            'id': str(conversation.uuid),
            'name': conversation.display_name,
            'conversation_type': conversation.conversation_type,
            'is_active': conversation.is_active,
            'is_archived': conversation.is_archived,
            'participant_count': conversation.participants.filter(
                conversation_participations__status='active'
            ).count(),
            'message_count': conversation.message_count,
            'last_message_at': conversation.last_message_at.isoformat() if conversation.last_message_at else None,
            'created_at': conversation.created_at.isoformat()
        }

        # 添加最后消息信息
        if conversation.last_message:
            data['last_message'] = {
                'id': str(conversation.last_message.uuid),
                'content': conversation.last_message.content[:100] + '...' if len(conversation.last_message.content) > 100 else conversation.last_message.content,
                'sender': conversation.last_message.sender.username,
                'message_type': conversation.last_message.message_type,
                'sent_at': conversation.last_message.sent_at.isoformat()
            }

        return data

    def _mark_messages_as_read(self, user_id: str, conversation, messages):
        """批量标记消息为已读"""
        user = self._get_user_by_id(user_id)

        # 更新送达状态
        message_ids = [msg.id for msg in messages if msg.sender != user]
        if message_ids:
            MessageDeliveryStatus.objects.filter(
                message_id__in=message_ids,
                recipient=user,
                status__in=['pending', 'delivered']
            ).update(
                status='read',
                read_at=timezone.now()
            )

        # 更新参与者已读状态
        if messages:
            latest_message = messages[0]  # 消息按时间倒序
            participant = ConversationParticipant.objects.filter(
                conversation=conversation,
                user=user,
                status='active'
            ).first()

            if participant:
                participant.mark_as_read(latest_message)

    def _validate_conversation_data(self, data: Dict[str, Any]):
        """验证会话数据"""
        required_fields = ['conversation_type']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )

        # 验证会话类型
        valid_types = ['private', 'group', 'system', 'space']
        if data['conversation_type'] not in valid_types:
            raise ValidationException(f"无效的会话类型: {data['conversation_type']}")

        # 私聊必须有参与者
        if data['conversation_type'] == 'private':
            participant_ids = data.get('participant_ids', [])
            if len(participant_ids) != 1:  # 不包括创建者
                raise ValidationException("私聊会话必须指定一个参与者")

    def _find_existing_private_conversation(self, creator_id: str, participant_ids: List[str]):
        """查找已存在的私聊会话"""
        if not participant_ids:
            return None

        # 私聊只有两个参与者
        all_participant_ids = [creator_id] + participant_ids
        if len(all_participant_ids) != 2:
            return None

        # 查找包含这两个用户的私聊会话
        conversations = Conversation.objects.filter(
            conversation_type='private',
            is_active=True
        ).annotate(
            participant_count=Count('participants')
        ).filter(participant_count=2)

        for conversation in conversations:
            conversation_participant_ids = list(
                conversation.participants.values_list('uuid', flat=True)
            )
            conversation_participant_ids = [str(uid) for uid in conversation_participant_ids]

            if set(conversation_participant_ids) == set(all_participant_ids):
                return conversation

        return None

    def _create_conversation_entity(self, creator, data: Dict[str, Any]) -> Conversation:
        """创建会话实体"""
        return Conversation.objects.create(
            name=data.get('name', ''),
            conversation_type=data['conversation_type'],
            related_space_id=data.get('related_space_id', ''),
            related_group_id=data.get('related_group_id', ''),
            is_active=True
        )

    def _add_conversation_participants(self, conversation, creator, data: Dict[str, Any]):
        """添加会话参与者"""
        # 添加创建者
        conversation.add_participant(creator, 'owner')

        # 添加其他参与者
        participant_ids = data.get('participant_ids', [])
        for participant_id in participant_ids:
            try:
                participant = self._get_user_by_id(participant_id)
                conversation.add_participant(participant, 'member')
            except ResourceNotFoundException:
                logger.warning(f"参与者不存在，跳过: {participant_id}")
                continue

    # 事件发布方法

    def _publish_message_sent_event(self, message):
        """发布消息发送事件"""
        event = DomainEvent(
            event_type='messaging.message_sent',
            aggregate_id=str(message.conversation.uuid),
            data={
                'message_id': str(message.uuid),
                'conversation_id': str(message.conversation.uuid),
                'sender_id': str(message.sender.uuid),
                'sender_username': message.sender.username,
                'message_type': message.message_type,
                'content': message.content,
                'sent_at': message.sent_at.isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_conversation_created_event(self, conversation):
        """发布会话创建事件"""
        event = DomainEvent(
            event_type='messaging.conversation_created',
            aggregate_id=str(conversation.uuid),
            data={
                'conversation_id': str(conversation.uuid),
                'conversation_type': conversation.conversation_type,
                'participant_count': conversation.participants.count()
            }
        )
        event_bus.publish(event)

    def _publish_user_status_changed_event(self, user, old_status: str, new_status: str):
        """发布用户状态变更事件"""
        event = DomainEvent(
            event_type='messaging.user_status_changed',
            aggregate_id=str(user.uuid),
            data={
                'user_id': str(user.uuid),
                'username': user.username,
                'old_status': old_status,
                'new_status': new_status,
                'changed_at': timezone.now().isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_message_read_event(self, message, user):
        """发布消息已读事件"""
        event = DomainEvent(
            event_type='messaging.message_read',
            aggregate_id=str(message.conversation.uuid),
            data={
                'message_id': str(message.uuid),
                'conversation_id': str(message.conversation.uuid),
                'reader_id': str(user.uuid),
                'reader_username': user.username,
                'read_at': timezone.now().isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_message_reaction_event(self, message, user, reaction_type: str, action: str):
        """发布消息反应事件"""
        event = DomainEvent(
            event_type='messaging.message_reaction',
            aggregate_id=str(message.conversation.uuid),
            data={
                'message_id': str(message.uuid),
                'conversation_id': str(message.conversation.uuid),
                'user_id': str(user.uuid),
                'username': user.username,
                'reaction_type': reaction_type,
                'action': action,  # 'added' or 'removed'
                'timestamp': timezone.now().isoformat()
            }
        )
        event_bus.publish(event)
