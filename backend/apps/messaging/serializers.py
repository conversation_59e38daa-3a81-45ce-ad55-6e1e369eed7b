"""
消息数据序列化器 - 数据转换和验证
职责：处理消息相关API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import (
    Conversation, ConversationParticipant, Message, MessageReaction,
    UserOnlineStatus, MessageDeliveryStatus
)


class ConversationCreateSerializer(serializers.Serializer):
    """会话创建序列化器"""
    
    name = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="会话名称（群聊时必填）"
    )
    conversation_type = serializers.ChoiceField(
        choices=Conversation.CONVERSATION_TYPE_CHOICES,
        help_text="会话类型"
    )
    participant_ids = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="参与者用户ID列表"
    )
    related_space_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="关联空间ID"
    )
    related_group_id = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="关联群组ID"
    )
    
    def validate(self, attrs):
        """交叉验证"""
        conversation_type = attrs['conversation_type']
        
        # 群聊必须有名称
        if conversation_type == 'group' and not attrs.get('name'):
            raise serializers.ValidationError({
                'name': '群聊会话必须设置名称'
            })
        
        # 私聊必须有且仅有一个参与者
        if conversation_type == 'private':
            participant_ids = attrs.get('participant_ids', [])
            if len(participant_ids) != 1:
                raise serializers.ValidationError({
                    'participant_ids': '私聊会话必须指定一个参与者'
                })
        
        return attrs
    
    def validate_participant_ids(self, value):
        """验证参与者ID列表"""
        if not value:
            return value
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # 验证用户是否存在
        for user_id in value:
            try:
                User.objects.get(uuid=user_id)
            except User.DoesNotExist:
                raise serializers.ValidationError(f"用户不存在: {user_id}")
        
        return value


class MessageCreateSerializer(serializers.Serializer):
    """消息创建序列化器"""
    
    conversation_id = serializers.CharField(
        help_text="会话ID"
    )
    message_type = serializers.ChoiceField(
        choices=Message.MESSAGE_TYPE_CHOICES,
        default='text',
        help_text="消息类型"
    )
    content = serializers.CharField(
        max_length=10000,
        help_text="消息内容"
    )
    media_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="媒体文件URL"
    )
    media_thumbnail_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="媒体缩略图URL"
    )
    media_size = serializers.IntegerField(
        required=False,
        min_value=0,
        help_text="媒体文件大小（字节）"
    )
    media_duration = serializers.IntegerField(
        required=False,
        min_value=0,
        help_text="媒体时长（秒）"
    )
    reply_to_id = serializers.CharField(
        required=False,
        help_text="回复的消息ID"
    )
    is_important = serializers.BooleanField(
        default=False,
        help_text="是否重要消息"
    )
    
    def validate_conversation_id(self, value):
        """验证会话ID"""
        try:
            Conversation.objects.get(uuid=value)
        except Conversation.DoesNotExist:
            raise serializers.ValidationError("会话不存在")
        
        return value
    
    def validate_reply_to_id(self, value):
        """验证回复消息ID"""
        if value:
            try:
                Message.objects.get(uuid=value)
            except Message.DoesNotExist:
                raise serializers.ValidationError("回复的消息不存在")
        
        return value
    
    def validate(self, attrs):
        """交叉验证"""
        message_type = attrs['message_type']
        content = attrs['content']
        media_url = attrs.get('media_url')
        
        # 媒体消息必须有媒体URL
        if message_type in ['image', 'video', 'audio', 'file'] and not media_url:
            raise serializers.ValidationError({
                'media_url': f'{message_type}消息必须提供媒体文件URL'
            })
        
        # 文本消息内容不能为空
        if message_type == 'text' and not content.strip():
            raise serializers.ValidationError({
                'content': '文本消息内容不能为空'
            })
        
        return attrs


class MessageSerializer(serializers.ModelSerializer):
    """消息序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    sender = serializers.SerializerMethodField()
    conversation_id = serializers.CharField(source='conversation.uuid', read_only=True)
    reply_to = serializers.SerializerMethodField()
    reactions = serializers.SerializerMethodField()
    media = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation_id', 'sender', 'message_type', 'content',
            'status', 'sent_at', 'delivered_at', 'read_at',
            'is_edited', 'is_pinned', 'is_important',
            'read_count', 'reaction_count',
            'reply_to', 'reactions', 'media'
        ]
        read_only_fields = fields
    
    def get_sender(self, obj):
        """获取发送者信息"""
        return {
            'id': str(obj.sender.uuid),
            'username': obj.sender.username,
            'display_name': obj.sender.display_name,
            'avatar_url': getattr(obj.sender.profile, 'avatar_url', '') if hasattr(obj.sender, 'profile') else ''
        }
    
    def get_reply_to(self, obj):
        """获取回复消息信息"""
        if obj.reply_to:
            return {
                'id': str(obj.reply_to.uuid),
                'content': obj.reply_to.content[:100] + '...' if len(obj.reply_to.content) > 100 else obj.reply_to.content,
                'sender': obj.reply_to.sender.username,
                'message_type': obj.reply_to.message_type
            }
        return None
    
    def get_reactions(self, obj):
        """获取消息反应统计"""
        reactions = obj.reactions.values('reaction_type').annotate(
            count=models.Count('id')
        )
        return {item['reaction_type']: item['count'] for item in reactions}
    
    def get_media(self, obj):
        """获取媒体信息"""
        if obj.media_url:
            return {
                'url': obj.media_url,
                'thumbnail_url': obj.media_thumbnail_url,
                'size': obj.media_size,
                'duration': obj.media_duration
            }
        return None


class ConversationSerializer(serializers.ModelSerializer):
    """会话序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    display_name = serializers.ReadOnlyField()
    participant_count = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    user_participation = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'name', 'display_name', 'conversation_type',
            'is_active', 'is_archived', 'is_muted',
            'participant_count', 'message_count',
            'last_message', 'last_message_at',
            'user_participation', 'created_at', 'updated_at'
        ]
        read_only_fields = fields
    
    def get_participant_count(self, obj):
        """获取参与者数量"""
        return obj.participants.filter(
            conversation_participations__status='active'
        ).count()
    
    def get_last_message(self, obj):
        """获取最后一条消息"""
        if obj.last_message:
            return {
                'id': str(obj.last_message.uuid),
                'content': obj.last_message.content[:100] + '...' if len(obj.last_message.content) > 100 else obj.last_message.content,
                'sender': obj.last_message.sender.username,
                'message_type': obj.last_message.message_type,
                'sent_at': obj.last_message.sent_at.isoformat()
            }
        return None
    
    def get_user_participation(self, obj):
        """获取当前用户的参与信息"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                participation = ConversationParticipant.objects.get(
                    conversation=obj,
                    user=request.user,
                    status='active'
                )
                return ConversationParticipantSerializer(participation).data
            except ConversationParticipant.DoesNotExist:
                pass
        return None


class ConversationParticipantSerializer(serializers.ModelSerializer):
    """会话参与者序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user = serializers.SerializerMethodField()
    conversation_name = serializers.CharField(source='conversation.display_name', read_only=True)
    
    class Meta:
        model = ConversationParticipant
        fields = [
            'id', 'user', 'conversation_name', 'role', 'status',
            'is_muted', 'is_pinned', 'unread_count',
            'last_read_at', 'joined_at', 'left_at'
        ]
        read_only_fields = fields
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj.user.uuid),
            'username': obj.user.username,
            'display_name': obj.user.display_name,
            'avatar_url': getattr(obj.user.profile, 'avatar_url', '') if hasattr(obj.user, 'profile') else ''
        }


class MessageReactionSerializer(serializers.Serializer):
    """消息反应序列化器"""
    
    reaction_type = serializers.ChoiceField(
        choices=MessageReaction.REACTION_TYPE_CHOICES,
        help_text="反应类型"
    )


class OnlineStatusSerializer(serializers.Serializer):
    """在线状态序列化器"""
    
    status = serializers.ChoiceField(
        choices=UserOnlineStatus.STATUS_CHOICES,
        help_text="在线状态"
    )
    status_message = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="状态消息"
    )
    device_info = serializers.JSONField(
        required=False,
        help_text="设备信息"
    )


class UserOnlineStatusSerializer(serializers.ModelSerializer):
    """用户在线状态序列化器"""
    
    user = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = UserOnlineStatus
        fields = [
            'user', 'status', 'status_display', 'status_message',
            'last_seen_at', 'device_type', 'device_info'
        ]
        read_only_fields = fields
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj.user.uuid),
            'username': obj.user.username,
            'display_name': obj.user.display_name,
            'avatar_url': getattr(obj.user.profile, 'avatar_url', '') if hasattr(obj.user, 'profile') else ''
        }


class MessageDeliveryStatusSerializer(serializers.ModelSerializer):
    """消息送达状态序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    message_id = serializers.CharField(source='message.uuid', read_only=True)
    recipient = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = MessageDeliveryStatus
        fields = [
            'id', 'message_id', 'recipient', 'status', 'status_display',
            'delivered_at', 'read_at', 'failure_reason', 'retry_count',
            'created_at'
        ]
        read_only_fields = fields
    
    def get_recipient(self, obj):
        """获取接收者信息"""
        return {
            'id': str(obj.recipient.uuid),
            'username': obj.recipient.username,
            'display_name': obj.recipient.display_name
        }


class MessagingStatsSerializer(serializers.Serializer):
    """消息统计序列化器"""
    
    user_id = serializers.CharField(read_only=True)
    conversation_count = serializers.IntegerField(read_only=True)
    total_unread = serializers.IntegerField(read_only=True)
    sent_messages = serializers.IntegerField(read_only=True)
    received_messages = serializers.IntegerField(read_only=True)
    conversation_types = serializers.DictField(read_only=True)


class MessageSearchSerializer(serializers.Serializer):
    """消息搜索序列化器"""
    
    q = serializers.CharField(
        min_length=1,
        max_length=100,
        help_text="搜索关键词"
    )
    conversation_id = serializers.CharField(
        required=False,
        help_text="限制搜索的会话ID"
    )
    message_type = serializers.ChoiceField(
        choices=Message.MESSAGE_TYPE_CHOICES,
        required=False,
        help_text="消息类型过滤"
    )
    sender_id = serializers.CharField(
        required=False,
        help_text="发送者ID过滤"
    )
    date_from = serializers.DateTimeField(
        required=False,
        help_text="开始时间"
    )
    date_to = serializers.DateTimeField(
        required=False,
        help_text="结束时间"
    )
    
    def validate_conversation_id(self, value):
        """验证会话ID"""
        if value:
            try:
                Conversation.objects.get(uuid=value)
            except Conversation.DoesNotExist:
                raise serializers.ValidationError("会话不存在")
        
        return value
    
    def validate_sender_id(self, value):
        """验证发送者ID"""
        if value:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            try:
                User.objects.get(uuid=value)
            except User.DoesNotExist:
                raise serializers.ValidationError("发送者不存在")
        
        return value
