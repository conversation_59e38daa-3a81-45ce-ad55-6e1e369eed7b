"""
用户应用配置 - 用户域应用配置
职责：配置用户应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
from django.db.models.signals import post_save, post_delete
import logging

logger = logging.getLogger(__name__)


class UsersConfig(AppConfig):
    """用户应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.users'
    verbose_name = '用户管理'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        logger.info("用户应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import UserService
            
            # 注册用户服务为单例
            container.register_singleton(UserService, UserService)
            
            logger.info("用户服务注册完成")
            
        except Exception as e:
            logger.error(f"用户服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入事件处理器（这会自动注册）
            # 暂时跳过Celery相关的任务导入
            # from . import tasks

            logger.info("用户事件处理器注册完成")

        except Exception as e:
            logger.error(f"用户事件处理器注册失败: {e}")