"""
用户业务服务 - 高内聚的用户业务逻辑
职责：处理用户注册、认证、资料管理、虚拟形象等核心业务逻辑
"""

import logging
from typing import Dict, Any, Optional, List
from django.db import transaction
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from django.core.exceptions import ValidationError
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken

from apps.core.events import event_bus, DomainEvent, UserRegisteredEvent
from apps.core.exceptions import (
    UserNotFoundException, 
    UserAlreadyExistsException,
    EmailAlreadyExistsException,
    UsernameAlreadyExistsException,
    InvalidCredentialsException,
    ValidationException
)
from apps.core.dependencies import inject, IUserService
from .models import User, UserProfile, Avatar, UserSession, UserPreference, UserActivity
# from .tasks import send_verification_email, create_default_avatar  # 暂时禁用Celery任务

logger = logging.getLogger(__name__)


class UserService(IUserService):
    """
    用户业务服务 - 高内聚的用户相关业务逻辑
    
    职责：
    1. 用户注册和认证
    2. 用户资料管理
    3. 虚拟形象管理
    4. 用户会话管理
    5. 用户偏好设置
    """
    
    @transaction.atomic
    def register_user(self, registration_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        用户注册主流程
        
        Args:
            registration_data: 注册数据
            
        Returns:
            Dict: 注册结果
            
        Raises:
            ValidationException: 数据验证失败
            EmailAlreadyExistsException: 邮箱已存在
            UsernameAlreadyExistsException: 用户名已存在
        """
        logger.info(f"开始用户注册流程: {registration_data.get('username')}")
        
        # 1. 数据验证
        self._validate_registration_data(registration_data)
        
        # 2. 检查业务规则
        self._check_registration_business_rules(registration_data)
        
        # 3. 创建用户实体
        user = self._create_user_entity(registration_data)
        
        # 4. 创建用户资料
        profile = self._create_user_profile(user, registration_data)
        
        # 5. 创建用户偏好设置
        preferences = self._create_user_preferences(user)
        
        # 6. 发布用户注册事件
        self._publish_user_registered_event(user, profile)
        
        # 7. 异步任务：发送验证邮件和创建默认形象（暂时禁用）
        # send_verification_email.delay(user.id)
        # create_default_avatar.delay(user.id)
        
        logger.info(f"用户注册成功: {user.username} (ID: {user.id})")
        
        return {
            'user_id': str(user.id),
            'username': user.username,
            'email': user.email,
            'status': 'pending_verification',
            'message': '注册成功，请查收验证邮件',
            'user': user  # 添加用户对象供create_user方法使用
        }
    
    def authenticate_user(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            credentials: 认证凭据
            
        Returns:
            Dict: 认证结果
            
        Raises:
            InvalidCredentialsException: 认证失败
        """
        identifier = credentials.get('identifier')  # 用户名或邮箱
        password = credentials.get('password')
        device_info = credentials.get('device_info', {})
        
        logger.info(f"用户认证请求: {identifier}")
        
        # 查找用户
        user = self._find_user_by_identifier(identifier)
        if not user:
            raise InvalidCredentialsException("用户名或密码错误")
        
        # 验证密码
        if not user.check_password(password):
            # 记录失败尝试
            self._record_login_attempt(user, False, device_info)
            raise InvalidCredentialsException("用户名或密码错误")
        
        # 检查用户状态
        if not user.is_active:
            raise InvalidCredentialsException("账户已被禁用")
        
        if user.status == 'suspended':
            raise InvalidCredentialsException("账户已被暂停")
        
        if user.status == 'banned':
            raise InvalidCredentialsException("账户已被封禁")
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)
        
        # 创建用户会话
        session = self._create_user_session(user, device_info)
        
        # 更新用户登录信息
        self._update_user_login_info(user, device_info)
        
        # 记录成功登录
        self._record_login_attempt(user, True, device_info)
        
        # 发布用户登录事件
        self._publish_user_login_event(user, session)
        
        logger.info(f"用户认证成功: {user.username} (ID: {user.id})")
        
        return {
            'user': self._serialize_user_data(user),
            'profile': self._serialize_profile_data(user.profile),
            'avatar': self._serialize_avatar_data(user.avatar) if hasattr(user, 'avatar') else None,
            'tokens': {
                'access_token': access_token,
                'refresh_token': refresh_token
            },
            'session_id': str(session.id),
            'expires_at': refresh.payload['exp']
        }
    
    def get_user_by_id(self, user_id: str) -> Dict[str, Any]:
        """
        根据ID获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户信息
            
        Raises:
            UserNotFoundException: 用户不存在
        """
        try:
            user = User.objects.select_related('profile').get(uuid=user_id)
            
            return {
                'user': self._serialize_user_data(user),
                'profile': self._serialize_profile_data(user.profile) if hasattr(user, 'profile') else None,
                'avatar': self._serialize_avatar_data(user.avatar) if hasattr(user, 'avatar') else None,
                'stats': self._get_user_stats(user)
            }
            
        except User.DoesNotExist:
            raise UserNotFoundException(user_id)
    
    @transaction.atomic
    def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户资料
        
        Args:
            user_id: 用户ID
            profile_data: 资料数据
            
        Returns:
            Dict: 更新后的资料
            
        Raises:
            UserNotFoundException: 用户不存在
            ValidationException: 数据验证失败
        """
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise UserNotFoundException(user_id)
        
        # 验证数据
        self._validate_profile_data(profile_data)
        
        # 获取或创建用户资料
        profile, created = UserProfile.objects.get_or_create(user=user)
        
        # 更新字段
        for field, value in profile_data.items():
            if hasattr(profile, field):
                setattr(profile, field, value)
        
        profile.save()
        
        # 发布资料更新事件
        self._publish_profile_updated_event(user, profile, profile_data)
        
        logger.info(f"用户资料更新成功: {user.username}")
        
        return self._serialize_profile_data(profile)
    
    @transaction.atomic
    def update_user_avatar(self, user_id: str, avatar_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户虚拟形象
        
        Args:
            user_id: 用户ID
            avatar_data: 形象数据
            
        Returns:
            Dict: 更新后的形象
            
        Raises:
            UserNotFoundException: 用户不存在
            ValidationException: 数据验证失败
        """
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise UserNotFoundException(user_id)
        
        # 验证数据
        self._validate_avatar_data(avatar_data)
        
        # 获取或创建虚拟形象
        avatar, created = Avatar.objects.get_or_create(user=user)
        
        # 更新字段
        for field, value in avatar_data.items():
            if hasattr(avatar, field):
                setattr(avatar, field, value)
        
        avatar.save()
        
        # 发布形象更新事件
        self._publish_avatar_updated_event(user, avatar, avatar_data)
        
        logger.info(f"用户形象更新成功: {user.username}")
        
        return self._serialize_avatar_data(avatar)
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List: 会话列表
        """
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise UserNotFoundException(user_id)
        
        sessions = UserSession.objects.filter(
            user=user,
            is_active=True,
            expires_at__gt=timezone.now()
        ).order_by('-last_activity')
        
        return [self._serialize_session_data(session) for session in sessions]
    
    @transaction.atomic
    def revoke_user_session(self, user_id: str, session_id: str) -> bool:
        """
        撤销用户会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            bool: 是否成功
        """
        try:
            session = UserSession.objects.get(
                user__uuid=user_id,
                uuid=session_id,
                is_active=True
            )
            
            session.is_active = False
            session.save()
            
            logger.info(f"用户会话已撤销: {session.user.username} - {session_id}")
            return True
            
        except UserSession.DoesNotExist:
            return False
    
    def search_users(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索用户
        
        Args:
            query: 搜索关键词
            limit: 结果限制
            
        Returns:
            List: 用户列表
        """
        users = User.objects.filter(
            models.Q(username__icontains=query) |
            models.Q(profile__nickname__icontains=query),
            is_active=True,
            status='active'
        ).select_related('profile')[:limit]
        
        return [
            {
                'user': self._serialize_user_data(user, include_sensitive=False),
                'profile': self._serialize_profile_data(user.profile, include_private=False) if hasattr(user, 'profile') else None
            }
            for user in users
        ]
    
    # 私有方法
    
    def _validate_registration_data(self, data: Dict[str, Any]):
        """验证注册数据"""
        required_fields = ['username', 'email', 'password', 'agree_to_terms']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )
        
        # 验证用户名格式
        username = data['username']
        if len(username) < 3 or len(username) > 30:
            raise ValidationException("用户名长度必须在3-30个字符之间")
        
        # 验证邮箱格式
        email = data['email']
        if '@' not in email or '.' not in email:
            raise ValidationException("邮箱格式不正确")
        
        # 验证密码强度
        password = data['password']
        if len(password) < 8:
            raise ValidationException("密码长度至少8个字符")
        
        # 验证服务条款同意
        if not data.get('agree_to_terms'):
            raise ValidationException("必须同意服务条款")
    
    def _check_registration_business_rules(self, data: Dict[str, Any]):
        """检查注册业务规则"""
        username = data['username']
        email = data['email']
        
        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            raise UsernameAlreadyExistsException(username)
        
        # 检查邮箱是否已存在
        if User.objects.filter(email=email).exists():
            raise EmailAlreadyExistsException(email)
    
    def _create_user_entity(self, data: Dict[str, Any]) -> User:
        """创建用户实体"""
        return User.objects.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            is_active=True,  # 允许登录，但需要邮箱验证
            is_verified=False,
            status='active',
            user_type='regular'
        )
    
    def _create_user_profile(self, user: User, data: Dict[str, Any]) -> UserProfile:
        """创建用户资料"""
        return UserProfile.objects.create(
            user=user,
            nickname=data.get('nickname', user.username),
            privacy_settings=UserProfile().get_default_privacy_settings()
        )
    
    def _create_user_preferences(self, user: User) -> UserPreference:
        """创建用户偏好设置"""
        return UserPreference.objects.create(
            user=user,
            theme='light',
            language='zh-CN',
            notification_settings={
                'email': True,
                'push': True,
                'sound': True,
                'vibration': True,
                'friend_requests': True,
                'messages': True,
                'space_invites': True,
                'system_updates': True
            },
            privacy_settings={
                'show_online_status': True,
                'allow_friend_requests': True,
                'show_profile': 'friends',
                'show_email': False,
                'show_phone': False,
                'show_location': False
            }
        )
    
    def _publish_user_registered_event(self, user: User, profile: UserProfile):
        """发布用户注册事件"""
        event = UserRegisteredEvent(
            user_id=str(user.id),
            email=user.email,
            username=user.username,
            nickname=profile.nickname,
            user_type=user.user_type
        )
        event_bus.publish(event)
    
    def _find_user_by_identifier(self, identifier: str) -> Optional[User]:
        """根据标识符查找用户"""
        try:
            if '@' in identifier:
                return User.objects.get(email=identifier)
            else:
                return User.objects.get(username=identifier)
        except User.DoesNotExist:
            return None
    
    def _create_user_session(self, user: User, device_info: Dict[str, Any]) -> UserSession:
        """创建用户会话"""
        return UserSession.objects.create(
            user=user,
            session_id=f"session_{timezone.now().timestamp()}_{user.id}",
            device_type=device_info.get('type', 'web'),
            device_info=device_info,
            ip_address=device_info.get('ip_address', ''),
            expires_at=timezone.now() + timezone.timedelta(days=7)
        )
    
    def _update_user_login_info(self, user: User, device_info: Dict[str, Any]):
        """更新用户登录信息"""
        user.last_login = timezone.now()
        user.last_login_ip = device_info.get('ip_address')
        user.login_count += 1
        user.save(update_fields=['last_login', 'last_login_ip', 'login_count'])
    
    def _record_login_attempt(self, user: User, success: bool, device_info: Dict[str, Any]):
        """记录登录尝试"""
        UserActivity.objects.create(
            user=user,
            activity_type='login' if success else 'login_failed',
            description=f"{'成功' if success else '失败'}登录",
            metadata={
                'success': success,
                'device_info': device_info,
                'ip_address': device_info.get('ip_address')
            },
            ip_address=device_info.get('ip_address'),
            user_agent=device_info.get('user_agent', '')
        )
    
    def _publish_user_login_event(self, user: User, session: UserSession):
        """发布用户登录事件"""
        event = DomainEvent(
            event_type='user.login',
            aggregate_id=str(user.id),
            data={
                'user_id': str(user.id),
                'username': user.username,
                'session_id': str(session.id),
                'device_type': session.device_type,
                'ip_address': session.ip_address
            }
        )
        event_bus.publish(event)
    
    def _serialize_user_data(self, user: User, include_sensitive: bool = True) -> Dict[str, Any]:
        """序列化用户数据"""
        data = {
            'id': str(user.id),
            'username': user.username,
            'is_active': user.is_active,
            'is_verified': user.is_verified,
            'status': user.status,
            'user_type': user.user_type,
            'login_count': user.login_count,
            'created_at': user.created_at.isoformat(),
            'updated_at': user.updated_at.isoformat()
        }
        
        if include_sensitive:
            data.update({
                'email': user.email,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'permissions': user.get_permissions()
            })
        
        return data
    
    def _serialize_profile_data(self, profile: UserProfile, include_private: bool = True) -> Dict[str, Any]:
        """序列化用户资料数据"""
        if not profile:
            return None
        
        data = {
            'id': str(profile.id),
            'nickname': profile.nickname,
            'bio': profile.bio,
            'avatar_url': profile.avatar_url,
            'location': profile.location if include_private else None,
            'website': profile.website,
            'social_links': profile.social_links,
            'created_at': profile.created_at.isoformat(),
            'updated_at': profile.updated_at.isoformat()
        }
        
        if include_private:
            data.update({
                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,
                'gender': profile.gender,
                'privacy_settings': profile.privacy_settings
            })
        
        return data
    
    def _serialize_avatar_data(self, avatar: Avatar) -> Dict[str, Any]:
        """序列化虚拟形象数据"""
        if not avatar:
            return None
        
        return {
            'id': str(avatar.id),
            'name': avatar.name,
            'model_url': avatar.model_url,
            'thumbnail_url': avatar.thumbnail_url,
            'config_data': avatar.config_data,
            'is_default': avatar.is_default,
            'created_at': avatar.created_at.isoformat(),
            'updated_at': avatar.updated_at.isoformat()
        }
    
    def _serialize_session_data(self, session: UserSession) -> Dict[str, Any]:
        """序列化会话数据"""
        return {
            'id': str(session.id),
            'session_id': session.session_id,
            'device_type': session.device_type,
            'device_info': session.device_info,
            'ip_address': session.ip_address,
            'location': session.location,
            'is_active': session.is_active,
            'last_activity': session.last_activity.isoformat(),
            'expires_at': session.expires_at.isoformat(),
            'created_at': session.created_at.isoformat()
        }
    
    def _get_user_stats(self, user: User) -> Dict[str, Any]:
        """获取用户统计信息"""
        return {
            'spaces_created': 0,  # TODO: 实现空间统计
            'spaces_joined': 0,   # TODO: 实现空间统计
            'friends_count': 0,   # TODO: 实现好友统计
            'messages_count': 0,  # TODO: 实现消息统计
            'content_uploaded': 0, # TODO: 实现内容统计
            'total_online_time': 0, # TODO: 实现在线时间统计
            'last_active_date': user.last_login.isoformat() if user.last_login else None,
            'join_date': user.created_at.isoformat()
        }
    
    def _validate_profile_data(self, data: Dict[str, Any]):
        """验证资料数据"""
        # 验证昵称长度
        if 'nickname' in data and len(data['nickname']) > 50:
            raise ValidationException("昵称长度不能超过50个字符")
        
        # 验证个人简介长度
        if 'bio' in data and len(data['bio']) > 500:
            raise ValidationException("个人简介长度不能超过500个字符")
    
    def _validate_avatar_data(self, data: Dict[str, Any]):
        """验证形象数据"""
        # 验证形象名称长度
        if 'name' in data and len(data['name']) > 50:
            raise ValidationException("形象名称长度不能超过50个字符")
    
    def _publish_profile_updated_event(self, user: User, profile: UserProfile, updated_data: Dict[str, Any]):
        """发布资料更新事件"""
        event = DomainEvent(
            event_type='user.profile_updated',
            aggregate_id=str(user.id),
            data={
                'user_id': str(user.id),
                'profile_id': str(profile.id),
                'updated_fields': list(updated_data.keys()),
                'updated_data': updated_data
            }
        )
        event_bus.publish(event)
    
    def _publish_avatar_updated_event(self, user: User, avatar: Avatar, updated_data: Dict[str, Any]):
        """发布形象更新事件"""
        event = DomainEvent(
            event_type='user.avatar_updated',
            aggregate_id=str(user.id),
            data={
                'user_id': str(user.id),
                'avatar_id': str(avatar.id),
                'updated_fields': list(updated_data.keys()),
                'updated_data': updated_data
            }
        )
        event_bus.publish(event)

    def create_user(self, user_data: dict) -> User:
        """
        创建用户 - 实现IUserService接口

        Args:
            user_data: 用户数据字典

        Returns:
            User: 创建的用户对象

        Raises:
            ValidationException: 数据验证失败
            UserAlreadyExistsException: 用户已存在
        """
        # 调用注册方法来创建用户
        result = self.register_user(user_data)
        return result['user']

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        根据ID获取用户 - 实现IUserService接口

        Args:
            user_id: 用户ID

        Returns:
            User: 用户对象或None
        """
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None