"""
用户信号处理器 - Django信号处理
职责：处理用户相关的Django信号，实现自动化操作
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from apps.core.events import event_bus, DomainEvent
from .models import UserProfile, Avatar, UserSession, UserActivity

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def handle_user_created(sender, instance, created, **kwargs):
    """处理用户创建信号"""
    if created:
        logger.info(f"用户创建信号触发: {instance.username}")
        
        # 发布用户创建事件
        event = DomainEvent(
            event_type='user.created',
            aggregate_id=str(instance.id),
            data={
                'user_id': str(instance.id),
                'username': instance.username,
                'email': instance.email,
                'user_type': instance.user_type,
                'status': instance.status
            }
        )
        event_bus.publish(event)


@receiver(post_save, sender=User)
def handle_user_updated(sender, instance, created, **kwargs):
    """处理用户更新信号"""
    if not created:
        logger.info(f"用户更新信号触发: {instance.username}")
        
        # 发布用户更新事件
        event = DomainEvent(
            event_type='user.updated',
            aggregate_id=str(instance.id),
            data={
                'user_id': str(instance.id),
                'username': instance.username,
                'email': instance.email,
                'status': instance.status,
                'is_active': instance.is_active,
                'is_verified': instance.is_verified
            }
        )
        event_bus.publish(event)


@receiver(post_delete, sender=User)
def handle_user_deleted(sender, instance, **kwargs):
    """处理用户删除信号"""
    logger.info(f"用户删除信号触发: {instance.username}")
    
    # 发布用户删除事件
    event = DomainEvent(
        event_type='user.deleted',
        aggregate_id=str(instance.uuid),
        data={
            'user_id': str(instance.uuid),
            'username': instance.username,
            'email': instance.email
        }
    )
    event_bus.publish(event)


@receiver(post_save, sender=UserProfile)
def handle_profile_updated(sender, instance, created, **kwargs):
    """处理用户资料更新信号"""
    action = 'created' if created else 'updated'
    logger.info(f"用户资料{action}信号触发: {instance.user.username}")
    
    # 发布资料更新事件
    event = DomainEvent(
        event_type=f'user.profile_{action}',
        aggregate_id=str(instance.user.uuid),
        data={
            'user_id': str(instance.user.uuid),
            'profile_id': str(instance.uuid),
            'nickname': instance.nickname,
            'bio': instance.bio,
            'avatar_url': instance.avatar_url
        }
    )
    event_bus.publish(event)


@receiver(post_save, sender=Avatar)
def handle_avatar_updated(sender, instance, created, **kwargs):
    """处理虚拟形象更新信号"""
    action = 'created' if created else 'updated'
    logger.info(f"虚拟形象{action}信号触发: {instance.user.username}")
    
    # 发布形象更新事件
    event = DomainEvent(
        event_type=f'user.avatar_{action}',
        aggregate_id=str(instance.user.uuid),
        data={
            'user_id': str(instance.user.uuid),
            'avatar_id': str(instance.uuid),
            'name': instance.name,
            'model_url': instance.model_url,
            'is_default': instance.is_default
        }
    )
    event_bus.publish(event)


@receiver(post_save, sender=UserSession)
def handle_session_created(sender, instance, created, **kwargs):
    """处理用户会话创建信号"""
    if created:
        logger.info(f"用户会话创建信号触发: {instance.user.username}")
        
        # 发布会话创建事件
        event = DomainEvent(
            event_type='user.session_created',
            aggregate_id=str(instance.user.uuid),
            data={
                'user_id': str(instance.user.uuid),
                'session_id': str(instance.uuid),
                'device_type': instance.device_type,
                'ip_address': instance.ip_address
            }
        )
        event_bus.publish(event)


@receiver(post_save, sender=UserActivity)
def handle_activity_logged(sender, instance, created, **kwargs):
    """处理用户活动记录信号"""
    if created:
        logger.debug(f"用户活动记录信号触发: {instance.user.username} - {instance.activity_type}")
        
        # 对于重要活动，发布事件
        important_activities = ['login', 'logout', 'profile_update', 'avatar_update']
        if instance.activity_type in important_activities:
            event = DomainEvent(
                event_type='user.activity_logged',
                aggregate_id=str(instance.user.uuid),
                data={
                    'user_id': str(instance.user.uuid),
                    'activity_id': str(instance.uuid),
                    'activity_type': instance.activity_type,
                    'description': instance.description,
                    'ip_address': instance.ip_address
                }
            )
            event_bus.publish(event)


@receiver(pre_save, sender=User)
def handle_user_status_change(sender, instance, **kwargs):
    """处理用户状态变更信号"""
    if instance.pk:  # 只处理更新，不处理创建
        try:
            old_instance = User.objects.get(pk=instance.pk)
            
            # 检查状态是否发生变化
            if old_instance.status != instance.status:
                logger.info(f"用户状态变更: {instance.username} {old_instance.status} -> {instance.status}")
                
                # 发布状态变更事件
                event = DomainEvent(
                    event_type='user.status_changed',
                    aggregate_id=str(instance.uuid),
                    data={
                        'user_id': str(instance.uuid),
                        'username': instance.username,
                        'old_status': old_instance.status,
                        'new_status': instance.status
                    }
                )
                event_bus.publish(event)
            
            # 检查验证状态是否发生变化
            if old_instance.is_verified != instance.is_verified and instance.is_verified:
                logger.info(f"用户邮箱验证完成: {instance.username}")
                
                # 发布邮箱验证事件
                event = DomainEvent(
                    event_type='user.email_verified',
                    aggregate_id=str(instance.uuid),
                    data={
                        'user_id': str(instance.uuid),
                        'username': instance.username,
                        'email': instance.email
                    }
                )
                event_bus.publish(event)
                
        except User.DoesNotExist:
            pass


# 自定义信号

from django.dispatch import Signal

# 定义自定义信号
user_login_success = Signal()
user_login_failed = Signal()
user_password_changed = Signal()
user_email_verified = Signal()


@receiver(user_login_success)
def handle_login_success(sender, user, request, **kwargs):
    """处理登录成功信号"""
    logger.info(f"用户登录成功: {user.username}")
    
    # 记录登录活动
    UserActivity.objects.create(
        user=user,
        activity_type='login',
        description='用户登录成功',
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', '')
    )


@receiver(user_login_failed)
def handle_login_failed(sender, username, request, **kwargs):
    """处理登录失败信号"""
    logger.warning(f"用户登录失败: {username}")
    
    # 可以在这里实现登录失败次数统计、账户锁定等逻辑


@receiver(user_password_changed)
def handle_password_changed(sender, user, **kwargs):
    """处理密码修改信号"""
    logger.info(f"用户密码修改: {user.username}")
    
    # 记录密码修改活动
    UserActivity.objects.create(
        user=user,
        activity_type='password_change',
        description='用户修改密码'
    )
    
    # 发布密码修改事件
    event = DomainEvent(
        event_type='user.password_changed',
        aggregate_id=str(user.uuid),
        data={
            'user_id': str(user.uuid),
            'username': user.username
        }
    )
    event_bus.publish(event)


@receiver(user_email_verified)
def handle_email_verified_signal(sender, user, **kwargs):
    """处理邮箱验证信号"""
    logger.info(f"用户邮箱验证: {user.username}")
    
    # 记录邮箱验证活动
    UserActivity.objects.create(
        user=user,
        activity_type='email_verification',
        description='用户完成邮箱验证'
    )


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip