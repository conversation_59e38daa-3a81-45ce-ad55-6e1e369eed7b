# Generated by Django 5.2 on 2025-07-30 16:06

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱')),
                ('phone', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message='请输入有效的手机号码', regex='^\\+?1?\\d{9,15}$')], verbose_name='手机号')),
                ('is_verified', models.BooleanField(default=False, verbose_name='是否认证')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='最后登录IP')),
                ('login_count', models.PositiveIntegerField(default=0, verbose_name='登录次数')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('suspended', '暂停'), ('banned', '封禁')], default='active', max_length=20, verbose_name='状态')),
                ('user_type', models.CharField(choices=[('regular', '普通用户'), ('premium', '高级用户'), ('vip', 'VIP用户'), ('admin', '管理员')], default='regular', max_length=20, verbose_name='用户类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Avatar',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(max_length=50, verbose_name='形象名称')),
                ('model_url', models.URLField(verbose_name='3D模型URL')),
                ('thumbnail_url', models.URLField(blank=True, verbose_name='缩略图URL')),
                ('config_data', models.JSONField(default=dict, verbose_name='配置数据')),
                ('is_default', models.BooleanField(default=True, verbose_name='是否默认')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='avatar', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '虚拟形象',
                'verbose_name_plural': '虚拟形象',
                'db_table': 'avatars',
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('activity_type', models.CharField(choices=[('login', '登录'), ('logout', '登出'), ('space_join', '加入空间'), ('space_leave', '离开空间'), ('friend_add', '添加好友'), ('content_upload', '上传内容'), ('message_send', '发送消息'), ('profile_update', '更新资料'), ('avatar_update', '更新形象')], max_length=20, verbose_name='活动类型')),
                ('description', models.CharField(max_length=200, verbose_name='描述')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户活动',
                'verbose_name_plural': '用户活动',
                'db_table': 'user_activities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('theme', models.CharField(default='light', max_length=20, verbose_name='主题')),
                ('language', models.CharField(default='zh-CN', max_length=10, verbose_name='语言')),
                ('notification_settings', models.JSONField(default=dict, verbose_name='通知设置')),
                ('privacy_settings', models.JSONField(default=dict, verbose_name='隐私设置')),
                ('audio_settings', models.JSONField(default=dict, verbose_name='音频设置')),
                ('video_settings', models.JSONField(default=dict, verbose_name='视频设置')),
                ('control_settings', models.JSONField(default=dict, verbose_name='控制设置')),
                ('accessibility_settings', models.JSONField(default=dict, verbose_name='可访问性设置')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户偏好',
                'verbose_name_plural': '用户偏好',
                'db_table': 'user_preferences',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('nickname', models.CharField(max_length=50, verbose_name='昵称')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='个人简介')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='生日')),
                ('gender', models.CharField(blank=True, choices=[('male', '男'), ('female', '女'), ('other', '其他'), ('prefer_not_to_say', '不愿透露')], max_length=20, verbose_name='性别')),
                ('avatar_url', models.URLField(blank=True, verbose_name='头像URL')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='位置')),
                ('website', models.URLField(blank=True, verbose_name='个人网站')),
                ('social_links', models.JSONField(blank=True, default=dict, verbose_name='社交链接')),
                ('privacy_settings', models.JSONField(default=dict, verbose_name='隐私设置')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('session_id', models.CharField(max_length=100, unique=True, verbose_name='会话ID')),
                ('device_type', models.CharField(max_length=20, verbose_name='设备类型')),
                ('device_info', models.JSONField(default=dict, verbose_name='设备信息')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='位置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='最后活动时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户会话',
                'verbose_name_plural': '用户会话',
                'db_table': 'user_sessions',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='users_email_4b85f2_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['phone'], name='users_phone_af6883_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['status'], name='users_status_9ca66f_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['created_at'], name='users_created_6541e9_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['user', 'activity_type'], name='user_activi_user_id_290628_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['created_at'], name='user_activi_created_9fa3ca_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', 'is_active'], name='user_sessio_user_id_bb1b83_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_id'], name='user_sessio_session_e62ba3_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['expires_at'], name='user_sessio_expires_66ae96_idx'),
        ),
    ]
