"""
用户权限控制 - 访问控制和权限验证
职责：定义用户相关的权限类，控制API访问权限
"""

from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView
from django.contrib.auth.models import AnonymousUser


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    只有所有者可以编辑，其他人只能读取
    """
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 读取权限对所有人开放
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只对所有者开放
        return obj.user == request.user


class IsProfileOwner(permissions.BasePermission):
    """
    只有资料所有者可以访问和编辑
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        # 必须是认证用户
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 只有资料所有者可以访问
        return obj.user == request.user


class IsActiveUser(permissions.BasePermission):
    """
    只有活跃用户可以访问
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_active and
            request.user.status == 'active'
        )


class IsVerifiedUser(permissions.BasePermission):
    """
    只有已验证用户可以访问
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_verified
        )


class IsPremiumUser(permissions.BasePermission):
    """
    只有高级用户可以访问
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return (
            request.user and
            request.user.is_authenticated and
            request.user.user_type in ['premium', 'vip']
        )


class IsVIPUser(permissions.BasePermission):
    """
    只有VIP用户可以访问
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return (
            request.user and
            request.user.is_authenticated and
            request.user.user_type == 'vip'
        )


class IsAdminUser(permissions.BasePermission):
    """
    只有管理员可以访问
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return (
            request.user and
            request.user.is_authenticated and
            (request.user.is_staff or request.user.user_type == 'admin')
        )


class CanManageUsers(permissions.BasePermission):
    """
    可以管理用户的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 检查用户权限
        user_permissions = request.user.get_permissions()
        return 'admin:basic' in user_permissions or 'admin:super' in user_permissions


class CanViewUserDetails(permissions.BasePermission):
    """
    可以查看用户详情的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        # 匿名用户可以查看公开信息
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 其他操作需要认证
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 读取权限
        if request.method in permissions.SAFE_METHODS:
            # 检查隐私设置
            if hasattr(obj, 'profile') and obj.profile:
                privacy_settings = obj.profile.privacy_settings
                show_profile = privacy_settings.get('show_profile', 'public')
                
                if show_profile == 'public':
                    return True
                elif show_profile == 'friends':
                    # TODO: 检查是否为好友关系
                    return request.user.is_authenticated
                else:  # private
                    return obj == request.user
            
            return True
        
        # 写入权限只对所有者开放
        return obj == request.user


class RateLimitPermission(permissions.BasePermission):
    """
    基于用户类型的限流权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not (request.user and request.user.is_authenticated):
            return True  # 匿名用户由其他中间件处理限流
        
        # 根据用户类型设置不同的限流策略
        user_type = request.user.user_type
        
        # VIP用户不限流
        if user_type == 'vip':
            return True
        
        # 高级用户更高的限流阈值
        if user_type == 'premium':
            return self._check_rate_limit(request, limit=200)
        
        # 普通用户标准限流
        return self._check_rate_limit(request, limit=100)
    
    def _check_rate_limit(self, request: Request, limit: int) -> bool:
        """检查限流"""
        try:
            from django.core.cache import cache
            
            # 生成限流键
            rate_limit_key = f"rate_limit:user:{request.user.id}"
            
            # 获取当前计数
            current = cache.get(rate_limit_key, 0)
            
            if current >= limit:
                return False
            
            # 增加计数
            cache.set(rate_limit_key, current + 1, 60)  # 1分钟窗口
            return True
            
        except Exception:
            # 缓存异常时允许访问
            return True


class UserActionPermission(permissions.BasePermission):
    """
    用户操作权限（基于操作类型）
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 获取操作类型
        action = getattr(view, 'action', None)
        
        # 根据操作类型检查权限
        if action in ['create', 'update', 'partial_update', 'destroy']:
            return self._can_modify_user_data(request.user)
        elif action in ['list', 'retrieve']:
            return self._can_view_user_data(request.user)
        
        return True
    
    def _can_modify_user_data(self, user) -> bool:
        """检查是否可以修改用户数据"""
        # 检查用户状态
        if user.status in ['suspended', 'banned']:
            return False
        
        # 检查用户权限
        permissions = user.get_permissions()
        return 'user:basic' in permissions
    
    def _can_view_user_data(self, user) -> bool:
        """检查是否可以查看用户数据"""
        return user.is_active


class SessionPermission(permissions.BasePermission):
    """
    会话权限控制
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 只能操作自己的会话
        return obj.user == request.user


class ProfileVisibilityPermission(permissions.BasePermission):
    """
    资料可见性权限
    """
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        # 所有者总是可以访问
        if obj.user == request.user:
            return True
        
        # 检查隐私设置
        privacy_settings = obj.privacy_settings
        show_profile = privacy_settings.get('show_profile', 'public')
        
        if show_profile == 'public':
            return True
        elif show_profile == 'friends':
            # TODO: 检查好友关系
            return request.user.is_authenticated
        else:  # private
            return False


# 权限组合类

class StandardUserPermissions:
    """标准用户权限组合"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsActiveUser,
            RateLimitPermission
        ]


class VerifiedUserPermissions:
    """已验证用户权限组合"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsActiveUser,
            IsVerifiedUser,
            RateLimitPermission
        ]


class PremiumUserPermissions:
    """高级用户权限组合"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsActiveUser,
            IsVerifiedUser,
            IsPremiumUser
        ]


class AdminPermissions:
    """管理员权限组合"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsAdminUser
        ]


# 权限检查函数

def check_user_permission(user, permission: str) -> bool:
    """
    检查用户是否有指定权限
    
    Args:
        user: 用户对象
        permission: 权限字符串
        
    Returns:
        bool: 是否有权限
    """
    if isinstance(user, AnonymousUser):
        return False
    
    if not user.is_active:
        return False
    
    user_permissions = user.get_permissions()
    return permission in user_permissions


def check_user_status(user, required_status: str = 'active') -> bool:
    """
    检查用户状态
    
    Args:
        user: 用户对象
        required_status: 要求的状态
        
    Returns:
        bool: 状态是否符合要求
    """
    if isinstance(user, AnonymousUser):
        return False
    
    return user.status == required_status


def check_user_type(user, required_types: list) -> bool:
    """
    检查用户类型
    
    Args:
        user: 用户对象
        required_types: 要求的用户类型列表
        
    Returns:
        bool: 用户类型是否符合要求
    """
    if isinstance(user, AnonymousUser):
        return False
    
    return user.user_type in required_types


def can_access_user_profile(viewer, profile_owner) -> bool:
    """
    检查是否可以访问用户资料
    
    Args:
        viewer: 查看者
        profile_owner: 资料所有者
        
    Returns:
        bool: 是否可以访问
    """
    # 所有者总是可以访问
    if viewer == profile_owner:
        return True
    
    # 检查隐私设置
    if hasattr(profile_owner, 'profile') and profile_owner.profile:
        privacy_settings = profile_owner.profile.privacy_settings
        show_profile = privacy_settings.get('show_profile', 'public')
        
        if show_profile == 'public':
            return True
        elif show_profile == 'friends':
            # TODO: 检查好友关系
            return isinstance(viewer, type(profile_owner)) and viewer.is_authenticated
        else:  # private
            return False
    
    # 默认允许访问
    return True