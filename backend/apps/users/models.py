"""
用户域数据模型 - 高内聚的用户相关模型
职责：定义用户、个人资料、虚拟形象等数据模型
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from apps.core.models import BaseModel


class User(AbstractUser):
    """用户基础模型"""
    
    email = models.EmailField(unique=True, verbose_name='邮箱')
    phone = models.CharField(
        max_length=17, 
        blank=True, 
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message='请输入有效的手机号码'
        )],
        verbose_name='手机号'
    )
    is_verified = models.BooleanField(default=False, verbose_name='是否认证')
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name='最后登录IP')
    login_count = models.PositiveIntegerField(default=0, verbose_name='登录次数')
    
    # 用户状态
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('inactive', '非活跃'),
        ('suspended', '暂停'),
        ('banned', '封禁'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    
    # 用户类型
    USER_TYPE_CHOICES = [
        ('regular', '普通用户'),
        ('premium', '高级用户'),
        ('vip', 'VIP用户'),
        ('admin', '管理员'),
    ]
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='regular', verbose_name='用户类型')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.username} ({self.email})"
    
    @property
    def display_name(self):
        """获取用户显示名称"""
        profile = getattr(self, 'profile', None)
        if profile and profile.nickname:
            return profile.nickname
        return self.username
    
    def get_permissions(self):
        """获取用户权限列表"""
        permissions = []
        
        # 基础权限
        if self.is_active:
            permissions.append('user:basic')
        
        # 管理员权限
        if self.is_staff:
            permissions.append('admin:basic')
        
        if self.is_superuser:
            permissions.append('admin:super')
        
        # 根据用户类型添加权限
        if self.user_type == 'premium':
            permissions.extend(['space:premium', 'content:premium'])
        elif self.user_type == 'vip':
            permissions.extend(['space:vip', 'content:vip', 'economy:vip'])
        
        return permissions


class UserProfile(BaseModel):
    """用户扩展资料模型"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name='用户')
    nickname = models.CharField(max_length=50, verbose_name='昵称')
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    birth_date = models.DateField(null=True, blank=True, verbose_name='生日')
    
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
        ('other', '其他'),
        ('prefer_not_to_say', '不愿透露'),
    ]
    gender = models.CharField(max_length=20, choices=GENDER_CHOICES, blank=True, verbose_name='性别')
    
    avatar_url = models.URLField(blank=True, verbose_name='头像URL')
    location = models.CharField(max_length=100, blank=True, verbose_name='位置')
    website = models.URLField(blank=True, verbose_name='个人网站')
    
    # 社交链接
    social_links = models.JSONField(default=dict, blank=True, verbose_name='社交链接')
    
    # 隐私设置
    privacy_settings = models.JSONField(default=dict, verbose_name='隐私设置')
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
    
    def __str__(self):
        return f"{self.user.username}的资料"
    
    def get_default_privacy_settings(self):
        """获取默认隐私设置"""
        return {
            'show_online_status': True,
            'allow_friend_requests': True,
            'show_profile': 'friends',  # public, friends, private
            'show_email': False,
            'show_phone': False,
            'show_location': False,
        }


class Avatar(BaseModel):
    """虚拟形象模型"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='avatar', verbose_name='用户')
    name = models.CharField(max_length=50, verbose_name='形象名称')
    model_url = models.URLField(verbose_name='3D模型URL')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图URL')
    
    # 形象配置数据
    config_data = models.JSONField(default=dict, verbose_name='配置数据')
    
    is_default = models.BooleanField(default=True, verbose_name='是否默认')
    
    class Meta:
        db_table = 'avatars'
        verbose_name = '虚拟形象'
        verbose_name_plural = '虚拟形象'
    
    def __str__(self):
        return f"{self.user.username}的{self.name}"
    
    def get_default_config(self):
        """获取默认形象配置"""
        return {
            'gender': 'male',
            'skin_color': '#FDBCB4',
            'hair_color': '#8B4513',
            'eye_color': '#4B0082',
            'height': 1.75,
            'body_type': 'normal',
            'clothing': {
                'top': 'default_shirt',
                'bottom': 'default_pants',
                'shoes': 'default_shoes',
                'accessories': []
            },
            'animations': {
                'idle': 'default_idle',
                'walk': 'default_walk',
                'run': 'default_run',
                'jump': 'default_jump',
                'wave': 'default_wave'
            }
        }


class UserSession(BaseModel):
    """用户会话模型"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions', verbose_name='用户')
    session_id = models.CharField(max_length=100, unique=True, verbose_name='会话ID')
    device_type = models.CharField(max_length=20, verbose_name='设备类型')
    device_info = models.JSONField(default=dict, verbose_name='设备信息')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    location = models.CharField(max_length=100, blank=True, verbose_name='位置')
    is_active = models.BooleanField(default=True, verbose_name='是否活跃')
    last_activity = models.DateTimeField(auto_now=True, verbose_name='最后活动时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    
    class Meta:
        db_table = 'user_sessions'
        verbose_name = '用户会话'
        verbose_name_plural = '用户会话'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_id']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username}的会话 ({self.device_type})"


class UserPreference(BaseModel):
    """用户偏好设置模型"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences', verbose_name='用户')
    
    # 主题和语言
    theme = models.CharField(max_length=20, default='light', verbose_name='主题')
    language = models.CharField(max_length=10, default='zh-CN', verbose_name='语言')
    
    # 通知设置
    notification_settings = models.JSONField(default=dict, verbose_name='通知设置')
    
    # 隐私设置
    privacy_settings = models.JSONField(default=dict, verbose_name='隐私设置')
    
    # 音视频设置
    audio_settings = models.JSONField(default=dict, verbose_name='音频设置')
    video_settings = models.JSONField(default=dict, verbose_name='视频设置')
    
    # 控制设置
    control_settings = models.JSONField(default=dict, verbose_name='控制设置')
    
    # 可访问性设置
    accessibility_settings = models.JSONField(default=dict, verbose_name='可访问性设置')
    
    class Meta:
        db_table = 'user_preferences'
        verbose_name = '用户偏好'
        verbose_name_plural = '用户偏好'
    
    def __str__(self):
        return f"{self.user.username}的偏好设置"


class UserActivity(BaseModel):
    """用户活动记录模型"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities', verbose_name='用户')
    
    ACTIVITY_TYPE_CHOICES = [
        ('login', '登录'),
        ('logout', '登出'),
        ('space_join', '加入空间'),
        ('space_leave', '离开空间'),
        ('friend_add', '添加好友'),
        ('content_upload', '上传内容'),
        ('message_send', '发送消息'),
        ('profile_update', '更新资料'),
        ('avatar_update', '更新形象'),
    ]
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPE_CHOICES, verbose_name='活动类型')
    
    description = models.CharField(max_length=200, verbose_name='描述')
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')
    
    class Meta:
        db_table = 'user_activities'
        verbose_name = '用户活动'
        verbose_name_plural = '用户活动'
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()}"