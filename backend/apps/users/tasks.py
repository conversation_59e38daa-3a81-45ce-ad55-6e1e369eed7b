"""
用户异步任务 - 后台处理任务
职责：处理用户相关的异步任务，如邮件发送、形象创建等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model

from apps.core.events import event_bus, DomainEvent

User = get_user_model()
logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_verification_email(self, user_id: int):
    """
    发送邮箱验证邮件
    
    Args:
        user_id: 用户ID
    """
    try:
        user = User.objects.get(id=user_id)
        
        # 生成验证码
        verification_code = generate_verification_code()
        
        # 存储验证码到缓存
        from django.core.cache import cache
        cache_key = f"email_verification:{user.id}"
        cache.set(cache_key, verification_code, timeout=3600)  # 1小时有效
        
        # 渲染邮件模板
        subject = '欢迎加入SOIC - 请验证您的邮箱'
        html_message = render_to_string('emails/verification.html', {
            'user': user,
            'verification_code': verification_code,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',  # 纯文本消息为空，使用HTML
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"验证邮件发送成功: {user.email}")
        
        # 发布邮件发送事件
        event = DomainEvent(
            event_type='user.verification_email_sent',
            aggregate_id=str(user.uuid),
            data={
                'user_id': str(user.uuid),
                'email': user.email,
                'verification_code': verification_code
            }
        )
        event_bus.publish(event)
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
        raise
    
    except Exception as exc:
        logger.error(f"发送验证邮件失败: {user_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            # 指数退避重试
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def create_default_avatar(self, user_id: int):
    """
    创建默认虚拟形象
    
    Args:
        user_id: 用户ID
    """
    try:
        user = User.objects.get(id=user_id)
        
        # 检查是否已有形象
        from .models import Avatar
        if Avatar.objects.filter(user=user).exists():
            logger.info(f"用户已有虚拟形象: {user.username}")
            return
        
        # 创建默认形象
        avatar = Avatar.objects.create(
            user=user,
            name=f"{user.username}的形象",
            model_url=get_default_avatar_model_url(),
            thumbnail_url=get_default_avatar_thumbnail_url(),
            config_data=get_default_avatar_config(),
            is_default=True
        )
        
        logger.info(f"默认虚拟形象创建成功: {user.username}")
        
        # 发布形象创建事件
        event = DomainEvent(
            event_type='user.avatar_created',
            aggregate_id=str(user.uuid),
            data={
                'user_id': str(user.uuid),
                'avatar_id': str(avatar.uuid),
                'is_default': True
            }
        )
        event_bus.publish(event)
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
        raise
    
    except Exception as exc:
        logger.error(f"创建默认形象失败: {user_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task
def create_activity_log(user_id: int, action: str, path: str, ip_address: str = None, 
                       user_agent: str = None, request_id: str = None):
    """
    创建活动日志
    
    Args:
        user_id: 用户ID
        action: 操作类型
        path: 请求路径
        ip_address: IP地址
        user_agent: 用户代理
        request_id: 请求ID
    """
    try:
        from .models import UserActivity
        
        user = User.objects.get(id=user_id)
        
        UserActivity.objects.create(
            user=user,
            activity_type=action,
            description=f"用户{action}操作: {path}",
            metadata={
                'path': path,
                'request_id': request_id
            },
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        logger.debug(f"活动日志创建成功: {user.username} - {action}")
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
    
    except Exception as exc:
        logger.error(f"创建活动日志失败: {user_id}, 错误: {exc}")


@shared_task
def send_welcome_email(user_id: int):
    """
    发送欢迎邮件
    
    Args:
        user_id: 用户ID
    """
    try:
        user = User.objects.get(id=user_id)
        
        # 渲染邮件模板
        subject = f'欢迎加入SOIC，{user.username}！'
        html_message = render_to_string('emails/welcome.html', {
            'user': user,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"欢迎邮件发送成功: {user.email}")
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
    
    except Exception as exc:
        logger.error(f"发送欢迎邮件失败: {user_id}, 错误: {exc}")


@shared_task
def send_password_reset_email(user_id: int, reset_token: str):
    """
    发送密码重置邮件
    
    Args:
        user_id: 用户ID
        reset_token: 重置令牌
    """
    try:
        user = User.objects.get(id=user_id)
        
        # 渲染邮件模板
        subject = 'SOIC - 密码重置请求'
        html_message = render_to_string('emails/password_reset.html', {
            'user': user,
            'reset_token': reset_token,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL,
            'reset_url': f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"密码重置邮件发送成功: {user.email}")
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
    
    except Exception as exc:
        logger.error(f"发送密码重置邮件失败: {user_id}, 错误: {exc}")


@shared_task
def cleanup_expired_sessions():
    """
    清理过期会话
    """
    try:
        from .models import UserSession
        
        expired_count = UserSession.objects.filter(
            expires_at__lt=timezone.now()
        ).update(is_active=False)
        
        logger.info(f"清理过期会话完成: {expired_count}个")
        
    except Exception as exc:
        logger.error(f"清理过期会话失败: {exc}")


@shared_task
def cleanup_old_activities():
    """
    清理旧活动日志（保留90天）
    """
    try:
        from .models import UserActivity
        
        cutoff_date = timezone.now() - timezone.timedelta(days=90)
        deleted_count, _ = UserActivity.objects.filter(
            created_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"清理旧活动日志完成: {deleted_count}条")
        
    except Exception as exc:
        logger.error(f"清理旧活动日志失败: {exc}")


@shared_task
def update_user_stats(user_id: int):
    """
    更新用户统计信息
    
    Args:
        user_id: 用户ID
    """
    try:
        user = User.objects.get(id=user_id)
        
        # TODO: 实现统计信息更新逻辑
        # 这里可以统计用户的各种数据
        
        logger.info(f"用户统计信息更新完成: {user.username}")
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
    
    except Exception as exc:
        logger.error(f"更新用户统计信息失败: {user_id}, 错误: {exc}")


@shared_task
def send_notification_email(user_id: int, notification_type: str, data: dict):
    """
    发送通知邮件
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        data: 通知数据
    """
    try:
        user = User.objects.get(id=user_id)
        
        # 检查用户通知偏好
        if hasattr(user, 'preferences'):
            notification_settings = user.preferences.notification_settings
            if not notification_settings.get('email', True):
                logger.info(f"用户已关闭邮件通知: {user.username}")
                return
        
        # 根据通知类型选择模板
        template_map = {
            'friend_request': 'emails/friend_request.html',
            'space_invite': 'emails/space_invite.html',
            'message_received': 'emails/message_received.html',
            'system_update': 'emails/system_update.html'
        }
        
        template = template_map.get(notification_type)
        if not template:
            logger.error(f"未知的通知类型: {notification_type}")
            return
        
        # 渲染邮件模板
        subject = f"SOIC - {data.get('title', '新通知')}"
        html_message = render_to_string(template, {
            'user': user,
            'data': data,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"通知邮件发送成功: {user.email} - {notification_type}")
        
    except User.DoesNotExist:
        logger.error(f"用户不存在: {user_id}")
    
    except Exception as exc:
        logger.error(f"发送通知邮件失败: {user_id}, 错误: {exc}")


# 辅助函数

def generate_verification_code() -> str:
    """生成6位验证码"""
    import random
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])


def get_default_avatar_model_url() -> str:
    """获取默认形象模型URL"""
    return f"{settings.MEDIA_URL}avatars/default/model.glb"


def get_default_avatar_thumbnail_url() -> str:
    """获取默认形象缩略图URL"""
    return f"{settings.MEDIA_URL}avatars/default/thumbnail.png"


def get_default_avatar_config() -> dict:
    """获取默认形象配置"""
    return {
        'gender': 'male',
        'skin_color': '#FDBCB4',
        'hair_color': '#8B4513',
        'eye_color': '#4B0082',
        'height': 1.75,
        'body_type': 'normal',
        'clothing': {
            'top': 'default_shirt',
            'bottom': 'default_pants',
            'shoes': 'default_shoes',
            'accessories': []
        },
        'animations': {
            'idle': 'default_idle',
            'walk': 'default_walk',
            'run': 'default_run',
            'jump': 'default_jump',
            'wave': 'default_wave'
        }
    }


# 事件处理器

from apps.core.events import event_handler

@event_handler('user.registered', async_handler=True)
def handle_user_registered(event):
    """处理用户注册事件"""
    user_id = event.data.get('user_id')
    if user_id:
        # 发送欢迎邮件
        send_welcome_email.delay(int(user_id))
        
        # 更新统计信息
        update_user_stats.delay(int(user_id))


@event_handler('user.email_verified', async_handler=True)
def handle_user_email_verified(event):
    """处理邮箱验证事件"""
    user_id = event.data.get('user_id')
    if user_id:
        # 发送欢迎邮件
        send_welcome_email.delay(int(user_id))


@event_handler('user.login', async_handler=True)
def handle_user_login(event):
    """处理用户登录事件"""
    user_id = event.data.get('user_id')
    if user_id:
        # 更新统计信息
        update_user_stats.delay(int(user_id))