"""
用户URL路由配置 - 独立的用户域路由
职责：定义用户相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import UserViewSet, UserProfileViewSet, UserAvatarViewSet, UserSessionViewSet

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'users', UserViewSet, basename='user')
router.register(r'profiles', UserProfileViewSet, basename='userprofile')
router.register(r'avatars', UserAvatarViewSet, basename='useravatar')
router.register(r'sessions', UserSessionViewSet, basename='usersession')

# URL模式
urlpatterns = [
    # API路由
    path('api/v1/', include(router.urls)),
    
    # 自定义路由（如果需要）
    # path('api/v1/auth/', include('rest_framework.urls')),
]

# 应用命名空间
app_name = 'users'