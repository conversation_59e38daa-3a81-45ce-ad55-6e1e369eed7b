"""
用户数据序列化器 - 数据转换和验证
职责：处理API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from django.utils import timezone
from .models import User, UserProfile, Avatar, UserSession, UserPreference, UserActivity


class ChineseDateTimeField(serializers.DateTimeField):
    """中文时间字段序列化器"""

    def to_representation(self, value):
        """转换为中文时间格式"""
        if value is None:
            return None

        # 确保时间是本地时区
        if timezone.is_aware(value):
            value = timezone.localtime(value)

        # 返回ISO格式，前端可以根据需要格式化
        return value.isoformat()


class ChineseDateField(serializers.DateField):
    """中文日期字段序列化器"""

    def to_representation(self, value):
        """转换为中文日期格式"""
        if value is None:
            return None

        return value.isoformat()


class UserRegistrationSerializer(serializers.Serializer):
    """用户注册序列化器"""
    
    username = serializers.CharField(
        max_length=150,
        min_length=3,
        help_text="用户名，3-150个字符"
    )
    email = serializers.EmailField(help_text="邮箱地址")
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="密码，至少8个字符"
    )
    confirm_password = serializers.CharField(
        write_only=True,
        help_text="确认密码"
    )
    nickname = serializers.CharField(
        max_length=50,
        required=False,
        help_text="昵称，可选"
    )
    agree_to_terms = serializers.BooleanField(
        write_only=True,
        help_text="是否同意服务条款"
    )
    subscribe_newsletter = serializers.BooleanField(
        default=False,
        help_text="是否订阅新闻通讯"
    )
    
    def validate_username(self, value):
        """验证用户名"""
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已存在")
        
        # 检查用户名格式
        if not value.replace('_', '').replace('-', '').isalnum():
            raise serializers.ValidationError("用户名只能包含字母、数字、下划线和连字符")
        
        return value
    
    def validate_email(self, value):
        """验证邮箱"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱已被注册")
        
        return value.lower()
    
    def validate_password(self, value):
        """验证密码"""
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        
        return value
    
    def validate(self, attrs):
        """交叉验证"""
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError({
                'confirm_password': '两次输入的密码不一致'
            })
        
        if not attrs.get('agree_to_terms'):
            raise serializers.ValidationError({
                'agree_to_terms': '必须同意服务条款'
            })
        
        return attrs


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    
    identifier = serializers.CharField(
        help_text="用户名或邮箱"
    )
    password = serializers.CharField(
        write_only=True,
        help_text="密码"
    )
    remember_me = serializers.BooleanField(
        default=False,
        help_text="记住我"
    )
    
    def validate_identifier(self, value):
        """验证标识符"""
        if not value.strip():
            raise serializers.ValidationError("用户名或邮箱不能为空")
        
        return value.strip()


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""

    permissions = serializers.SerializerMethodField(help_text="用户权限列表")
    display_name = serializers.SerializerMethodField(help_text="用户显示名称")
    last_login = ChineseDateTimeField(read_only=True, help_text="最后登录时间")
    created_at = ChineseDateTimeField(read_only=True, help_text="创建时间")
    updated_at = ChineseDateTimeField(read_only=True, help_text="更新时间")
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'display_name',
            'is_active', 'is_verified', 'status', 'user_type',
            'login_count', 'last_login', 'permissions',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'username', 'email', 'is_active', 'is_verified',
            'login_count', 'last_login', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'id': {'help_text': '用户唯一标识符'},
            'username': {'help_text': '用户名'},
            'email': {'help_text': '邮箱地址'},
            'is_active': {'help_text': '账户是否激活'},
            'is_verified': {'help_text': '是否已验证邮箱'},
            'status': {'help_text': '用户状态：active(活跃), inactive(非活跃), suspended(暂停), banned(封禁)'},
            'user_type': {'help_text': '用户类型：regular(普通), premium(高级), vip(VIP), admin(管理员)'},
            'login_count': {'help_text': '登录次数'},
            'last_login': {'help_text': '最后登录时间'},
            'created_at': {'help_text': '账户创建时间'},
            'updated_at': {'help_text': '最后更新时间'},
        }
    
    def get_permissions(self, obj):
        """获取用户权限"""
        return obj.get_permissions()
    
    def get_display_name(self, obj):
        """获取显示名称"""
        return obj.display_name


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""

    user_id = serializers.CharField(source='user.id', read_only=True, help_text="关联用户ID")
    birth_date = ChineseDateField(required=False, allow_null=True, help_text="生日")
    created_at = ChineseDateTimeField(read_only=True, help_text="创建时间")
    updated_at = ChineseDateTimeField(read_only=True, help_text="更新时间")
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'user_id', 'nickname', 'bio', 'birth_date',
            'gender', 'avatar_url', 'location', 'website',
            'social_links', 'privacy_settings',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'id': {'help_text': '资料唯一标识符'},
            'nickname': {'help_text': '用户昵称'},
            'bio': {'help_text': '个人简介，最多500字符'},
            'birth_date': {'help_text': '生日，格式：YYYY-MM-DD'},
            'gender': {'help_text': '性别：male(男), female(女), other(其他), prefer_not_to_say(不愿透露)'},
            'avatar_url': {'help_text': '头像图片URL'},
            'location': {'help_text': '所在位置'},
            'website': {'help_text': '个人网站URL'},
            'social_links': {'help_text': '社交媒体链接，JSON格式'},
            'privacy_settings': {'help_text': '隐私设置，JSON格式'},
            'created_at': {'help_text': '资料创建时间'},
            'updated_at': {'help_text': '资料更新时间'},
        }
        read_only_fields = ['id', 'user_id', 'created_at', 'updated_at']
    
    def validate_nickname(self, value):
        """验证昵称"""
        if len(value.strip()) < 1:
            raise serializers.ValidationError("昵称不能为空")
        
        if len(value) > 50:
            raise serializers.ValidationError("昵称长度不能超过50个字符")
        
        return value.strip()
    
    def validate_bio(self, value):
        """验证个人简介"""
        if len(value) > 500:
            raise serializers.ValidationError("个人简介长度不能超过500个字符")
        
        return value
    
    def validate_social_links(self, value):
        """验证社交链接"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("社交链接必须是字典格式")
        
        allowed_platforms = [
            'twitter', 'facebook', 'instagram', 'linkedin',
            'github', 'youtube', 'tiktok', 'discord'
        ]
        
        for platform, url in value.items():
            if platform not in allowed_platforms:
                raise serializers.ValidationError(f"不支持的社交平台: {platform}")
            
            if not isinstance(url, str) or not url.startswith(('http://', 'https://')):
                raise serializers.ValidationError(f"{platform}链接格式不正确")
        
        return value


class AvatarSerializer(serializers.ModelSerializer):
    """虚拟形象序列化器"""

    user_id = serializers.CharField(source='user.id', read_only=True, help_text="关联用户ID")
    created_at = ChineseDateTimeField(read_only=True, help_text="创建时间")
    updated_at = ChineseDateTimeField(read_only=True, help_text="更新时间")
    
    class Meta:
        model = Avatar
        fields = [
            'id', 'user_id', 'name', 'model_url', 'thumbnail_url',
            'config_data', 'is_default',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user_id', 'created_at', 'updated_at']
        extra_kwargs = {
            'id': {'help_text': '头像唯一标识符'},
            'name': {'help_text': '头像名称，最多50个字符'},
            'model_url': {'help_text': '3D模型文件URL'},
            'thumbnail_url': {'help_text': '头像缩略图URL'},
            'config_data': {'help_text': '头像配置数据，包含性别、肤色、发色、眼色等信息'},
            'is_default': {'help_text': '是否为默认头像'},
        }
    
    def validate_name(self, value):
        """验证形象名称"""
        if len(value.strip()) < 1:
            raise serializers.ValidationError("形象名称不能为空")
        
        if len(value) > 50:
            raise serializers.ValidationError("形象名称长度不能超过50个字符")
        
        return value.strip()
    
    def validate_config_data(self, value):
        """验证配置数据"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("配置数据必须是字典格式")
        
        # 验证必需的配置字段
        required_fields = ['gender', 'skin_color', 'hair_color', 'eye_color']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"缺少必需的配置字段: {field}")
        
        return value


class UserAvatarSerializer(AvatarSerializer):
    """用户虚拟形象序列化器（用于API）"""
    pass


class UserSessionSerializer(serializers.ModelSerializer):
    """用户会话序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user_id = serializers.CharField(source='user.uuid', read_only=True)
    is_current = serializers.SerializerMethodField()
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user_id', 'session_id', 'device_type',
            'device_info', 'ip_address', 'location',
            'is_active', 'is_current', 'last_activity',
            'expires_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'user_id', 'session_id', 'device_info',
            'ip_address', 'location', 'last_activity',
            'expires_at', 'created_at'
        ]
    
    def get_is_current(self, obj):
        """判断是否为当前会话"""
        request = self.context.get('request')
        if request and hasattr(request, 'session'):
            return obj.session_id == request.session.session_key
        return False


class UserPreferenceSerializer(serializers.ModelSerializer):
    """用户偏好设置序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user_id = serializers.CharField(source='user.uuid', read_only=True)
    
    class Meta:
        model = UserPreference
        fields = [
            'id', 'user_id', 'theme', 'language',
            'notification_settings', 'privacy_settings',
            'audio_settings', 'video_settings',
            'control_settings', 'accessibility_settings',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user_id', 'created_at', 'updated_at']
    
    def validate_theme(self, value):
        """验证主题"""
        allowed_themes = ['light', 'dark', 'auto']
        if value not in allowed_themes:
            raise serializers.ValidationError(f"主题必须是以下之一: {', '.join(allowed_themes)}")
        
        return value
    
    def validate_language(self, value):
        """验证语言"""
        allowed_languages = ['zh-CN', 'zh-TW', 'en-US', 'ja-JP', 'ko-KR']
        if value not in allowed_languages:
            raise serializers.ValidationError(f"语言必须是以下之一: {', '.join(allowed_languages)}")
        
        return value


class UserActivitySerializer(serializers.ModelSerializer):
    """用户活动序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user_id = serializers.CharField(source='user.uuid', read_only=True)
    activity_type_display = serializers.CharField(source='get_activity_type_display', read_only=True)
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user_id', 'activity_type', 'activity_type_display',
            'description', 'metadata', 'ip_address', 'user_agent',
            'created_at'
        ]
        read_only_fields = [
            'id', 'user_id', 'activity_type', 'activity_type_display',
            'description', 'metadata', 'ip_address', 'user_agent',
            'created_at'
        ]


class UserStatsSerializer(serializers.Serializer):
    """用户统计信息序列化器"""
    
    spaces_created = serializers.IntegerField(read_only=True)
    spaces_joined = serializers.IntegerField(read_only=True)
    friends_count = serializers.IntegerField(read_only=True)
    messages_count = serializers.IntegerField(read_only=True)
    content_uploaded = serializers.IntegerField(read_only=True)
    total_online_time = serializers.IntegerField(read_only=True)
    last_active_date = serializers.DateTimeField(read_only=True)
    join_date = serializers.DateTimeField(read_only=True)


class UserDetailSerializer(serializers.Serializer):
    """用户详情序列化器（组合序列化器）"""
    
    user = UserSerializer(read_only=True)
    profile = UserProfileSerializer(read_only=True)
    avatar = UserAvatarSerializer(read_only=True)
    stats = UserStatsSerializer(read_only=True)


class UserPublicSerializer(serializers.ModelSerializer):
    """用户公开信息序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    display_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name',
            'is_active', 'is_verified', 'status',
            'user_type', 'created_at'
        ]
        read_only_fields = fields
    
    def get_display_name(self, obj):
        """获取显示名称"""
        return obj.display_name


class UserSearchResultSerializer(serializers.Serializer):
    """用户搜索结果序列化器"""
    
    user = UserPublicSerializer(read_only=True)
    profile = serializers.SerializerMethodField()
    
    def get_profile(self, obj):
        """获取公开资料"""
        if hasattr(obj, 'profile'):
            return {
                'nickname': obj.profile.nickname,
                'bio': obj.profile.bio,
                'avatar_url': obj.profile.avatar_url,
                'location': obj.profile.location
            }
        return None


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    
    current_password = serializers.CharField(
        write_only=True,
        help_text="当前密码"
    )
    new_password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="新密码，至少8个字符"
    )
    confirm_password = serializers.CharField(
        write_only=True,
        help_text="确认新密码"
    )
    
    def validate_new_password(self, value):
        """验证新密码"""
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        
        return value
    
    def validate(self, attrs):
        """交叉验证"""
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({
                'confirm_password': '两次输入的密码不一致'
            })
        
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """邮箱验证序列化器"""
    
    code = serializers.CharField(
        max_length=6,
        min_length=6,
        help_text="6位验证码"
    )
    
    def validate_code(self, value):
        """验证验证码格式"""
        if not value.isdigit():
            raise serializers.ValidationError("验证码必须是6位数字")
        
        return value