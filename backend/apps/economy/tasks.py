"""
经济异步任务 - 后台处理任务
职责：处理经济相关的异步任务，如支付处理、订单状态更新、财务统计等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db.models import Sum, Count, F
from datetime import timedelta
from decimal import Decimal

from apps.core.events import event_bus, DomainEvent

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_payment_notification(self, order_id: int, payment_data: dict):
    """
    处理支付通知
    
    Args:
        order_id: 订单ID
        payment_data: 支付数据
    """
    try:
        from .models import Order, Transaction
        from .services import EconomyService
        
        order = Order.objects.get(id=order_id)
        economy_service = EconomyService()
        
        # 处理支付结果
        if payment_data.get('status') == 'success':
            # 支付成功，更新订单状态
            if order.order_status == 'pending':
                result = economy_service.process_payment(
                    str(order.uuid),
                    {
                        'payment_method': 'external',
                        'external_transaction_id': payment_data.get('transaction_id'),
                        'payment_data': payment_data
                    }
                )
                
                # 发送支付成功通知
                send_payment_notification.delay(
                    order.user.id,
                    'payment_success',
                    {
                        'order_number': order.order_number,
                        'amount': str(order.total_amount),
                        'currency': order.currency.code
                    }
                )
                
                logger.info(f"支付处理成功: {order.order_number}")
        else:
            # 支付失败
            order.order_status = 'cancelled'
            order.save()
            
            # 发送支付失败通知
            send_payment_notification.delay(
                order.user.id,
                'payment_failed',
                {
                    'order_number': order.order_number,
                    'reason': payment_data.get('failure_reason', '支付失败')
                }
            )
            
            logger.warning(f"支付失败: {order.order_number}")
        
    except Exception as exc:
        logger.error(f"处理支付通知失败: {order_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def send_payment_notification(self, user_id: int, notification_type: str, data: dict):
    """
    发送支付通知
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        data: 通知数据
    """
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 检查用户通知偏好
        if hasattr(user, 'preferences'):
            notification_settings = user.preferences.notification_settings
            if not notification_settings.get('payment_notifications', True):
                logger.info(f"用户已关闭支付通知: {user.username}")
                return
        
        # 根据通知类型选择模板和内容
        template_map = {
            'payment_success': {
                'template': 'emails/payment_success.html',
                'subject': '支付成功通知'
            },
            'payment_failed': {
                'template': 'emails/payment_failed.html',
                'subject': '支付失败通知'
            },
            'order_completed': {
                'template': 'emails/order_completed.html',
                'subject': '订单完成通知'
            },
            'transfer_received': {
                'template': 'emails/transfer_received.html',
                'subject': '收到转账通知'
            }
        }
        
        template_info = template_map.get(notification_type)
        if not template_info:
            logger.error(f"未知的通知类型: {notification_type}")
            return
        
        # 渲染邮件模板
        html_message = render_to_string(template_info['template'], {
            'user': user,
            'data': data,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL
        })
        
        # 发送邮件
        send_mail(
            subject=template_info['subject'],
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"支付通知发送成功: {user.email} - {notification_type}")
        
    except Exception as exc:
        logger.error(f"发送支付通知失败: {user_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task
def update_order_status():
    """
    更新订单状态
    """
    try:
        from .models import Order
        
        # 处理超时未支付的订单
        timeout_threshold = timezone.now() - timedelta(hours=24)
        expired_orders = Order.objects.filter(
            order_status='pending',
            created_at__lt=timeout_threshold
        )
        
        expired_count = expired_orders.count()
        expired_orders.update(order_status='cancelled')
        
        # 处理已支付但未完成的订单
        processing_orders = Order.objects.filter(
            order_status='paid',
            paid_at__lt=timezone.now() - timedelta(minutes=5)
        )
        
        completed_count = 0
        for order in processing_orders:
            # 检查是否为虚拟商品（可以立即完成）
            virtual_items = ['virtual_item', 'service', 'subscription', 'avatar_item']
            if all(item.product.product_type in virtual_items for item in order.items.all()):
                order.order_status = 'completed'
                order.save()
                completed_count += 1
                
                # 发送订单完成通知
                send_payment_notification.delay(
                    order.user.id,
                    'order_completed',
                    {
                        'order_number': order.order_number,
                        'items': [
                            {
                                'name': item.product_name,
                                'quantity': item.quantity
                            }
                            for item in order.items.all()
                        ]
                    }
                )
        
        logger.info(f"订单状态更新完成: 取消了 {expired_count} 个超时订单，完成了 {completed_count} 个订单")
        
        return {
            'expired_orders': expired_count,
            'completed_orders': completed_count
        }
        
    except Exception as exc:
        logger.error(f"更新订单状态失败: {exc}")
        return {'expired_orders': 0, 'completed_orders': 0}


@shared_task
def calculate_financial_statistics():
    """
    计算财务统计
    """
    try:
        from .models import Order, Transaction, Product, Currency
        from django.db.models import Sum, Count, Avg
        
        # 计算今日统计
        today = timezone.now().date()
        
        # 订单统计
        daily_orders = Order.objects.filter(
            created_at__date=today,
            order_status__in=['paid', 'completed']
        ).aggregate(
            count=Count('id'),
            revenue=Sum('total_amount')
        )
        
        # 交易统计
        daily_transactions = Transaction.objects.filter(
            created_at__date=today,
            transaction_status='completed'
        ).aggregate(
            count=Count('id'),
            volume=Sum('amount')
        )
        
        # 商品销售统计
        top_products = Product.objects.filter(
            order_items__order__created_at__date=today,
            order_items__order__order_status__in=['paid', 'completed']
        ).annotate(
            daily_sales=Sum('order_items__quantity'),
            daily_revenue=Sum('order_items__total_price')
        ).order_by('-daily_sales')[:10]
        
        # 生成统计报告
        stats_report = {
            'date': today.isoformat(),
            'orders': {
                'count': daily_orders['count'] or 0,
                'revenue': daily_orders['revenue'] or Decimal('0')
            },
            'transactions': {
                'count': daily_transactions['count'] or 0,
                'volume': daily_transactions['volume'] or Decimal('0')
            },
            'top_products': [
                {
                    'name': product.name,
                    'sales': product.daily_sales or 0,
                    'revenue': product.daily_revenue or Decimal('0')
                }
                for product in top_products
            ],
            'generated_at': timezone.now().isoformat()
        }
        
        # 存储统计报告（可以存储到缓存或数据库）
        # cache.set(f'financial_stats_{today}', stats_report, 60 * 60 * 24)
        
        logger.info(f"财务统计计算完成: {today}")
        
        return stats_report
        
    except Exception as exc:
        logger.error(f"计算财务统计失败: {exc}")
        return None


@shared_task
def process_expired_coupons():
    """
    处理过期优惠券
    """
    try:
        from .models import Coupon
        
        # 标记过期的优惠券
        expired_coupons = Coupon.objects.filter(
            status='active',
            valid_until__lt=timezone.now()
        )
        
        expired_count = expired_coupons.count()
        expired_coupons.update(status='expired')
        
        logger.info(f"优惠券过期处理完成: 处理了 {expired_count} 个过期优惠券")
        
        return expired_count
        
    except Exception as exc:
        logger.error(f"处理过期优惠券失败: {exc}")
        return 0


@shared_task
def sync_exchange_rates():
    """
    同步汇率
    """
    try:
        from .models import Currency
        import requests
        
        # 这里应该调用真实的汇率API
        # 简化实现，使用模拟数据
        
        currencies = Currency.objects.filter(status='active')
        updated_count = 0
        
        for currency in currencies:
            try:
                # 模拟汇率更新
                # 实际应该调用汇率API获取最新汇率
                if currency.code != 'USD':
                    # 模拟汇率波动 ±5%
                    import random
                    fluctuation = random.uniform(-0.05, 0.05)
                    new_rate = currency.exchange_rate_to_usd * (1 + fluctuation)
                    
                    currency.exchange_rate_to_usd = new_rate
                    currency.save()
                    updated_count += 1
                
            except Exception as e:
                logger.error(f"更新货币 {currency.code} 汇率失败: {e}")
                continue
        
        logger.info(f"汇率同步完成: 更新了 {updated_count} 个货币的汇率")
        
        return updated_count
        
    except Exception as exc:
        logger.error(f"同步汇率失败: {exc}")
        return 0


@shared_task
def cleanup_old_transactions():
    """
    清理旧交易记录
    """
    try:
        from .models import Transaction
        
        # 清理6个月前的交易记录（保留重要交易）
        cutoff_date = timezone.now() - timedelta(days=180)
        
        old_transactions = Transaction.objects.filter(
            created_at__lt=cutoff_date,
            transaction_type__in=['deposit', 'withdraw'],  # 只清理充值提现记录
            transaction_status='completed'
        )
        
        deleted_count = old_transactions.count()
        old_transactions.delete()
        
        logger.info(f"交易记录清理完成: 删除了 {deleted_count} 条旧记录")
        
        return deleted_count
        
    except Exception as exc:
        logger.error(f"清理旧交易记录失败: {exc}")
        return 0


# 事件处理器

from apps.core.events import event_handler

@event_handler('economy.order_created', async_handler=True)
def handle_order_created(event):
    """处理订单创建事件"""
    order_id = event.data.get('order_id')
    
    if order_id:
        # 设置订单超时检查
        update_order_status.apply_async(countdown=60 * 60 * 24)  # 24小时后检查


@event_handler('economy.payment_completed', async_handler=True)
def handle_payment_completed(event):
    """处理支付完成事件"""
    order_id = event.data.get('order_id')
    user_id = event.data.get('user_id')
    
    if order_id and user_id:
        # 发送支付成功通知
        send_payment_notification.delay(
            int(user_id),
            'payment_success',
            {
                'order_number': event.data.get('order_number'),
                'amount': event.data.get('amount'),
                'currency': event.data.get('currency_code')
            }
        )


@event_handler('economy.transfer_completed', async_handler=True)
def handle_transfer_completed(event):
    """处理转账完成事件"""
    to_user_id = event.data.get('to_user_id')
    from_username = event.data.get('from_username')
    amount = event.data.get('amount')
    currency_code = event.data.get('currency_code')
    
    if to_user_id:
        # 通知接收者
        send_payment_notification.delay(
            int(to_user_id),
            'transfer_received',
            {
                'from_username': from_username,
                'amount': amount,
                'currency': currency_code
            }
        )
