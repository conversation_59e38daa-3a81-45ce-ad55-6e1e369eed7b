"""
经济域数据模型 - 高内聚的虚拟经济相关模型
职责：定义虚拟货币、商品、订单、交易记录等数据模型
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from typing import Tuple, Dict, Any, Optional
from apps.core.models import BaseModel, AuditModel, StatusModel, MetadataModel
from django.contrib.auth import get_user_model

User = get_user_model()


class Currency(StatusModel):
    """虚拟货币模型"""
    
    name = models.CharField(max_length=50, unique=True, verbose_name='货币名称')
    code = models.CharField(max_length=10, unique=True, verbose_name='货币代码')
    symbol = models.CharField(max_length=5, verbose_name='货币符号')
    description = models.TextField(blank=True, verbose_name='货币描述')
    
    # 货币属性
    icon_url = models.URLField(blank=True, verbose_name='图标URL')
    decimal_places = models.PositiveIntegerField(default=2, verbose_name='小数位数')
    
    # 汇率设置
    exchange_rate_to_usd = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('1.0'),
        verbose_name='对美元汇率'
    )
    
    # 发行设置
    total_supply = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='总发行量'
    )
    circulating_supply = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='流通量'
    )
    
    class Meta:
        db_table = 'economy_currencies'
        verbose_name = '虚拟货币'
        verbose_name_plural = '虚拟货币'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class Wallet(MetadataModel):
    """用户钱包模型"""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='wallets',
        verbose_name='用户'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='wallets',
        verbose_name='货币'
    )
    
    # 余额信息
    balance = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='可用余额'
    )
    frozen_balance = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='冻结余额'
    )
    
    # 钱包设置
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    is_locked = models.BooleanField(default=False, verbose_name='是否锁定')
    
    class Meta:
        db_table = 'economy_wallets'
        verbose_name = '用户钱包'
        verbose_name_plural = '用户钱包'
        unique_together = ['user', 'currency']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['currency']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.currency.code}"
    
    @property
    def total_balance(self):
        """总余额"""
        return self.balance + self.frozen_balance
    
    def can_spend(self, amount: Decimal) -> bool:
        """检查是否可以消费指定金额"""
        return self.is_active and not self.is_locked and self.balance >= amount
    
    def freeze_amount(self, amount: Decimal):
        """冻结指定金额"""
        if self.balance >= amount:
            self.balance -= amount
            self.frozen_balance += amount
            self.save()
            return True
        return False
    
    def unfreeze_amount(self, amount: Decimal):
        """解冻指定金额"""
        if self.frozen_balance >= amount:
            self.frozen_balance -= amount
            self.balance += amount
            self.save()
            return True
        return False


class Product(MetadataModel, StatusModel):
    """商品模型"""
    
    PRODUCT_TYPE_CHOICES = [
        ('virtual_item', '虚拟物品'),
        ('service', '服务'),
        ('subscription', '订阅'),
        ('gift', '礼品'),
        ('avatar_item', '形象道具'),
        ('space_decoration', '空间装饰'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='商品名称')
    description = models.TextField(verbose_name='商品描述')
    product_type = models.CharField(
        max_length=20,
        choices=PRODUCT_TYPE_CHOICES,
        verbose_name='商品类型'
    )
    
    # 商品属性
    sku = models.CharField(max_length=50, unique=True, verbose_name='商品编码')
    category = models.CharField(max_length=50, blank=True, verbose_name='商品分类')
    tags = models.JSONField(default=list, blank=True, verbose_name='标签')
    
    # 价格信息
    price = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='价格'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name='货币'
    )
    original_price = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='原价'
    )
    
    # 库存信息
    stock_quantity = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='库存数量'
    )
    unlimited_stock = models.BooleanField(default=False, verbose_name='无限库存')
    
    # 媒体资源
    image_url = models.URLField(blank=True, verbose_name='商品图片')
    gallery_urls = models.JSONField(default=list, blank=True, verbose_name='图片集')
    
    # 销售设置
    is_featured = models.BooleanField(default=False, verbose_name='是否精选')
    is_limited = models.BooleanField(default=False, verbose_name='是否限量')
    max_purchase_per_user = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='每用户最大购买数量'
    )
    
    # 时间设置
    sale_start_at = models.DateTimeField(null=True, blank=True, verbose_name='销售开始时间')
    sale_end_at = models.DateTimeField(null=True, blank=True, verbose_name='销售结束时间')
    
    # 统计信息
    sales_count = models.PositiveIntegerField(default=0, verbose_name='销售数量')
    revenue = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='销售收入'
    )
    
    class Meta:
        db_table = 'economy_products'
        verbose_name = '商品'
        verbose_name_plural = '商品'
        indexes = [
            models.Index(fields=['sku']),
            models.Index(fields=['product_type']),
            models.Index(fields=['category']),
            models.Index(fields=['currency']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['status']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.sku})"
    
    @property
    def is_available(self):
        """检查商品是否可购买"""
        if self.status != 'active':
            return False
        
        # 检查销售时间
        from django.utils import timezone
        now = timezone.now()
        
        if self.sale_start_at and now < self.sale_start_at:
            return False
        
        if self.sale_end_at and now > self.sale_end_at:
            return False
        
        # 检查库存
        if not self.unlimited_stock and self.stock_quantity is not None:
            return self.stock_quantity > 0
        
        return True
    
    @property
    def discount_percentage(self):
        """折扣百分比"""
        if self.original_price and self.original_price > self.price:
            return int((1 - self.price / self.original_price) * 100)
        return 0
    
    def can_purchase(self, user, quantity: int = 1) -> Tuple[bool, str]:
        """检查用户是否可以购买"""
        if not self.is_available:
            return False, "商品不可购买"
        
        # 检查库存
        if not self.unlimited_stock and self.stock_quantity is not None:
            if self.stock_quantity < quantity:
                return False, "库存不足"
        
        # 检查用户购买限制
        if self.max_purchase_per_user:
            purchased_count = Order.objects.filter(
                user=user,
                items__product=self,
                status='completed'
            ).aggregate(
                total=models.Sum('items__quantity')
            )['total'] or 0
            
            if purchased_count + quantity > self.max_purchase_per_user:
                return False, f"超过购买限制（每用户最多{self.max_purchase_per_user}个）"
        
        return True, "可以购买"


class Order(AuditModel, StatusModel, MetadataModel):
    """订单模型"""
    
    ORDER_STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('wallet', '钱包支付'),
        ('external', '外部支付'),
        ('gift', '礼品兑换'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='orders',
        verbose_name='用户'
    )
    
    # 订单信息
    order_number = models.CharField(max_length=32, unique=True, verbose_name='订单号')
    order_status = models.CharField(
        max_length=20,
        choices=ORDER_STATUS_CHOICES,
        default='pending',
        verbose_name='订单状态'
    )
    
    # 金额信息
    subtotal = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='小计'
    )
    discount_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='折扣金额'
    )
    tax_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='税费'
    )
    total_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='总金额'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='orders',
        verbose_name='货币'
    )
    
    # 支付信息
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        verbose_name='支付方式'
    )
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='支付时间')
    
    # 订单备注
    notes = models.TextField(blank=True, verbose_name='订单备注')
    
    class Meta:
        db_table = 'economy_orders'
        verbose_name = '订单'
        verbose_name_plural = '订单'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['order_number']),
            models.Index(fields=['order_status']),
            models.Index(fields=['currency']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"Order {self.order_number} - {self.user.username}"
    
    def calculate_total(self):
        """计算订单总金额"""
        self.subtotal = sum(item.total_price for item in self.items.all())
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
        self.save()


class OrderItem(BaseModel):
    """订单项模型"""
    
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='订单'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='order_items',
        verbose_name='商品'
    )
    
    # 商品信息快照
    product_name = models.CharField(max_length=200, verbose_name='商品名称')
    product_sku = models.CharField(max_length=50, verbose_name='商品编码')
    unit_price = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='单价'
    )
    quantity = models.PositiveIntegerField(verbose_name='数量')
    total_price = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='总价'
    )
    
    class Meta:
        db_table = 'economy_order_items'
        verbose_name = '订单项'
        verbose_name_plural = '订单项'
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['product']),
        ]
    
    def __str__(self):
        return f"{self.product_name} x {self.quantity}"
    
    def save(self, *args, **kwargs):
        # 自动计算总价
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)


class Transaction(MetadataModel):
    """交易记录模型"""
    
    TRANSACTION_TYPE_CHOICES = [
        ('deposit', '充值'),
        ('withdraw', '提现'),
        ('purchase', '购买'),
        ('refund', '退款'),
        ('transfer', '转账'),
        ('reward', '奖励'),
        ('penalty', '扣除'),
        ('commission', '佣金'),
    ]
    
    TRANSACTION_STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='用户'
    )
    
    # 交易信息
    transaction_id = models.CharField(max_length=64, unique=True, verbose_name='交易ID')
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPE_CHOICES,
        verbose_name='交易类型'
    )
    transaction_status = models.CharField(
        max_length=20,
        choices=TRANSACTION_STATUS_CHOICES,
        default='pending',
        verbose_name='交易状态'
    )
    
    # 金额信息
    amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='交易金额'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='货币'
    )
    fee = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0'),
        verbose_name='手续费'
    )
    
    # 余额信息
    balance_before = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='交易前余额'
    )
    balance_after = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='交易后余额'
    )
    
    # 关联信息
    related_order = models.ForeignKey(
        Order,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transactions',
        verbose_name='关联订单'
    )
    
    # 交易描述
    description = models.TextField(verbose_name='交易描述')
    reference = models.CharField(max_length=100, blank=True, verbose_name='外部参考号')
    
    class Meta:
        db_table = 'economy_transactions'
        verbose_name = '交易记录'
        verbose_name_plural = '交易记录'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['transaction_id']),
            models.Index(fields=['transaction_type']),
            models.Index(fields=['transaction_status']),
            models.Index(fields=['currency']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.transaction_id} - {self.user.username} - {self.amount} {self.currency.code}"


class PaymentMethod(StatusModel):
    """支付方式模型"""

    METHOD_TYPE_CHOICES = [
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
        ('bank_card', '银行卡'),
        ('crypto', '加密货币'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='payment_methods',
        verbose_name='用户'
    )

    # 支付方式信息
    method_type = models.CharField(
        max_length=20,
        choices=METHOD_TYPE_CHOICES,
        verbose_name='支付方式类型'
    )
    display_name = models.CharField(max_length=100, verbose_name='显示名称')

    # 支付账户信息（加密存储）
    account_info = models.JSONField(default=dict, verbose_name='账户信息')

    # 设置
    is_default = models.BooleanField(default=False, verbose_name='是否默认')
    is_verified = models.BooleanField(default=False, verbose_name='是否已验证')

    class Meta:
        db_table = 'economy_payment_methods'
        verbose_name = '支付方式'
        verbose_name_plural = '支付方式'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['method_type']),
            models.Index(fields=['is_default']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.display_name}"


class Gift(AuditModel, StatusModel, MetadataModel):
    """礼品模型"""

    GIFT_TYPE_CHOICES = [
        ('product', '商品礼品'),
        ('currency', '货币礼品'),
        ('coupon', '优惠券'),
        ('membership', '会员权益'),
    ]

    GIFT_STATUS_CHOICES = [
        ('pending', '待发送'),
        ('sent', '已发送'),
        ('received', '已接收'),
        ('expired', '已过期'),
        ('cancelled', '已取消'),
    ]

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_gifts',
        verbose_name='发送者'
    )
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_gifts',
        verbose_name='接收者'
    )

    # 礼品信息
    gift_type = models.CharField(
        max_length=20,
        choices=GIFT_TYPE_CHOICES,
        verbose_name='礼品类型'
    )
    gift_status = models.CharField(
        max_length=20,
        choices=GIFT_STATUS_CHOICES,
        default='pending',
        verbose_name='礼品状态'
    )

    # 礼品内容
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='gifts',
        verbose_name='商品'
    )
    currency_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='货币金额'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='gifts',
        verbose_name='货币'
    )

    # 礼品消息
    message = models.TextField(blank=True, verbose_name='礼品消息')

    # 时间设置
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='发送时间')
    received_at = models.DateTimeField(null=True, blank=True, verbose_name='接收时间')

    class Meta:
        db_table = 'economy_gifts'
        verbose_name = '礼品'
        verbose_name_plural = '礼品'
        indexes = [
            models.Index(fields=['sender']),
            models.Index(fields=['recipient']),
            models.Index(fields=['gift_type']),
            models.Index(fields=['gift_status']),
            models.Index(fields=['-created_at']),
        ]

    def __str__(self):
        return f"Gift from {self.sender.username} to {self.recipient.username}"

    @property
    def is_expired(self):
        """检查是否已过期"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False


class Coupon(AuditModel, StatusModel, MetadataModel):
    """优惠券模型"""

    COUPON_TYPE_CHOICES = [
        ('percentage', '百分比折扣'),
        ('fixed_amount', '固定金额折扣'),
        ('free_shipping', '免费配送'),
        ('buy_one_get_one', '买一送一'),
    ]

    name = models.CharField(max_length=100, verbose_name='优惠券名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='优惠券代码')
    description = models.TextField(blank=True, verbose_name='描述')

    # 优惠券类型和值
    coupon_type = models.CharField(
        max_length=20,
        choices=COUPON_TYPE_CHOICES,
        verbose_name='优惠券类型'
    )
    discount_value = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='折扣值'
    )

    # 使用条件
    minimum_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='最低消费金额'
    )
    currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='coupons',
        verbose_name='货币'
    )

    # 适用商品
    applicable_products = models.ManyToManyField(
        Product,
        blank=True,
        related_name='coupons',
        verbose_name='适用商品'
    )
    applicable_categories = models.JSONField(
        default=list,
        blank=True,
        verbose_name='适用分类'
    )

    # 使用限制
    usage_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='使用次数限制'
    )
    usage_limit_per_user = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='每用户使用次数限制'
    )
    used_count = models.PositiveIntegerField(default=0, verbose_name='已使用次数')

    # 时间设置
    valid_from = models.DateTimeField(verbose_name='有效开始时间')
    valid_until = models.DateTimeField(verbose_name='有效结束时间')

    class Meta:
        db_table = 'economy_coupons'
        verbose_name = '优惠券'
        verbose_name_plural = '优惠券'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['coupon_type']),
            models.Index(fields=['currency']),
            models.Index(fields=['valid_from', 'valid_until']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def is_valid(self):
        """检查优惠券是否有效"""
        if self.status != 'active':
            return False

        from django.utils import timezone
        now = timezone.now()

        if now < self.valid_from or now > self.valid_until:
            return False

        if self.usage_limit and self.used_count >= self.usage_limit:
            return False

        return True

    def can_use(self, user, order_amount: Decimal, products: list = None) -> Tuple[bool, str]:
        """检查用户是否可以使用优惠券"""
        if not self.is_valid:
            return False, "优惠券无效或已过期"

        # 检查最低消费金额
        if self.minimum_amount and order_amount < self.minimum_amount:
            return False, f"订单金额需满{self.minimum_amount}才能使用"

        # 检查用户使用次数限制
        if self.usage_limit_per_user:
            user_usage = CouponUsage.objects.filter(
                coupon=self,
                user=user
            ).count()

            if user_usage >= self.usage_limit_per_user:
                return False, "您已达到该优惠券的使用次数限制"

        # 检查适用商品
        if self.applicable_products.exists() and products:
            applicable_product_ids = set(self.applicable_products.values_list('id', flat=True))
            order_product_ids = set(p.id for p in products)

            if not applicable_product_ids.intersection(order_product_ids):
                return False, "该优惠券不适用于订单中的商品"

        return True, "可以使用"


class CouponUsage(BaseModel):
    """优惠券使用记录模型"""

    coupon = models.ForeignKey(
        Coupon,
        on_delete=models.CASCADE,
        related_name='usage_records',
        verbose_name='优惠券'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='coupon_usages',
        verbose_name='用户'
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='coupon_usages',
        verbose_name='订单'
    )

    # 使用信息
    discount_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        verbose_name='折扣金额'
    )

    class Meta:
        db_table = 'economy_coupon_usages'
        verbose_name = '优惠券使用记录'
        verbose_name_plural = '优惠券使用记录'
        indexes = [
            models.Index(fields=['coupon']),
            models.Index(fields=['user']),
            models.Index(fields=['order']),
            models.Index(fields=['-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} used {self.coupon.code}"


class EconomySettings(MetadataModel):
    """经济系统设置模型"""

    # 系统设置
    default_currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='default_settings',
        verbose_name='默认货币'
    )

    # 交易设置
    transaction_fee_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        default=Decimal('0.0100'),
        verbose_name='交易手续费率'
    )
    minimum_transaction_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0.01'),
        verbose_name='最小交易金额'
    )
    maximum_transaction_amount = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('10000.00'),
        verbose_name='最大交易金额'
    )

    # 钱包设置
    initial_balance = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('0.00'),
        verbose_name='初始余额'
    )
    daily_transaction_limit = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        default=Decimal('1000.00'),
        verbose_name='日交易限额'
    )

    class Meta:
        db_table = 'economy_settings'
        verbose_name = '经济系统设置'
        verbose_name_plural = '经济系统设置'

    def __str__(self):
        return f"Economy Settings - {self.default_currency.code}"
