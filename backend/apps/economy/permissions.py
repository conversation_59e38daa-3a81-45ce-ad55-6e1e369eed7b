"""
经济权限控制 - 访问控制和权限验证
职责：定义经济相关的权限类，控制API访问权限
"""

from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView
from django.contrib.auth.models import AnonymousUser
from decimal import Decimal
from typing import Tuple
from .models import Order, Wallet, Product, Transaction


class CanAccessWallet(permissions.BasePermission):
    """
    可以访问钱包的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        wallet = obj if hasattr(obj, 'user') else obj
        
        # 只有钱包所有者可以访问
        return wallet.user == request.user


class CanTransferFunds(permissions.BasePermission):
    """
    可以转账的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 检查用户状态
        if request.user.status != 'active':
            return False
        
        # 检查用户类型限制
        if hasattr(request.user, 'user_type'):
            restricted_types = ['banned', 'suspended', 'limited']
            if request.user.user_type in restricted_types:
                return False
        
        return True


class CanCreateOrder(permissions.BasePermission):
    """
    可以创建订单的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 检查用户状态
        if request.user.status != 'active':
            return False
        
        # 检查用户是否被限制购买
        if hasattr(request.user, 'user_type'):
            if request.user.user_type in ['banned', 'suspended']:
                return False
        
        return True


class CanViewOrder(permissions.BasePermission):
    """
    可以查看订单的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        order = obj if isinstance(obj, Order) else obj
        
        # 订单所有者可以查看
        if order.user == request.user:
            return True
        
        # 管理员可以查看所有订单
        if request.user.is_staff or request.user.user_type in ['admin', 'moderator']:
            return True
        
        return False


class CanPayOrder(permissions.BasePermission):
    """
    可以支付订单的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        order = obj if isinstance(obj, Order) else obj
        
        # 只有订单所有者可以支付
        if order.user != request.user:
            return False
        
        # 检查订单状态
        if order.order_status != 'pending':
            return False
        
        # 检查用户支付权限
        if hasattr(request.user, 'user_type'):
            if request.user.user_type in ['banned', 'suspended', 'payment_restricted']:
                return False
        
        return True


class CanViewTransaction(permissions.BasePermission):
    """
    可以查看交易的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        transaction = obj if isinstance(obj, Transaction) else obj
        
        # 交易所有者可以查看
        if transaction.user == request.user:
            return True
        
        # 管理员可以查看所有交易
        if request.user.is_staff or request.user.user_type in ['admin', 'financial_admin']:
            return True
        
        return False


class CanManageProducts(permissions.BasePermission):
    """
    可以管理商品的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 只有管理员和商品管理员可以管理商品
        return (request.user.is_staff or 
                request.user.user_type in ['admin', 'product_admin'])


class CanViewFinancialStats(permissions.BasePermission):
    """
    可以查看财务统计的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 只有管理员和财务管理员可以查看财务统计
        return (request.user.is_staff or 
                request.user.user_type in ['admin', 'financial_admin'])


class CanManageCoupons(permissions.BasePermission):
    """
    可以管理优惠券的权限
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 只有管理员和营销管理员可以管理优惠券
        return (request.user.is_staff or 
                request.user.user_type in ['admin', 'marketing_admin'])


# 权限检查函数

def can_access_wallet(user, wallet: Wallet) -> bool:
    """
    检查用户是否可以访问钱包
    
    Args:
        user: 用户对象
        wallet: 钱包对象
        
    Returns:
        bool: 是否可以访问
    """
    if isinstance(user, AnonymousUser):
        return False
    
    return wallet.user == user


def can_transfer_funds(user, amount: Decimal = None) -> Tuple[bool, str]:
    """
    检查用户是否可以转账
    
    Args:
        user: 用户对象
        amount: 转账金额（可选）
        
    Returns:
        Tuple[bool, str]: (是否可以转账, 原因)
    """
    if isinstance(user, AnonymousUser):
        return False, "用户未登录"
    
    if user.status != 'active':
        return False, "用户状态异常"
    
    if hasattr(user, 'user_type'):
        restricted_types = ['banned', 'suspended', 'limited']
        if user.user_type in restricted_types:
            return False, f"用户类型受限: {user.user_type}"
    
    # 检查转账限额
    if amount:
        from .models import EconomySettings
        
        try:
            settings = EconomySettings.objects.first()
            if settings:
                if amount < settings.minimum_transaction_amount:
                    return False, f"转账金额不能低于 {settings.minimum_transaction_amount}"
                
                if amount > settings.maximum_transaction_amount:
                    return False, f"转账金额不能超过 {settings.maximum_transaction_amount}"
        except:
            pass
    
    return True, "可以转账"


def can_create_order(user) -> Tuple[bool, str]:
    """
    检查用户是否可以创建订单
    
    Args:
        user: 用户对象
        
    Returns:
        Tuple[bool, str]: (是否可以创建订单, 原因)
    """
    if isinstance(user, AnonymousUser):
        return False, "用户未登录"
    
    if user.status != 'active':
        return False, "用户状态异常"
    
    if hasattr(user, 'user_type'):
        if user.user_type in ['banned', 'suspended']:
            return False, f"用户被{user.user_type}，无法创建订单"
    
    return True, "可以创建订单"


def can_pay_order(user, order: Order) -> Tuple[bool, str]:
    """
    检查用户是否可以支付订单
    
    Args:
        user: 用户对象
        order: 订单对象
        
    Returns:
        Tuple[bool, str]: (是否可以支付, 原因)
    """
    if isinstance(user, AnonymousUser):
        return False, "用户未登录"
    
    if order.user != user:
        return False, "不是您的订单"
    
    if order.order_status != 'pending':
        return False, f"订单状态异常: {order.order_status}"
    
    if hasattr(user, 'user_type'):
        if user.user_type in ['banned', 'suspended', 'payment_restricted']:
            return False, f"用户支付权限受限: {user.user_type}"
    
    return True, "可以支付"


def can_purchase_product(user, product: Product, quantity: int = 1) -> Tuple[bool, str]:
    """
    检查用户是否可以购买商品
    
    Args:
        user: 用户对象
        product: 商品对象
        quantity: 购买数量
        
    Returns:
        Tuple[bool, str]: (是否可以购买, 原因)
    """
    if isinstance(user, AnonymousUser):
        return False, "用户未登录"
    
    # 检查商品是否可购买
    if not product.is_available:
        return False, "商品不可购买"
    
    # 检查库存
    if not product.unlimited_stock and product.stock_quantity is not None:
        if product.stock_quantity < quantity:
            return False, "库存不足"
    
    # 检查用户购买限制
    if product.max_purchase_per_user:
        purchased_count = Order.objects.filter(
            user=user,
            items__product=product,
            order_status='completed'
        ).aggregate(
            total=models.Sum('items__quantity')
        )['total'] or 0
        
        if purchased_count + quantity > product.max_purchase_per_user:
            return False, f"超过购买限制（每用户最多{product.max_purchase_per_user}个）"
    
    return True, "可以购买"


def get_user_economy_permissions(user) -> dict:
    """
    获取用户的经济权限
    
    Args:
        user: 用户对象
        
    Returns:
        dict: 权限字典
    """
    if isinstance(user, AnonymousUser):
        return {
            'can_view_wallet': False,
            'can_transfer': False,
            'can_create_order': False,
            'can_view_transactions': False,
            'can_manage_products': False,
            'can_view_financial_stats': False,
            'can_manage_coupons': False
        }
    
    can_transfer, _ = can_transfer_funds(user)
    can_order, _ = can_create_order(user)
    
    return {
        'can_view_wallet': user.status == 'active',
        'can_transfer': can_transfer,
        'can_create_order': can_order,
        'can_view_transactions': user.status == 'active',
        'can_manage_products': (
            user.is_staff or 
            user.user_type in ['admin', 'product_admin']
        ),
        'can_view_financial_stats': (
            user.is_staff or 
            user.user_type in ['admin', 'financial_admin']
        ),
        'can_manage_coupons': (
            user.is_staff or 
            user.user_type in ['admin', 'marketing_admin']
        )
    }


def check_transaction_limits(user, amount: Decimal, currency_code: str) -> Tuple[bool, str]:
    """
    检查交易限额
    
    Args:
        user: 用户对象
        amount: 交易金额
        currency_code: 货币代码
        
    Returns:
        Tuple[bool, str]: (是否通过检查, 原因)
    """
    from .models import EconomySettings, Transaction, Currency
    from django.db.models import Sum
    from django.utils import timezone
    
    try:
        settings = EconomySettings.objects.first()
        currency = Currency.objects.get(code=currency_code)
        
        if not settings:
            return True, "无限制设置"
        
        # 检查单笔交易限额
        if amount < settings.minimum_transaction_amount:
            return False, f"交易金额不能低于 {settings.minimum_transaction_amount}"
        
        if amount > settings.maximum_transaction_amount:
            return False, f"交易金额不能超过 {settings.maximum_transaction_amount}"
        
        # 检查日交易限额
        today = timezone.now().date()
        daily_total = Transaction.objects.filter(
            user=user,
            currency=currency,
            created_at__date=today,
            transaction_status='completed',
            amount__gt=0  # 只计算支出
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        if daily_total + amount > settings.daily_transaction_limit:
            return False, f"超过日交易限额 {settings.daily_transaction_limit}"
        
        return True, "通过限额检查"
        
    except Exception as e:
        return False, f"检查限额时出错: {str(e)}"
