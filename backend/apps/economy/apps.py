"""
经济应用配置 - 经济域应用配置
职责：配置经济应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class EconomyConfig(AppConfig):
    """经济应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.economy'
    verbose_name = '虚拟经济系统'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        # 初始化经济系统
        self.initialize_economy_system()
        
        logger.info("经济应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import EconomyService
            
            # 注册经济服务为单例
            container.register_singleton(EconomyService, EconomyService)
            
            logger.info("经济服务注册完成")
            
        except Exception as e:
            logger.error(f"经济服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入事件处理器（这会自动注册）
            # 暂时跳过Celery相关的任务导入
            # from . import tasks

            logger.info("经济事件处理器注册完成")

        except Exception as e:
            logger.error(f"经济事件处理器注册失败: {e}")
    
    def initialize_economy_system(self):
        """初始化经济系统"""
        try:
            # 不在应用启动时访问数据库，而是注册一个信号处理器
            # 在第一次请求时或者通过管理命令时初始化
            from django.db.models.signals import post_migrate
            from django.dispatch import receiver

            @receiver(post_migrate, sender=self)
            def create_default_economy_data(sender, **kwargs):
                """迁移完成后创建默认经济数据"""
                try:
                    self._create_default_currency()
                    self._create_default_settings()
                    logger.info("经济系统默认数据创建完成")
                except Exception as e:
                    logger.error(f"创建默认经济数据失败: {e}")

            logger.info("经济系统初始化完成")

        except Exception as e:
            logger.error(f"经济系统初始化失败: {e}")
    
    def _create_default_currency(self):
        """创建默认货币"""
        from .models import Currency
        
        # 创建默认虚拟货币
        default_currency, created = Currency.objects.get_or_create(
            code='COIN',
            defaults={
                'name': 'SOIC Coin',
                'symbol': '🪙',
                'description': '平台默认虚拟货币',
                'decimal_places': 2,
                'exchange_rate_to_usd': 0.01,  # 1 COIN = 0.01 USD
                'status': 'active'
            }
        )
        
        if created:
            logger.info("默认货币创建成功: SOIC Coin")
    
    def _create_default_settings(self):
        """创建默认经济设置"""
        from .models import EconomySettings, Currency
        from decimal import Decimal
        
        # 获取默认货币
        try:
            default_currency = Currency.objects.get(code='COIN')
        except Currency.DoesNotExist:
            logger.error("默认货币不存在，无法创建经济设置")
            return
        
        # 创建默认经济设置
        settings, created = EconomySettings.objects.get_or_create(
            defaults={
                'default_currency': default_currency,
                'transaction_fee_rate': Decimal('0.0100'),  # 1%手续费
                'minimum_transaction_amount': Decimal('0.01'),
                'maximum_transaction_amount': Decimal('10000.00'),
                'initial_balance': Decimal('100.00'),  # 新用户初始100币
                'daily_transaction_limit': Decimal('1000.00')
            }
        )
        
        if created:
            logger.info("默认经济设置创建成功")
