"""
经济域信号处理器
处理经济相关的Django信号
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import Order, Transaction, Wallet

User = get_user_model()


@receiver(post_save, sender=User)
def create_user_wallet(sender, instance, created, **kwargs):
    """
    用户创建时自动创建钱包
    """
    if created:
        try:
            from .models import Currency, EconomySettings
            
            # 获取默认货币
            default_currency = Currency.objects.filter(code='COIN').first()
            if default_currency:
                # 获取初始余额设置
                settings = EconomySettings.objects.first()
                initial_balance = settings.initial_balance if settings else 0
                
                # 创建钱包
                Wallet.objects.create(
                    user=instance,
                    currency=default_currency,
                    balance=initial_balance
                )
        except Exception as e:
            # 如果创建钱包失败，记录错误但不阻止用户创建
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"创建用户钱包失败: {e}")


@receiver(post_save, sender=Order)
def handle_order_created(sender, instance, created, **kwargs):
    """
    处理订单创建
    """
    if created:
        # 可以在这里添加订单创建后的逻辑
        # 比如库存检查、支付处理等
        pass


@receiver(post_save, sender=Transaction)
def handle_transaction_created(sender, instance, created, **kwargs):
    """
    处理交易创建
    """
    if created and instance.transaction_status == 'completed':
        # 可以在这里添加交易完成后的逻辑
        # 比如更新钱包余额、发送通知等
        pass


@receiver(post_delete, sender=Order)
def handle_order_deleted(sender, instance, **kwargs):
    """
    处理订单删除
    """
    # 可以在这里添加订单删除后的逻辑
    pass
