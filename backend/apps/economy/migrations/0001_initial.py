# Generated by Django 5.2 on 2025-07-30 16:06

import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='货币名称')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='货币代码')),
                ('symbol', models.CharField(max_length=5, verbose_name='货币符号')),
                ('description', models.TextField(blank=True, verbose_name='货币描述')),
                ('icon_url', models.URLField(blank=True, verbose_name='图标URL')),
                ('decimal_places', models.PositiveIntegerField(default=2, verbose_name='小数位数')),
                ('exchange_rate_to_usd', models.DecimalField(decimal_places=8, default=Decimal('1.0'), max_digits=20, verbose_name='对美元汇率')),
                ('total_supply', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True, verbose_name='总发行量')),
                ('circulating_supply', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='流通量')),
            ],
            options={
                'verbose_name': '虚拟货币',
                'verbose_name_plural': '虚拟货币',
                'db_table': 'economy_currencies',
                'indexes': [models.Index(fields=['code'], name='economy_cur_code_70daf4_idx'), models.Index(fields=['status'], name='economy_cur_status_90e008_idx')],
            },
        ),
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('name', models.CharField(max_length=100, verbose_name='优惠券名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='优惠券代码')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('coupon_type', models.CharField(choices=[('percentage', '百分比折扣'), ('fixed_amount', '固定金额折扣'), ('free_shipping', '免费配送'), ('buy_one_get_one', '买一送一')], max_length=20, verbose_name='优惠券类型')),
                ('discount_value', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='折扣值')),
                ('minimum_amount', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True, verbose_name='最低消费金额')),
                ('applicable_categories', models.JSONField(blank=True, default=list, verbose_name='适用分类')),
                ('usage_limit', models.PositiveIntegerField(blank=True, null=True, verbose_name='使用次数限制')),
                ('usage_limit_per_user', models.PositiveIntegerField(blank=True, null=True, verbose_name='每用户使用次数限制')),
                ('used_count', models.PositiveIntegerField(default=0, verbose_name='已使用次数')),
                ('valid_from', models.DateTimeField(verbose_name='有效开始时间')),
                ('valid_until', models.DateTimeField(verbose_name='有效结束时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coupons', to='economy.currency', verbose_name='货币')),
            ],
            options={
                'verbose_name': '优惠券',
                'verbose_name_plural': '优惠券',
                'db_table': 'economy_coupons',
            },
        ),
        migrations.CreateModel(
            name='EconomySettings',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('transaction_fee_rate', models.DecimalField(decimal_places=4, default=Decimal('0.0100'), max_digits=5, verbose_name='交易手续费率')),
                ('minimum_transaction_amount', models.DecimalField(decimal_places=8, default=Decimal('0.01'), max_digits=20, verbose_name='最小交易金额')),
                ('maximum_transaction_amount', models.DecimalField(decimal_places=8, default=Decimal('10000.00'), max_digits=20, verbose_name='最大交易金额')),
                ('initial_balance', models.DecimalField(decimal_places=8, default=Decimal('0.00'), max_digits=20, verbose_name='初始余额')),
                ('daily_transaction_limit', models.DecimalField(decimal_places=8, default=Decimal('1000.00'), max_digits=20, verbose_name='日交易限额')),
                ('default_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='default_settings', to='economy.currency', verbose_name='默认货币')),
            ],
            options={
                'verbose_name': '经济系统设置',
                'verbose_name_plural': '经济系统设置',
                'db_table': 'economy_settings',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('order_number', models.CharField(max_length=32, unique=True, verbose_name='订单号')),
                ('order_status', models.CharField(choices=[('pending', '待支付'), ('paid', '已支付'), ('processing', '处理中'), ('completed', '已完成'), ('cancelled', '已取消'), ('refunded', '已退款')], default='pending', max_length=20, verbose_name='订单状态')),
                ('subtotal', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='小计')),
                ('discount_amount', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='折扣金额')),
                ('tax_amount', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='税费')),
                ('total_amount', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='总金额')),
                ('payment_method', models.CharField(choices=[('wallet', '钱包支付'), ('external', '外部支付'), ('gift', '礼品兑换')], max_length=20, verbose_name='支付方式')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('notes', models.TextField(blank=True, verbose_name='订单备注')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='economy.currency', verbose_name='货币')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'db_table': 'economy_orders',
            },
        ),
        migrations.CreateModel(
            name='CouponUsage',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('discount_amount', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='折扣金额')),
                ('coupon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_records', to='economy.coupon', verbose_name='优惠券')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coupon_usages', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coupon_usages', to='economy.order', verbose_name='订单')),
            ],
            options={
                'verbose_name': '优惠券使用记录',
                'verbose_name_plural': '优惠券使用记录',
                'db_table': 'economy_coupon_usages',
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('method_type', models.CharField(choices=[('alipay', '支付宝'), ('wechat', '微信支付'), ('paypal', 'PayPal'), ('stripe', 'Stripe'), ('bank_card', '银行卡'), ('crypto', '加密货币')], max_length=20, verbose_name='支付方式类型')),
                ('display_name', models.CharField(max_length=100, verbose_name='显示名称')),
                ('account_info', models.JSONField(default=dict, verbose_name='账户信息')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认')),
                ('is_verified', models.BooleanField(default=False, verbose_name='是否已验证')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_methods', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '支付方式',
                'verbose_name_plural': '支付方式',
                'db_table': 'economy_payment_methods',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('description', models.TextField(verbose_name='商品描述')),
                ('product_type', models.CharField(choices=[('virtual_item', '虚拟物品'), ('service', '服务'), ('subscription', '订阅'), ('gift', '礼品'), ('avatar_item', '形象道具'), ('space_decoration', '空间装饰')], max_length=20, verbose_name='商品类型')),
                ('sku', models.CharField(max_length=50, unique=True, verbose_name='商品编码')),
                ('category', models.CharField(blank=True, max_length=50, verbose_name='商品分类')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='标签')),
                ('price', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='价格')),
                ('original_price', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True, verbose_name='原价')),
                ('stock_quantity', models.PositiveIntegerField(blank=True, null=True, verbose_name='库存数量')),
                ('unlimited_stock', models.BooleanField(default=False, verbose_name='无限库存')),
                ('image_url', models.URLField(blank=True, verbose_name='商品图片')),
                ('gallery_urls', models.JSONField(blank=True, default=list, verbose_name='图片集')),
                ('is_featured', models.BooleanField(default=False, verbose_name='是否精选')),
                ('is_limited', models.BooleanField(default=False, verbose_name='是否限量')),
                ('max_purchase_per_user', models.PositiveIntegerField(blank=True, null=True, verbose_name='每用户最大购买数量')),
                ('sale_start_at', models.DateTimeField(blank=True, null=True, verbose_name='销售开始时间')),
                ('sale_end_at', models.DateTimeField(blank=True, null=True, verbose_name='销售结束时间')),
                ('sales_count', models.PositiveIntegerField(default=0, verbose_name='销售数量')),
                ('revenue', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='销售收入')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='economy.currency', verbose_name='货币')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
                'db_table': 'economy_products',
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('product_name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('product_sku', models.CharField(max_length=50, verbose_name='商品编码')),
                ('unit_price', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='单价')),
                ('quantity', models.PositiveIntegerField(verbose_name='数量')),
                ('total_price', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='总价')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='economy.order', verbose_name='订单')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='economy.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '订单项',
                'verbose_name_plural': '订单项',
                'db_table': 'economy_order_items',
            },
        ),
        migrations.CreateModel(
            name='Gift',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('gift_type', models.CharField(choices=[('product', '商品礼品'), ('currency', '货币礼品'), ('coupon', '优惠券'), ('membership', '会员权益')], max_length=20, verbose_name='礼品类型')),
                ('gift_status', models.CharField(choices=[('pending', '待发送'), ('sent', '已发送'), ('received', '已接收'), ('expired', '已过期'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='礼品状态')),
                ('currency_amount', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True, verbose_name='货币金额')),
                ('message', models.TextField(blank=True, verbose_name='礼品消息')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='发送时间')),
                ('received_at', models.DateTimeField(blank=True, null=True, verbose_name='接收时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gifts', to='economy.currency', verbose_name='货币')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_gifts', to=settings.AUTH_USER_MODEL, verbose_name='接收者')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_gifts', to=settings.AUTH_USER_MODEL, verbose_name='发送者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gifts', to='economy.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '礼品',
                'verbose_name_plural': '礼品',
                'db_table': 'economy_gifts',
            },
        ),
        migrations.AddField(
            model_name='coupon',
            name='applicable_products',
            field=models.ManyToManyField(blank=True, related_name='coupons', to='economy.product', verbose_name='适用商品'),
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('transaction_id', models.CharField(max_length=64, unique=True, verbose_name='交易ID')),
                ('transaction_type', models.CharField(choices=[('deposit', '充值'), ('withdraw', '提现'), ('purchase', '购买'), ('refund', '退款'), ('transfer', '转账'), ('reward', '奖励'), ('penalty', '扣除'), ('commission', '佣金')], max_length=20, verbose_name='交易类型')),
                ('transaction_status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='交易状态')),
                ('amount', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='交易金额')),
                ('fee', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='手续费')),
                ('balance_before', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='交易前余额')),
                ('balance_after', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='交易后余额')),
                ('description', models.TextField(verbose_name='交易描述')),
                ('reference', models.CharField(blank=True, max_length=100, verbose_name='外部参考号')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='economy.currency', verbose_name='货币')),
                ('related_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions', to='economy.order', verbose_name='关联订单')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '交易记录',
                'verbose_name_plural': '交易记录',
                'db_table': 'economy_transactions',
            },
        ),
        migrations.CreateModel(
            name='Wallet',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('balance', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='可用余额')),
                ('frozen_balance', models.DecimalField(decimal_places=8, default=Decimal('0'), max_digits=20, verbose_name='冻结余额')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_locked', models.BooleanField(default=False, verbose_name='是否锁定')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wallets', to='economy.currency', verbose_name='货币')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wallets', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户钱包',
                'verbose_name_plural': '用户钱包',
                'db_table': 'economy_wallets',
            },
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user'], name='economy_ord_user_id_0afa42_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['order_number'], name='economy_ord_order_n_5528c8_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['order_status'], name='economy_ord_order_s_c06294_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['currency'], name='economy_ord_currenc_68d98c_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['-created_at'], name='economy_ord_created_a2e2aa_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['coupon'], name='economy_cou_coupon__65638f_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['user'], name='economy_cou_user_id_b92a82_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['order'], name='economy_cou_order_i_3ae378_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['-created_at'], name='economy_cou_created_16949c_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentmethod',
            index=models.Index(fields=['user'], name='economy_pay_user_id_2fe3f4_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentmethod',
            index=models.Index(fields=['method_type'], name='economy_pay_method__1f7913_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentmethod',
            index=models.Index(fields=['is_default'], name='economy_pay_is_defa_a57fec_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['sku'], name='economy_pro_sku_2f9daf_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['product_type'], name='economy_pro_product_4dd8f4_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category'], name='economy_pro_categor_8525eb_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['currency'], name='economy_pro_currenc_69e6b9_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_featured'], name='economy_pro_is_feat_4340df_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['status'], name='economy_pro_status_fd19e6_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['-created_at'], name='economy_pro_created_8b5cb8_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['order'], name='economy_ord_order_i_262a34_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['product'], name='economy_ord_product_68401c_idx'),
        ),
        migrations.AddIndex(
            model_name='gift',
            index=models.Index(fields=['sender'], name='economy_gif_sender__fb92dc_idx'),
        ),
        migrations.AddIndex(
            model_name='gift',
            index=models.Index(fields=['recipient'], name='economy_gif_recipie_4c7713_idx'),
        ),
        migrations.AddIndex(
            model_name='gift',
            index=models.Index(fields=['gift_type'], name='economy_gif_gift_ty_3304d1_idx'),
        ),
        migrations.AddIndex(
            model_name='gift',
            index=models.Index(fields=['gift_status'], name='economy_gif_gift_st_13af79_idx'),
        ),
        migrations.AddIndex(
            model_name='gift',
            index=models.Index(fields=['-created_at'], name='economy_gif_created_f201c9_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['code'], name='economy_cou_code_0a996a_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['coupon_type'], name='economy_cou_coupon__6edbd3_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['currency'], name='economy_cou_currenc_7ca4ee_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['valid_from', 'valid_until'], name='economy_cou_valid_f_e346d9_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['status'], name='economy_cou_status_2d40ca_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['user'], name='economy_tra_user_id_4b990a_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_id'], name='economy_tra_transac_4dc60b_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_type'], name='economy_tra_transac_d17dd6_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_status'], name='economy_tra_transac_eab1c8_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['currency'], name='economy_tra_currenc_31b0ce_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['-created_at'], name='economy_tra_created_99e6f0_idx'),
        ),
        migrations.AddIndex(
            model_name='wallet',
            index=models.Index(fields=['user'], name='economy_wal_user_id_edb313_idx'),
        ),
        migrations.AddIndex(
            model_name='wallet',
            index=models.Index(fields=['currency'], name='economy_wal_currenc_d5e5d9_idx'),
        ),
        migrations.AddIndex(
            model_name='wallet',
            index=models.Index(fields=['is_active'], name='economy_wal_is_acti_b93d7f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='wallet',
            unique_together={('user', 'currency')},
        ),
    ]
