"""
经济视图控制器 - 薄层HTTP处理
职责：处理经济相关的HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.core.responses import APIResponse, SuccessMessages
from apps.core.exceptions import handle_exceptions
from .services import EconomyService
from .serializers import (
    OrderCreateSerializer,
    PaymentSerializer,
    TransferSerializer,
    ProductSerializer
)

logger = logging.getLogger(__name__)


class WalletViewSet(viewsets.ViewSet):
    """
    钱包视图集
    
    职责：
    1. 处理钱包相关的HTTP请求
    2. 调用经济业务服务
    3. 返回标准化响应
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.economy_service = EconomyService()
    
    @action(detail=False, methods=['get'])
    def balance(self, request: Request) -> Response:
        """
        获取钱包余额
        
        GET /api/v1/wallets/balance/?currency=COIN
        """
        currency_code = request.query_params.get('currency')
        
        # 调用业务服务
        result = self.economy_service.get_user_wallet(
            str(request.user.uuid),
            currency_code
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['post'])
    def transfer(self, request: Request) -> Response:
        """
        转账
        
        POST /api/v1/wallets/transfer/
        """
        logger.info(f"转账请求: {request.user.username}")
        
        # 数据验证
        serializer = TransferSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.economy_service.transfer_funds(
            str(request.user.uuid),
            serializer.validated_data['to_user_id'],
            serializer.validated_data['amount'],
            serializer.validated_data['currency_code'],
            serializer.validated_data.get('description', '')
        )
        
        return APIResponse.success(
            data=result,
            message="转账成功"
        )


class ProductViewSet(viewsets.ViewSet):
    """商品视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.economy_service = EconomyService()
    def list(self, request: Request) -> Response:
        """
        获取商品列表
        
        GET /api/v1/products/?category=avatar&product_type=virtual_item&page=1&page_size=20
        """
        # 构建过滤条件
        filters = {
            'category': request.query_params.get('category'),
            'product_type': request.query_params.get('product_type'),
            'is_featured': request.query_params.get('is_featured') == 'true',
            'currency_code': request.query_params.get('currency'),
            'order_by': request.query_params.get('order_by', '-created_at')
        }
        
        # 移除空值
        filters = {k: v for k, v in filters.items() if v is not None}
        
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)
        
        # 调用业务服务
        result = self.economy_service.get_products(filters, page, page_size)
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 10))  # 缓存10分钟
    def featured(self, request: Request) -> Response:
        """
        获取精选商品
        
        GET /api/v1/products/featured/?limit=10
        """
        limit = min(int(request.query_params.get('limit', 10)), 50)
        
        # 调用业务服务
        result = self.economy_service.get_products(
            {'is_featured': True, 'order_by': '-created_at'},
            1,
            limit
        )
        
        return APIResponse.success(data=result['products'])


class OrderViewSet(viewsets.ViewSet):
    """订单视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.economy_service = EconomyService()
    def create(self, request: Request) -> Response:
        """
        创建订单
        
        POST /api/v1/orders/
        """
        logger.info(f"创建订单请求: {request.user.username}")
        
        # 数据验证
        serializer = OrderCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.economy_service.create_order(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="订单创建成功"
        )
    def list(self, request: Request) -> Response:
        """
        获取用户订单列表
        
        GET /api/v1/orders/?status=pending&page=1&page_size=20
        """
        status_filter = request.query_params.get('status')
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)
        
        # 调用业务服务
        result = self.economy_service.get_user_orders(
            str(request.user.uuid),
            status_filter,
            page,
            page_size
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    def pay(self, request: Request, pk=None) -> Response:
        """
        支付订单
        
        POST /api/v1/orders/{id}/pay/
        """
        logger.info(f"支付订单请求: {request.user.username} -> {pk}")
        
        # 数据验证
        serializer = PaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.economy_service.process_payment(
            pk,
            serializer.validated_data
        )
        
        return APIResponse.success(
            data=result,
            message="支付成功"
        )


class TransactionViewSet(viewsets.ViewSet):
    """交易视图集"""
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.economy_service = EconomyService()
    def list(self, request: Request) -> Response:
        """
        获取交易历史
        
        GET /api/v1/transactions/?type=transfer&page=1&page_size=20
        """
        transaction_type = request.query_params.get('type')
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)
        
        # 调用业务服务
        result = self.economy_service.get_transaction_history(
            str(request.user.uuid),
            transaction_type,
            page,
            page_size
        )
        
        return APIResponse.success(data=result)


class EconomyStatsViewSet(viewsets.ViewSet):
    """经济统计视图集"""
    
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def user_stats(self, request: Request) -> Response:
        """
        获取用户经济统计
        
        GET /api/v1/economy-stats/user_stats/
        """
        from .models import Order, Transaction, Wallet
        from django.db.models import Sum, Count
        
        user = request.user
        
        # 统计订单信息
        order_stats = Order.objects.filter(user=user).aggregate(
            total_orders=Count('id'),
            completed_orders=Count('id', filter=models.Q(order_status='completed')),
            total_spent=Sum('total_amount', filter=models.Q(order_status__in=['paid', 'completed']))
        )
        
        # 统计交易信息
        transaction_stats = Transaction.objects.filter(user=user).aggregate(
            total_transactions=Count('id'),
            total_income=Sum('amount', filter=models.Q(amount__gt=0)),
            total_expense=Sum('amount', filter=models.Q(amount__lt=0))
        )
        
        # 统计钱包信息
        wallets = Wallet.objects.filter(user=user, is_active=True)
        wallet_balances = []
        for wallet in wallets:
            wallet_balances.append({
                'currency': wallet.currency.code,
                'balance': wallet.balance,
                'frozen_balance': wallet.frozen_balance
            })
        
        return APIResponse.success(data={
            'user_id': str(user.uuid),
            'orders': {
                'total_orders': order_stats['total_orders'] or 0,
                'completed_orders': order_stats['completed_orders'] or 0,
                'total_spent': order_stats['total_spent'] or 0
            },
            'transactions': {
                'total_transactions': transaction_stats['total_transactions'] or 0,
                'total_income': transaction_stats['total_income'] or 0,
                'total_expense': abs(transaction_stats['total_expense'] or 0)
            },
            'wallets': wallet_balances
        })
    
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    def platform_stats(self, request: Request) -> Response:
        """
        获取平台经济统计（需要管理员权限）
        
        GET /api/v1/economy-stats/platform_stats/
        """
        # 检查权限
        if not request.user.is_staff:
            return APIResponse.forbidden(message="需要管理员权限")
        
        from .models import Order, Transaction, Product, Currency
        from django.db.models import Sum, Count, Avg
        
        # 统计订单信息
        order_stats = Order.objects.aggregate(
            total_orders=Count('id'),
            completed_orders=Count('id', filter=models.Q(order_status='completed')),
            total_revenue=Sum('total_amount', filter=models.Q(order_status__in=['paid', 'completed'])),
            average_order_value=Avg('total_amount', filter=models.Q(order_status__in=['paid', 'completed']))
        )
        
        # 统计商品信息
        product_stats = Product.objects.filter(status='active').aggregate(
            total_products=Count('id'),
            total_sales=Sum('sales_count'),
            total_revenue=Sum('revenue')
        )
        
        # 统计用户钱包
        wallet_stats = Wallet.objects.filter(is_active=True).aggregate(
            total_wallets=Count('id'),
            total_balance=Sum('balance')
        )
        
        # 统计货币
        currency_stats = Currency.objects.filter(status='active').aggregate(
            total_currencies=Count('id')
        )
        
        return APIResponse.success(data={
            'orders': {
                'total_orders': order_stats['total_orders'] or 0,
                'completed_orders': order_stats['completed_orders'] or 0,
                'total_revenue': order_stats['total_revenue'] or 0,
                'average_order_value': order_stats['average_order_value'] or 0
            },
            'products': {
                'total_products': product_stats['total_products'] or 0,
                'total_sales': product_stats['total_sales'] or 0,
                'total_revenue': product_stats['total_revenue'] or 0
            },
            'wallets': {
                'total_wallets': wallet_stats['total_wallets'] or 0,
                'total_balance': wallet_stats['total_balance'] or 0
            },
            'currencies': {
                'total_currencies': currency_stats['total_currencies'] or 0
            }
        })
