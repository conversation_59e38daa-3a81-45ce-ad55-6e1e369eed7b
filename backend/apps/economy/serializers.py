"""
经济数据序列化器 - 数据转换和验证
职责：处理经济相关API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from decimal import Decimal
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import (
    Currency, Wallet, Product, Order, OrderItem, Transaction,
    PaymentMethod, Gift, Coupon, CouponUsage
)


class CurrencySerializer(serializers.ModelSerializer):
    """货币序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    
    class Meta:
        model = Currency
        fields = [
            'id', 'name', 'code', 'symbol', 'description',
            'icon_url', 'decimal_places', 'exchange_rate_to_usd',
            'total_supply', 'circulating_supply', 'status'
        ]
        read_only_fields = fields


class WalletSerializer(serializers.ModelSerializer):
    """钱包序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    currency = CurrencySerializer(read_only=True)
    total_balance = serializers.ReadOnlyField()
    
    class Meta:
        model = Wallet
        fields = [
            'id', 'currency', 'balance', 'frozen_balance', 'total_balance',
            'is_active', 'is_locked', 'created_at', 'updated_at'
        ]
        read_only_fields = fields


class ProductSerializer(serializers.ModelSerializer):
    """商品序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    currency = CurrencySerializer(read_only=True)
    is_available = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'description', 'product_type', 'sku', 'category', 'tags',
            'price', 'original_price', 'currency', 'discount_percentage',
            'stock_quantity', 'unlimited_stock', 'image_url', 'gallery_urls',
            'is_featured', 'is_limited', 'is_available', 'max_purchase_per_user',
            'sale_start_at', 'sale_end_at', 'sales_count', 'revenue',
            'created_at', 'updated_at'
        ]
        read_only_fields = fields


class OrderItemSerializer(serializers.ModelSerializer):
    """订单项序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    
    class Meta:
        model = OrderItem
        fields = [
            'id', 'product_name', 'product_sku', 'unit_price',
            'quantity', 'total_price'
        ]
        read_only_fields = fields


class OrderSerializer(serializers.ModelSerializer):
    """订单序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    currency = CurrencySerializer(read_only=True)
    items = OrderItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'order_status', 'subtotal',
            'discount_amount', 'tax_amount', 'total_amount', 'currency',
            'payment_method', 'items', 'notes', 'paid_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = fields


class TransactionSerializer(serializers.ModelSerializer):
    """交易序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    currency = CurrencySerializer(read_only=True)
    related_order_number = serializers.CharField(
        source='related_order.order_number',
        read_only=True
    )
    
    class Meta:
        model = Transaction
        fields = [
            'id', 'transaction_id', 'transaction_type', 'transaction_status',
            'amount', 'currency', 'fee', 'balance_before', 'balance_after',
            'description', 'reference', 'related_order_number', 'created_at'
        ]
        read_only_fields = fields


class OrderCreateSerializer(serializers.Serializer):
    """订单创建序列化器"""
    
    items = serializers.ListField(
        child=serializers.DictField(),
        help_text="订单项列表"
    )
    payment_method = serializers.ChoiceField(
        choices=Order.PAYMENT_METHOD_CHOICES,
        default='wallet',
        help_text="支付方式"
    )
    coupon_code = serializers.CharField(
        required=False,
        max_length=50,
        help_text="优惠券代码"
    )
    notes = serializers.CharField(
        required=False,
        max_length=500,
        help_text="订单备注"
    )
    
    def validate_items(self, value):
        """验证订单项"""
        if not value or len(value) == 0:
            raise serializers.ValidationError("订单必须包含至少一个商品")
        
        for item in value:
            if 'product_id' not in item or 'quantity' not in item:
                raise serializers.ValidationError("订单项必须包含product_id和quantity")
            
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise serializers.ValidationError("商品数量必须大于0")
                item['quantity'] = quantity
            except (ValueError, TypeError):
                raise serializers.ValidationError("商品数量必须为正整数")
        
        return value
    
    def validate_coupon_code(self, value):
        """验证优惠券代码"""
        if value:
            try:
                coupon = Coupon.objects.get(code=value, status='active')
                if not coupon.is_valid:
                    raise serializers.ValidationError("优惠券无效或已过期")
            except Coupon.DoesNotExist:
                raise serializers.ValidationError("优惠券不存在")
        
        return value


class PaymentSerializer(serializers.Serializer):
    """支付序列化器"""
    
    payment_method = serializers.ChoiceField(
        choices=Order.PAYMENT_METHOD_CHOICES,
        help_text="支付方式"
    )
    external_transaction_id = serializers.CharField(
        required=False,
        max_length=100,
        help_text="外部交易ID（外部支付时使用）"
    )
    payment_data = serializers.JSONField(
        required=False,
        help_text="支付相关数据"
    )
    
    def validate(self, attrs):
        """交叉验证"""
        payment_method = attrs['payment_method']
        
        if payment_method == 'external' and not attrs.get('external_transaction_id'):
            raise serializers.ValidationError({
                'external_transaction_id': '外部支付必须提供交易ID'
            })
        
        return attrs


class TransferSerializer(serializers.Serializer):
    """转账序列化器"""
    
    to_user_id = serializers.CharField(
        help_text="接收者用户ID"
    )
    amount = serializers.DecimalField(
        max_digits=20,
        decimal_places=8,
        min_value=Decimal('0.01'),
        help_text="转账金额"
    )
    currency_code = serializers.CharField(
        max_length=10,
        help_text="货币代码"
    )
    description = serializers.CharField(
        required=False,
        max_length=200,
        help_text="转账描述"
    )
    
    def validate_to_user_id(self, value):
        """验证接收者用户ID"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            User.objects.get(uuid=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("接收者用户不存在")
        
        return value
    
    def validate_currency_code(self, value):
        """验证货币代码"""
        try:
            Currency.objects.get(code=value, status='active')
        except Currency.DoesNotExist:
            raise serializers.ValidationError("货币不存在或已禁用")
        
        return value


class GiftSerializer(serializers.ModelSerializer):
    """礼品序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    sender = serializers.SerializerMethodField()
    recipient = serializers.SerializerMethodField()
    product = ProductSerializer(read_only=True)
    currency = CurrencySerializer(read_only=True)
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Gift
        fields = [
            'id', 'sender', 'recipient', 'gift_type', 'gift_status',
            'product', 'currency_amount', 'currency', 'message',
            'is_expired', 'expires_at', 'sent_at', 'received_at',
            'created_at'
        ]
        read_only_fields = fields
    
    def get_sender(self, obj):
        """获取发送者信息"""
        return {
            'id': str(obj.sender.uuid),
            'username': obj.sender.username,
            'display_name': obj.sender.display_name
        }
    
    def get_recipient(self, obj):
        """获取接收者信息"""
        return {
            'id': str(obj.recipient.uuid),
            'username': obj.recipient.username,
            'display_name': obj.recipient.display_name
        }


class CouponSerializer(serializers.ModelSerializer):
    """优惠券序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    currency = CurrencySerializer(read_only=True)
    applicable_products = ProductSerializer(many=True, read_only=True)
    is_valid = serializers.ReadOnlyField()
    
    class Meta:
        model = Coupon
        fields = [
            'id', 'name', 'code', 'description', 'coupon_type',
            'discount_value', 'minimum_amount', 'currency',
            'applicable_products', 'applicable_categories',
            'usage_limit', 'usage_limit_per_user', 'used_count',
            'is_valid', 'valid_from', 'valid_until', 'status'
        ]
        read_only_fields = fields


class PaymentMethodSerializer(serializers.ModelSerializer):
    """支付方式序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    
    class Meta:
        model = PaymentMethod
        fields = [
            'id', 'method_type', 'display_name', 'is_default',
            'is_verified', 'status', 'created_at'
        ]
        read_only_fields = ['id', 'is_verified', 'created_at']
        extra_kwargs = {
            'account_info': {'write_only': True}  # 敏感信息只写不读
        }


class EconomyStatsSerializer(serializers.Serializer):
    """经济统计序列化器"""
    
    user_id = serializers.CharField(read_only=True)
    orders = serializers.DictField(read_only=True)
    transactions = serializers.DictField(read_only=True)
    wallets = serializers.ListField(read_only=True)


class PlatformStatsSerializer(serializers.Serializer):
    """平台统计序列化器"""
    
    orders = serializers.DictField(read_only=True)
    products = serializers.DictField(read_only=True)
    wallets = serializers.DictField(read_only=True)
    currencies = serializers.DictField(read_only=True)
