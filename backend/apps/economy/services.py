"""
经济业务服务 - 高内聚的虚拟经济业务逻辑
职责：处理支付处理、商品管理、交易验证、钱包管理等核心业务逻辑
"""

import logging
import uuid
from typing import Dict, Any, Optional, List, Tuple
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Sum, Count, F

from apps.core.events import event_bus, DomainEvent
from apps.core.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    ForbiddenException,
    ConflictException
)
from apps.core.dependencies import inject
from .models import (
    Currency, Wallet, Product, Order, OrderItem, Transaction,
    PaymentMethod, Gift, Coupon, CouponUsage, EconomySettings
)

logger = logging.getLogger(__name__)


class EconomyService:
    """
    经济业务服务 - 高内聚的经济相关业务逻辑
    
    职责：
    1. 钱包管理和余额操作
    2. 商品管理和订单处理
    3. 支付处理和交易验证
    4. 优惠券和礼品系统
    5. 经济统计和报告
    """
    
    def get_user_wallet(self, user_id: str, currency_code: str = None) -> Dict[str, Any]:
        """
        获取用户钱包信息
        
        Args:
            user_id: 用户ID
            currency_code: 货币代码，默认为系统默认货币
            
        Returns:
            Dict: 钱包信息
        """
        user = self._get_user_by_id(user_id)
        
        # 获取货币
        if currency_code:
            currency = self._get_currency_by_code(currency_code)
        else:
            currency = self._get_default_currency()
        
        # 获取或创建钱包
        wallet, created = Wallet.objects.get_or_create(
            user=user,
            currency=currency,
            defaults={
                'balance': Decimal('0'),
                'frozen_balance': Decimal('0'),
                'is_active': True
            }
        )
        
        if created:
            # 新钱包，给予初始余额
            settings = self._get_economy_settings()
            if settings.initial_balance > 0:
                wallet.balance = settings.initial_balance
                wallet.save()
                
                # 记录初始余额交易
                self._create_transaction(
                    user=user,
                    transaction_type='reward',
                    amount=settings.initial_balance,
                    currency=currency,
                    description='新用户初始余额',
                    balance_before=Decimal('0'),
                    balance_after=settings.initial_balance
                )
        
        return {
            'wallet_id': str(wallet.uuid),
            'currency': {
                'code': currency.code,
                'name': currency.name,
                'symbol': currency.symbol
            },
            'balance': wallet.balance,
            'frozen_balance': wallet.frozen_balance,
            'total_balance': wallet.total_balance,
            'is_active': wallet.is_active,
            'is_locked': wallet.is_locked
        }
    
    @transaction.atomic
    def transfer_funds(self, from_user_id: str, to_user_id: str, 
                      amount: Decimal, currency_code: str, 
                      description: str = '') -> Dict[str, Any]:
        """
        转账
        
        Args:
            from_user_id: 转出用户ID
            to_user_id: 转入用户ID
            amount: 转账金额
            currency_code: 货币代码
            description: 转账描述
            
        Returns:
            Dict: 转账结果
        """
        logger.info(f"转账: {from_user_id} -> {to_user_id}, 金额: {amount} {currency_code}")
        
        # 验证参数
        if amount <= 0:
            raise ValidationException("转账金额必须大于0")
        
        if from_user_id == to_user_id:
            raise ValidationException("不能向自己转账")
        
        # 获取用户和货币
        from_user = self._get_user_by_id(from_user_id)
        to_user = self._get_user_by_id(to_user_id)
        currency = self._get_currency_by_code(currency_code)
        
        # 获取钱包
        from_wallet = self._get_user_wallet_entity(from_user, currency)
        to_wallet, _ = Wallet.objects.get_or_create(
            user=to_user,
            currency=currency,
            defaults={'balance': Decimal('0'), 'frozen_balance': Decimal('0')}
        )
        
        # 检查转出钱包余额
        if not from_wallet.can_spend(amount):
            raise ValidationException("余额不足或钱包被锁定")
        
        # 检查转账限额
        self._check_transaction_limits(from_user, amount, currency)
        
        # 计算手续费
        settings = self._get_economy_settings()
        fee = amount * settings.transaction_fee_rate
        total_deduct = amount + fee
        
        if not from_wallet.can_spend(total_deduct):
            raise ValidationException(f"余额不足，需要 {total_deduct}（包含手续费 {fee}）")
        
        # 执行转账
        from_balance_before = from_wallet.balance
        to_balance_before = to_wallet.balance
        
        from_wallet.balance -= total_deduct
        to_wallet.balance += amount
        
        from_wallet.save()
        to_wallet.save()
        
        # 生成交易ID
        transfer_id = self._generate_transaction_id()
        
        # 记录转出交易
        from_transaction = self._create_transaction(
            user=from_user,
            transaction_type='transfer',
            amount=-total_deduct,
            currency=currency,
            description=f"转账给 {to_user.username}: {description}",
            balance_before=from_balance_before,
            balance_after=from_wallet.balance,
            fee=fee,
            transaction_id=f"{transfer_id}_out"
        )
        
        # 记录转入交易
        to_transaction = self._create_transaction(
            user=to_user,
            transaction_type='transfer',
            amount=amount,
            currency=currency,
            description=f"来自 {from_user.username} 的转账: {description}",
            balance_before=to_balance_before,
            balance_after=to_wallet.balance,
            transaction_id=f"{transfer_id}_in"
        )
        
        # 发布转账事件
        self._publish_transfer_event(from_user, to_user, amount, currency, transfer_id)
        
        logger.info(f"转账成功: {transfer_id}")
        
        return {
            'transfer_id': transfer_id,
            'from_user_id': str(from_user.uuid),
            'to_user_id': str(to_user.uuid),
            'amount': amount,
            'fee': fee,
            'currency': currency.code,
            'from_balance': from_wallet.balance,
            'to_balance': to_wallet.balance,
            'completed_at': timezone.now().isoformat()
        }
    
    @transaction.atomic
    def create_order(self, user_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建订单
        
        Args:
            user_id: 用户ID
            order_data: 订单数据
            
        Returns:
            Dict: 创建的订单信息
        """
        logger.info(f"创建订单: {user_id}")
        
        # 验证用户
        user = self._get_user_by_id(user_id)
        
        # 验证订单数据
        self._validate_order_data(order_data)
        
        # 获取商品和验证库存
        items_data = order_data['items']
        products_info = self._validate_and_get_products(items_data)
        
        # 计算订单金额
        currency = products_info[0]['product'].currency  # 假设所有商品使用同一货币
        subtotal = sum(info['total_price'] for info in products_info)
        
        # 应用优惠券
        discount_amount = Decimal('0')
        coupon = None
        if order_data.get('coupon_code'):
            coupon, discount_amount = self._apply_coupon(
                order_data['coupon_code'], 
                user, 
                subtotal, 
                [info['product'] for info in products_info]
            )
        
        # 计算总金额
        total_amount = subtotal - discount_amount
        
        # 创建订单
        order = Order.objects.create(
            user=user,
            order_number=self._generate_order_number(),
            subtotal=subtotal,
            discount_amount=discount_amount,
            total_amount=total_amount,
            currency=currency,
            payment_method=order_data.get('payment_method', 'wallet'),
            notes=order_data.get('notes', ''),
            created_by=user,
            updated_by=user
        )
        
        # 创建订单项
        for info in products_info:
            OrderItem.objects.create(
                order=order,
                product=info['product'],
                product_name=info['product'].name,
                product_sku=info['product'].sku,
                unit_price=info['product'].price,
                quantity=info['quantity']
            )
        
        # 记录优惠券使用
        if coupon:
            CouponUsage.objects.create(
                coupon=coupon,
                user=user,
                order=order,
                discount_amount=discount_amount
            )
            coupon.used_count += 1
            coupon.save()
        
        # 发布订单创建事件
        self._publish_order_created_event(order)
        
        logger.info(f"订单创建成功: {order.order_number}")
        
        return {
            'order_id': str(order.uuid),
            'order_number': order.order_number,
            'subtotal': subtotal,
            'discount_amount': discount_amount,
            'total_amount': total_amount,
            'currency': currency.code,
            'status': order.order_status,
            'created_at': order.created_at.isoformat()
        }
    
    @transaction.atomic
    def process_payment(self, order_id: str, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理支付
        
        Args:
            order_id: 订单ID
            payment_data: 支付数据
            
        Returns:
            Dict: 支付结果
        """
        logger.info(f"处理支付: {order_id}")
        
        # 获取订单
        order = self._get_order_by_id(order_id)
        
        # 检查订单状态
        if order.order_status != 'pending':
            raise ValidationException(f"订单状态异常: {order.order_status}")
        
        # 根据支付方式处理
        payment_method = payment_data.get('payment_method', order.payment_method)
        
        if payment_method == 'wallet':
            return self._process_wallet_payment(order, payment_data)
        elif payment_method == 'external':
            return self._process_external_payment(order, payment_data)
        else:
            raise ValidationException(f"不支持的支付方式: {payment_method}")
    
    def get_products(self, filters: Dict[str, Any] = None, 
                    page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取商品列表
        
        Args:
            filters: 过滤条件
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 商品列表
        """
        filters = filters or {}
        
        # 构建查询
        queryset = Product.objects.filter(status='active').select_related('currency')
        
        # 应用过滤器
        if filters.get('category'):
            queryset = queryset.filter(category=filters['category'])
        
        if filters.get('product_type'):
            queryset = queryset.filter(product_type=filters['product_type'])
        
        if filters.get('is_featured'):
            queryset = queryset.filter(is_featured=True)
        
        if filters.get('currency_code'):
            queryset = queryset.filter(currency__code=filters['currency_code'])
        
        # 只显示可购买的商品
        queryset = queryset.filter(
            Q(sale_start_at__isnull=True) | Q(sale_start_at__lte=timezone.now()),
            Q(sale_end_at__isnull=True) | Q(sale_end_at__gte=timezone.now())
        )
        
        # 排序
        order_by = filters.get('order_by', '-created_at')
        queryset = queryset.order_by(order_by)
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化商品
        products = []
        for product in page_obj.object_list:
            product_data = self._serialize_product(product)
            products.append(product_data)
        
        return {
            'products': products,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
    
    def get_user_orders(self, user_id: str, status: str = None,
                       page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取用户订单列表
        
        Args:
            user_id: 用户ID
            status: 订单状态过滤
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 订单列表
        """
        user = self._get_user_by_id(user_id)
        
        # 构建查询
        queryset = Order.objects.filter(user=user).select_related('currency')
        
        if status:
            queryset = queryset.filter(order_status=status)
        
        queryset = queryset.order_by('-created_at')
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化订单
        orders = []
        for order in page_obj.object_list:
            order_data = self._serialize_order(order)
            orders.append(order_data)
        
        return {
            'orders': orders,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
    
    def get_transaction_history(self, user_id: str, transaction_type: str = None,
                              page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取交易历史
        
        Args:
            user_id: 用户ID
            transaction_type: 交易类型过滤
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 交易历史
        """
        user = self._get_user_by_id(user_id)
        
        # 构建查询
        queryset = Transaction.objects.filter(user=user).select_related('currency')
        
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)
        
        queryset = queryset.order_by('-created_at')
        
        # 分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化交易
        transactions = []
        for transaction in page_obj.object_list:
            transaction_data = self._serialize_transaction(transaction)
            transactions.append(transaction_data)
        
        return {
            'transactions': transactions,
            'pagination': {
                'page': page_obj.number,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_items': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }

    # 私有方法

    def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            return User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")

    def _get_currency_by_code(self, code: str):
        """根据代码获取货币"""
        try:
            return Currency.objects.get(code=code, status='active')
        except Currency.DoesNotExist:
            raise ResourceNotFoundException(f"货币不存在: {code}", "CURRENCY")

    def _get_default_currency(self):
        """获取默认货币"""
        settings = self._get_economy_settings()
        return settings.default_currency

    def _get_economy_settings(self):
        """获取经济系统设置"""
        try:
            return EconomySettings.objects.first()
        except EconomySettings.DoesNotExist:
            # 创建默认设置
            default_currency, _ = Currency.objects.get_or_create(
                code='COIN',
                defaults={
                    'name': 'SOIC Coin',
                    'symbol': '🪙',
                    'description': '平台默认虚拟货币'
                }
            )

            return EconomySettings.objects.create(
                default_currency=default_currency
            )

    def _get_user_wallet_entity(self, user, currency):
        """获取用户钱包实体"""
        try:
            return Wallet.objects.get(user=user, currency=currency)
        except Wallet.DoesNotExist:
            raise ResourceNotFoundException("钱包不存在", "WALLET")

    def _get_order_by_id(self, order_id: str):
        """根据ID获取订单"""
        try:
            return Order.objects.get(uuid=order_id)
        except Order.DoesNotExist:
            raise ResourceNotFoundException(f"订单不存在: {order_id}", "ORDER")

    def _check_transaction_limits(self, user, amount: Decimal, currency):
        """检查交易限额"""
        settings = self._get_economy_settings()

        # 检查单笔交易限额
        if amount < settings.minimum_transaction_amount:
            raise ValidationException(f"交易金额不能低于 {settings.minimum_transaction_amount}")

        if amount > settings.maximum_transaction_amount:
            raise ValidationException(f"交易金额不能超过 {settings.maximum_transaction_amount}")

        # 检查日交易限额
        today = timezone.now().date()
        daily_total = Transaction.objects.filter(
            user=user,
            currency=currency,
            created_at__date=today,
            transaction_status='completed'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        if daily_total + amount > settings.daily_transaction_limit:
            raise ValidationException(f"超过日交易限额 {settings.daily_transaction_limit}")

    def _generate_transaction_id(self) -> str:
        """生成交易ID"""
        return f"TXN_{timezone.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8].upper()}"

    def _generate_order_number(self) -> str:
        """生成订单号"""
        return f"ORD_{timezone.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8].upper()}"

    def _create_transaction(self, user, transaction_type: str, amount: Decimal,
                          currency, description: str, balance_before: Decimal,
                          balance_after: Decimal, fee: Decimal = Decimal('0'),
                          transaction_id: str = None, related_order=None) -> Transaction:
        """创建交易记录"""
        if not transaction_id:
            transaction_id = self._generate_transaction_id()

        return Transaction.objects.create(
            user=user,
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            amount=amount,
            currency=currency,
            fee=fee,
            balance_before=balance_before,
            balance_after=balance_after,
            description=description,
            related_order=related_order,
            transaction_status='completed'
        )

    def _validate_order_data(self, data: Dict[str, Any]):
        """验证订单数据"""
        required_fields = ['items']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )

        items = data['items']
        if not items or len(items) == 0:
            raise ValidationException("订单必须包含至少一个商品")

        for item in items:
            if not item.get('product_id') or not item.get('quantity'):
                raise ValidationException("商品项必须包含product_id和quantity")

            if item['quantity'] <= 0:
                raise ValidationException("商品数量必须大于0")

    def _validate_and_get_products(self, items_data: List[Dict]) -> List[Dict]:
        """验证并获取商品信息"""
        products_info = []

        for item_data in items_data:
            product_id = item_data['product_id']
            quantity = item_data['quantity']

            try:
                product = Product.objects.get(uuid=product_id, status='active')
            except Product.DoesNotExist:
                raise ResourceNotFoundException(f"商品不存在: {product_id}", "PRODUCT")

            # 检查商品是否可购买
            if not product.is_available:
                raise ValidationException(f"商品 {product.name} 不可购买")

            # 检查库存
            if not product.unlimited_stock and product.stock_quantity is not None:
                if product.stock_quantity < quantity:
                    raise ValidationException(f"商品 {product.name} 库存不足")

            products_info.append({
                'product': product,
                'quantity': quantity,
                'unit_price': product.price,
                'total_price': product.price * quantity
            })

        return products_info

    def _apply_coupon(self, coupon_code: str, user, order_amount: Decimal,
                     products: List) -> Tuple[Coupon, Decimal]:
        """应用优惠券"""
        try:
            coupon = Coupon.objects.get(code=coupon_code, status='active')
        except Coupon.DoesNotExist:
            raise ResourceNotFoundException(f"优惠券不存在: {coupon_code}", "COUPON")

        # 检查优惠券是否可用
        can_use, reason = coupon.can_use(user, order_amount, products)
        if not can_use:
            raise ValidationException(reason)

        # 计算折扣金额
        if coupon.coupon_type == 'percentage':
            discount_amount = order_amount * (coupon.discount_value / 100)
        elif coupon.coupon_type == 'fixed_amount':
            discount_amount = min(coupon.discount_value, order_amount)
        else:
            discount_amount = Decimal('0')  # 其他类型暂不支持

        return coupon, discount_amount

    def _process_wallet_payment(self, order: Order, payment_data: Dict) -> Dict[str, Any]:
        """处理钱包支付"""
        user = order.user
        currency = order.currency
        amount = order.total_amount

        # 获取用户钱包
        wallet = self._get_user_wallet_entity(user, currency)

        # 检查余额
        if not wallet.can_spend(amount):
            raise ValidationException("钱包余额不足")

        # 扣除余额
        balance_before = wallet.balance
        wallet.balance -= amount
        wallet.save()

        # 更新订单状态
        order.order_status = 'paid'
        order.paid_at = timezone.now()
        order.save()

        # 记录交易
        transaction = self._create_transaction(
            user=user,
            transaction_type='purchase',
            amount=-amount,
            currency=currency,
            description=f"购买订单 {order.order_number}",
            balance_before=balance_before,
            balance_after=wallet.balance,
            related_order=order
        )

        # 更新商品销售统计
        for item in order.items.all():
            product = item.product
            product.sales_count += item.quantity
            product.revenue += item.total_price

            # 扣除库存
            if not product.unlimited_stock and product.stock_quantity is not None:
                product.stock_quantity -= item.quantity

            product.save()

        # 发布支付成功事件
        self._publish_payment_completed_event(order, transaction)

        return {
            'payment_id': str(transaction.uuid),
            'order_id': str(order.uuid),
            'amount': amount,
            'currency': currency.code,
            'payment_method': 'wallet',
            'status': 'completed',
            'paid_at': order.paid_at.isoformat()
        }

    def _process_external_payment(self, order: Order, payment_data: Dict) -> Dict[str, Any]:
        """处理外部支付"""
        # 这里应该集成第三方支付接口
        # 简化实现，直接标记为已支付

        order.order_status = 'paid'
        order.paid_at = timezone.now()
        order.save()

        # 记录交易
        transaction = self._create_transaction(
            user=order.user,
            transaction_type='purchase',
            amount=order.total_amount,
            currency=order.currency,
            description=f"外部支付订单 {order.order_number}",
            balance_before=Decimal('0'),  # 外部支付不影响钱包余额
            balance_after=Decimal('0'),
            related_order=order,
            transaction_id=payment_data.get('external_transaction_id')
        )

        # 发布支付成功事件
        self._publish_payment_completed_event(order, transaction)

        return {
            'payment_id': str(transaction.uuid),
            'order_id': str(order.uuid),
            'amount': order.total_amount,
            'currency': order.currency.code,
            'payment_method': 'external',
            'status': 'completed',
            'paid_at': order.paid_at.isoformat()
        }

    def _serialize_product(self, product: Product) -> Dict[str, Any]:
        """序列化商品"""
        return {
            'id': str(product.uuid),
            'name': product.name,
            'description': product.description,
            'product_type': product.product_type,
            'sku': product.sku,
            'category': product.category,
            'tags': product.tags,
            'price': product.price,
            'original_price': product.original_price,
            'currency': {
                'code': product.currency.code,
                'symbol': product.currency.symbol
            },
            'discount_percentage': product.discount_percentage,
            'stock_quantity': product.stock_quantity,
            'unlimited_stock': product.unlimited_stock,
            'image_url': product.image_url,
            'gallery_urls': product.gallery_urls,
            'is_featured': product.is_featured,
            'is_limited': product.is_limited,
            'is_available': product.is_available,
            'sales_count': product.sales_count,
            'sale_start_at': product.sale_start_at.isoformat() if product.sale_start_at else None,
            'sale_end_at': product.sale_end_at.isoformat() if product.sale_end_at else None,
            'created_at': product.created_at.isoformat()
        }

    def _serialize_order(self, order: Order) -> Dict[str, Any]:
        """序列化订单"""
        return {
            'id': str(order.uuid),
            'order_number': order.order_number,
            'order_status': order.order_status,
            'subtotal': order.subtotal,
            'discount_amount': order.discount_amount,
            'total_amount': order.total_amount,
            'currency': {
                'code': order.currency.code,
                'symbol': order.currency.symbol
            },
            'payment_method': order.payment_method,
            'items': [
                {
                    'product_name': item.product_name,
                    'product_sku': item.product_sku,
                    'unit_price': item.unit_price,
                    'quantity': item.quantity,
                    'total_price': item.total_price
                }
                for item in order.items.all()
            ],
            'notes': order.notes,
            'paid_at': order.paid_at.isoformat() if order.paid_at else None,
            'created_at': order.created_at.isoformat()
        }

    def _serialize_transaction(self, transaction: Transaction) -> Dict[str, Any]:
        """序列化交易"""
        return {
            'id': str(transaction.uuid),
            'transaction_id': transaction.transaction_id,
            'transaction_type': transaction.transaction_type,
            'transaction_status': transaction.transaction_status,
            'amount': transaction.amount,
            'currency': {
                'code': transaction.currency.code,
                'symbol': transaction.currency.symbol
            },
            'fee': transaction.fee,
            'balance_before': transaction.balance_before,
            'balance_after': transaction.balance_after,
            'description': transaction.description,
            'reference': transaction.reference,
            'related_order_number': transaction.related_order.order_number if transaction.related_order else None,
            'created_at': transaction.created_at.isoformat()
        }

    # 事件发布方法

    def _publish_transfer_event(self, from_user, to_user, amount: Decimal,
                               currency, transfer_id: str):
        """发布转账事件"""
        event = DomainEvent(
            event_type='economy.transfer_completed',
            aggregate_id=transfer_id,
            data={
                'transfer_id': transfer_id,
                'from_user_id': str(from_user.uuid),
                'from_username': from_user.username,
                'to_user_id': str(to_user.uuid),
                'to_username': to_user.username,
                'amount': str(amount),
                'currency_code': currency.code,
                'completed_at': timezone.now().isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_order_created_event(self, order: Order):
        """发布订单创建事件"""
        event = DomainEvent(
            event_type='economy.order_created',
            aggregate_id=str(order.uuid),
            data={
                'order_id': str(order.uuid),
                'order_number': order.order_number,
                'user_id': str(order.user.uuid),
                'username': order.user.username,
                'total_amount': str(order.total_amount),
                'currency_code': order.currency.code,
                'item_count': order.items.count(),
                'created_at': order.created_at.isoformat()
            }
        )
        event_bus.publish(event)

    def _publish_payment_completed_event(self, order: Order, transaction: Transaction):
        """发布支付完成事件"""
        event = DomainEvent(
            event_type='economy.payment_completed',
            aggregate_id=str(order.uuid),
            data={
                'order_id': str(order.uuid),
                'order_number': order.order_number,
                'transaction_id': transaction.transaction_id,
                'user_id': str(order.user.uuid),
                'username': order.user.username,
                'amount': str(order.total_amount),
                'currency_code': order.currency.code,
                'payment_method': order.payment_method,
                'paid_at': order.paid_at.isoformat()
            }
        )
        event_bus.publish(event)
