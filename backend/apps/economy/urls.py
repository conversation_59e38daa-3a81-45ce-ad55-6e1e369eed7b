"""
经济URL路由配置 - 独立的经济域路由
职责：定义经济相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    WalletViewSet,
    ProductViewSet,
    OrderViewSet,
    TransactionViewSet,
    EconomyStatsViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'wallets', WalletViewSet, basename='wallet')
router.register(r'products', ProductViewSet, basename='product')
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'transactions', TransactionViewSet, basename='transaction')
router.register(r'economy-stats', EconomyStatsViewSet, basename='economystats')

# URL模式
urlpatterns = [
    # API路由
    path('api/v1/', include(router.urls)),
]

# 应用命名空间
app_name = 'economy'
