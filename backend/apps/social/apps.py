"""
社交应用配置 - 社交域应用配置
职责：配置社交应用，注册信号处理器，初始化服务
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class SocialConfig(AppConfig):
    """社交应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.social'
    verbose_name = '社交关系管理'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入信号处理器
        from . import signals
        
        # 注册依赖服务
        self.register_services()
        
        # 注册事件处理器
        self.register_event_handlers()
        
        logger.info("社交应用初始化完成")
    
    def register_services(self):
        """注册依赖服务"""
        try:
            from apps.core.dependencies import container
            from .services import SocialService
            
            # 注册社交服务为单例
            container.register_singleton(SocialService, SocialService)
            
            logger.info("社交服务注册完成")
            
        except Exception as e:
            logger.error(f"社交服务注册失败: {e}")
    
    def register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 导入信号处理器（这会自动注册Django信号）
            from . import signals

            logger.info("社交事件处理器注册完成")

        except Exception as e:
            logger.error(f"社交事件处理器注册失败: {e}")