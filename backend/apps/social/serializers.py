"""
社交数据序列化器 - 数据转换和验证
职责：处理社交相关API数据的序列化和反序列化，数据验证
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import (
    Friendship, Follow, Group, GroupMember, GroupInvitation,
    UserRecommendation, SocialActivity, BlockedUser
)


class FriendRequestSerializer(serializers.Serializer):
    """好友请求序列化器"""
    
    addressee_id = serializers.CharField(
        help_text="接收者用户ID"
    )
    message = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="请求消息，最多500个字符"
    )
    
    def validate_addressee_id(self, value):
        """验证接收者用户ID"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            User.objects.get(uuid=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")
        
        return value


class FriendshipResponseSerializer(serializers.Serializer):
    """好友请求响应序列化器"""
    
    accept = serializers.BooleanField(
        help_text="是否接受好友请求"
    )


class FriendshipSerializer(serializers.ModelSerializer):
    """好友关系序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    requester = serializers.SerializerMethodField()
    addressee = serializers.SerializerMethodField()
    
    class Meta:
        model = Friendship
        fields = [
            'id', 'requester', 'addressee', 'status',
            'is_close_friend', 'is_favorite',
            'interaction_count', 'last_interaction_at',
            'requested_at', 'responded_at'
        ]
        read_only_fields = fields
    
    def get_requester(self, obj):
        """获取请求者信息"""
        return {
            'id': str(obj.requester.uuid),
            'username': obj.requester.username,
            'display_name': obj.requester.display_name
        }
    
    def get_addressee(self, obj):
        """获取接收者信息"""
        return {
            'id': str(obj.addressee.uuid),
            'username': obj.addressee.username,
            'display_name': obj.addressee.display_name
        }


class FollowSerializer(serializers.Serializer):
    """关注序列化器"""
    
    following_id = serializers.CharField(
        help_text="被关注用户ID"
    )
    
    def validate_following_id(self, value):
        """验证被关注用户ID"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            User.objects.get(uuid=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")
        
        return value


class FollowDetailSerializer(serializers.ModelSerializer):
    """关注详情序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    follower = serializers.SerializerMethodField()
    following = serializers.SerializerMethodField()
    
    class Meta:
        model = Follow
        fields = [
            'id', 'follower', 'following',
            'notify_posts', 'notify_activities',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'follower', 'following', 'created_at', 'updated_at']
    
    def get_follower(self, obj):
        """获取关注者信息"""
        return {
            'id': str(obj.follower.uuid),
            'username': obj.follower.username,
            'display_name': obj.follower.display_name
        }
    
    def get_following(self, obj):
        """获取被关注者信息"""
        return {
            'id': str(obj.following.uuid),
            'username': obj.following.username,
            'display_name': obj.following.display_name
        }


class GroupCreateSerializer(serializers.Serializer):
    """群组创建序列化器"""
    
    name = serializers.CharField(
        max_length=100,
        min_length=2,
        help_text="群组名称，2-100个字符"
    )
    description = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="群组描述，最多1000个字符"
    )
    group_type = serializers.ChoiceField(
        choices=Group.GROUP_TYPE_CHOICES,
        default='public',
        help_text="群组类型"
    )
    max_members = serializers.IntegerField(
        min_value=2,
        max_value=10000,
        default=500,
        help_text="最大成员数，2-10000"
    )
    
    # 群组设置
    allow_member_invite = serializers.BooleanField(default=True)
    require_approval = serializers.BooleanField(default=False)
    allow_anonymous_posts = serializers.BooleanField(default=False)
    
    # 媒体资源
    avatar_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="群组头像URL"
    )
    cover_url = serializers.URLField(
        required=False,
        allow_blank=True,
        help_text="群组封面URL"
    )
    
    # 标签
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        help_text="群组标签列表"
    )
    
    def validate_name(self, value):
        """验证群组名称"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("群组名称不能为空")
        
        # 检查是否包含敏感词
        sensitive_words = ['admin', 'system', 'test']
        if any(word in value.lower() for word in sensitive_words):
            raise serializers.ValidationError("群组名称包含敏感词")
        
        return value.strip()
    
    def validate_tags(self, value):
        """验证标签"""
        if len(value) > 10:
            raise serializers.ValidationError("标签数量不能超过10个")
        
        for tag in value:
            if len(tag.strip()) < 1:
                raise serializers.ValidationError("标签不能为空")
        
        return [tag.strip() for tag in value]


class GroupSerializer(serializers.ModelSerializer):
    """群组序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    owner = serializers.SerializerMethodField()
    member_rate = serializers.ReadOnlyField()
    is_full = serializers.ReadOnlyField()
    user_membership = serializers.SerializerMethodField()
    
    class Meta:
        model = Group
        fields = [
            'id', 'name', 'description', 'owner',
            'group_type', 'status',
            'max_members', 'current_members', 'member_rate', 'is_full',
            'allow_member_invite', 'require_approval', 'allow_anonymous_posts',
            'avatar_url', 'cover_url',
            'post_count', 'activity_count',
            'tags', 'user_membership',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'owner', 'current_members', 'member_rate', 'is_full',
            'post_count', 'activity_count', 'user_membership',
            'created_at', 'updated_at'
        ]
    
    def get_owner(self, obj):
        """获取群主信息"""
        return {
            'id': str(obj.owner.uuid),
            'username': obj.owner.username,
            'display_name': obj.owner.display_name
        }
    
    def get_user_membership(self, obj):
        """获取当前用户的成员信息"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                member = GroupMember.objects.get(
                    group=obj,
                    user=request.user,
                    status='active'
                )
                return GroupMemberSerializer(member).data
            except GroupMember.DoesNotExist:
                pass
        return None


class GroupMemberSerializer(serializers.ModelSerializer):
    """群组成员序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user = serializers.SerializerMethodField()
    group_name = serializers.CharField(source='group.name', read_only=True)
    invited_by = serializers.SerializerMethodField()
    
    class Meta:
        model = GroupMember
        fields = [
            'id', 'user', 'group_name', 'role', 'status',
            'can_post', 'can_comment', 'can_invite', 'can_moderate',
            'post_count', 'comment_count', 'last_active_at',
            'invited_by', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'group_name', 'post_count', 'comment_count',
            'last_active_at', 'invited_by', 'created_at', 'updated_at'
        ]
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj.user.uuid),
            'username': obj.user.username,
            'display_name': obj.user.display_name
        }
    
    def get_invited_by(self, obj):
        """获取邀请者信息"""
        if obj.invited_by:
            return {
                'id': str(obj.invited_by.uuid),
                'username': obj.invited_by.username,
                'display_name': obj.invited_by.display_name
            }
        return None


class GroupJoinSerializer(serializers.Serializer):
    """加入群组序列化器"""
    
    message = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="申请消息（如果需要审批）"
    )


class GroupInvitationSerializer(serializers.ModelSerializer):
    """群组邀请序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    group = serializers.SerializerMethodField()
    inviter = serializers.SerializerMethodField()
    invitee = serializers.SerializerMethodField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = GroupInvitation
        fields = [
            'id', 'group', 'inviter', 'invitee', 'message',
            'status', 'is_expired', 'expires_at', 'responded_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = fields
    
    def get_group(self, obj):
        """获取群组信息"""
        return {
            'id': str(obj.group.uuid),
            'name': obj.group.name,
            'avatar_url': obj.group.avatar_url
        }
    
    def get_inviter(self, obj):
        """获取邀请者信息"""
        return {
            'id': str(obj.inviter.uuid),
            'username': obj.inviter.username,
            'display_name': obj.inviter.display_name
        }
    
    def get_invitee(self, obj):
        """获取被邀请者信息"""
        return {
            'id': str(obj.invitee.uuid),
            'username': obj.invitee.username,
            'display_name': obj.invitee.display_name
        }


class UserRecommendationSerializer(serializers.ModelSerializer):
    """用户推荐序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    recommended_user = serializers.SerializerMethodField()
    
    class Meta:
        model = UserRecommendation
        fields = [
            'id', 'recommended_user', 'recommendation_type',
            'reason', 'confidence_score',
            'is_shown', 'is_dismissed', 'is_accepted',
            'shown_at', 'dismissed_at', 'accepted_at',
            'created_at'
        ]
        read_only_fields = fields
    
    def get_recommended_user(self, obj):
        """获取推荐用户信息"""
        return {
            'id': str(obj.recommended_user.uuid),
            'username': obj.recommended_user.username,
            'display_name': obj.recommended_user.display_name,
            'avatar_url': getattr(obj.recommended_user.profile, 'avatar_url', '') if hasattr(obj.recommended_user, 'profile') else ''
        }


class SocialActivitySerializer(serializers.ModelSerializer):
    """社交活动序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    user = serializers.SerializerMethodField()
    target_user = serializers.SerializerMethodField()
    target_group = serializers.SerializerMethodField()
    activity_type_display = serializers.CharField(source='get_activity_type_display', read_only=True)
    
    class Meta:
        model = SocialActivity
        fields = [
            'id', 'user', 'activity_type', 'activity_type_display',
            'target_user', 'target_group',
            'description', 'activity_data',
            'is_public', 'created_at'
        ]
        read_only_fields = fields
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj.user.uuid),
            'username': obj.user.username,
            'display_name': obj.user.display_name
        }
    
    def get_target_user(self, obj):
        """获取目标用户信息"""
        if obj.target_user:
            return {
                'id': str(obj.target_user.uuid),
                'username': obj.target_user.username,
                'display_name': obj.target_user.display_name
            }
        return None
    
    def get_target_group(self, obj):
        """获取目标群组信息"""
        if obj.target_group:
            return {
                'id': str(obj.target_group.uuid),
                'name': obj.target_group.name,
                'avatar_url': obj.target_group.avatar_url
            }
        return None


class BlockedUserSerializer(serializers.ModelSerializer):
    """屏蔽用户序列化器"""
    
    id = serializers.CharField(source='uuid', read_only=True)
    blocked_user = serializers.SerializerMethodField()
    
    class Meta:
        model = BlockedUser
        fields = [
            'id', 'blocked_user', 'reason', 'created_at'
        ]
        read_only_fields = ['id', 'blocked_user', 'created_at']
    
    def get_blocked_user(self, obj):
        """获取被屏蔽用户信息"""
        return {
            'id': str(obj.blocked.uuid),
            'username': obj.blocked.username,
            'display_name': obj.blocked.display_name
        }


class SocialStatsSerializer(serializers.Serializer):
    """社交统计序列化器"""
    
    user_id = serializers.CharField(read_only=True)
    friends_count = serializers.IntegerField(read_only=True)
    following_count = serializers.IntegerField(read_only=True)
    followers_count = serializers.IntegerField(read_only=True)
    owned_groups_count = serializers.IntegerField(read_only=True)
    joined_groups_count = serializers.IntegerField(read_only=True)
    activities_count = serializers.IntegerField(read_only=True)
    total_social_connections = serializers.IntegerField(read_only=True)


class FriendSuggestionSerializer(serializers.Serializer):
    """好友建议序列化器"""
    
    user = serializers.SerializerMethodField()
    mutual_friends_count = serializers.IntegerField()
    common_interests = serializers.ListField(child=serializers.CharField())
    suggestion_reason = serializers.CharField()
    confidence_score = serializers.FloatField()
    
    def get_user(self, obj):
        """获取用户信息"""
        return {
            'id': str(obj['user'].uuid),
            'username': obj['user'].username,
            'display_name': obj['user'].display_name,
            'avatar_url': getattr(obj['user'].profile, 'avatar_url', '') if hasattr(obj['user'], 'profile') else ''
        }


class GroupSearchSerializer(serializers.Serializer):
    """群组搜索序列化器"""
    
    query = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="搜索关键词"
    )
    group_type = serializers.ChoiceField(
        choices=Group.GROUP_TYPE_CHOICES,
        required=False,
        help_text="群组类型过滤"
    )
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        help_text="标签过滤"
    )
    min_members = serializers.IntegerField(
        min_value=0,
        required=False,
        help_text="最小成员数"
    )
    max_members = serializers.IntegerField(
        min_value=1,
        required=False,
        help_text="最大成员数"
    )
    order_by = serializers.ChoiceField(
        choices=[
            ('name', '按名称排序'),
            ('-created_at', '按创建时间倒序'),
            ('-current_members', '按成员数倒序'),
            ('-activity_count', '按活跃度倒序')
        ],
        default='-created_at',
        required=False,
        help_text="排序方式"
    )


class UserSearchSerializer(serializers.Serializer):
    """用户搜索序列化器"""
    
    query = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="搜索关键词"
    )
    user_type = serializers.CharField(
        required=False,
        help_text="用户类型过滤"
    )
    is_online = serializers.BooleanField(
        required=False,
        help_text="是否在线"
    )
    has_avatar = serializers.BooleanField(
        required=False,
        help_text="是否有头像"
    )
    order_by = serializers.ChoiceField(
        choices=[
            ('username', '按用户名排序'),
            ('-created_at', '按注册时间倒序'),
            ('-last_login', '按最后登录倒序')
        ],
        default='username',
        required=False,
        help_text="排序方式"
    )