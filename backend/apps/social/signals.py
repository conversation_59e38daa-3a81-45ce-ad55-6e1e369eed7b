"""
社交域信号处理器
处理社交相关的Django信号
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import Friendship, Follow, Group, GroupMember

User = get_user_model()


@receiver(post_save, sender=Friendship)
def handle_friendship_created(sender, instance, created, **kwargs):
    """
    处理好友关系创建
    """
    if created and instance.status == 'accepted':
        # 可以在这里添加好友关系创建后的逻辑
        # 比如发送通知、更新统计等
        pass


@receiver(post_save, sender=Follow)
def handle_follow_created(sender, instance, created, **kwargs):
    """
    处理关注关系创建
    """
    if created:
        # 可以在这里添加关注后的逻辑
        # 比如发送通知、更新粉丝数等
        pass


@receiver(post_delete, sender=Follow)
def handle_follow_deleted(sender, instance, **kwargs):
    """
    处理取消关注
    """
    # 可以在这里添加取消关注后的逻辑
    pass


@receiver(post_save, sender=GroupMember)
def handle_group_member_added(sender, instance, created, **kwargs):
    """
    处理群组成员添加
    """
    if created:
        # 可以在这里添加成员加入群组后的逻辑
        pass


@receiver(post_delete, sender=GroupMember)
def handle_group_member_removed(sender, instance, **kwargs):
    """
    处理群组成员移除
    """
    # 可以在这里添加成员离开群组后的逻辑
    pass
