"""
社交业务服务 - 高内聚的社交关系业务逻辑
职责：处理好友关系、群组管理、关注系统、推荐算法等核心业务逻辑
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Avg, F
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent
from apps.core.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    ForbiddenException,
    ConflictException
)
from apps.core.dependencies import inject
from .models import (
    Friendship, Follow, Group, GroupMember, GroupInvitation,
    UserRecommendation, SocialActivity, BlockedUser
)

logger = logging.getLogger(__name__)


class SocialService:
    """
    社交业务服务 - 高内聚的社交相关业务逻辑
    
    职责：
    1. 好友关系管理
    2. 关注系统管理
    3. 群组创建和管理
    4. 社交推荐算法
    5. 社交活动记录
    """
    
    @transaction.atomic
    def send_friend_request(self, requester_id: str, addressee_id: str, message: str = '') -> Dict[str, Any]:
        """
        发送好友请求
        
        Args:
            requester_id: 请求者ID
            addressee_id: 接收者ID
            message: 请求消息
            
        Returns:
            Dict: 请求结果
            
        Raises:
            ValidationException: 数据验证失败
            ConflictException: 关系冲突
        """
        logger.info(f"发送好友请求: {requester_id} -> {addressee_id}")
        
        # 验证用户
        requester, addressee = self._validate_users(requester_id, addressee_id)
        
        # 检查业务规则
        self._check_friend_request_rules(requester, addressee)
        
        # 创建好友请求
        friendship, created = Friendship.objects.get_or_create(
            requester=requester,
            addressee=addressee,
            defaults={
                'status': 'pending',
                'requested_at': timezone.now()
            }
        )
        
        if not created:
            if friendship.status == 'pending':
                raise ConflictException("已经发送过好友请求")
            elif friendship.status == 'accepted':
                raise ConflictException("已经是好友关系")
            elif friendship.status == 'blocked':
                raise ForbiddenException("无法发送好友请求")
            else:
                # 重新发送请求
                friendship.status = 'pending'
                friendship.requested_at = timezone.now()
                friendship.save()
        
        # 记录社交活动
        self._record_social_activity(
            requester,
            'friend_request',
            f"{requester.username} 向 {addressee.username} 发送了好友请求",
            target_user=addressee,
            activity_data={'message': message}
        )
        
        # 发布好友请求事件
        self._publish_friend_request_event(friendship, message)
        
        logger.info(f"好友请求发送成功: {requester.username} -> {addressee.username}")
        
        return {
            'friendship_id': str(friendship.uuid),
            'status': friendship.status,
            'message': '好友请求发送成功'
        }
    
    @transaction.atomic
    def respond_to_friend_request(self, friendship_id: str, user_id: str, accept: bool) -> Dict[str, Any]:
        """
        响应好友请求
        
        Args:
            friendship_id: 好友关系ID
            user_id: 用户ID
            accept: 是否接受
            
        Returns:
            Dict: 响应结果
        """
        try:
            friendship = Friendship.objects.get(uuid=friendship_id)
        except Friendship.DoesNotExist:
            raise ResourceNotFoundException(f"好友请求不存在: {friendship_id}", "FRIENDSHIP")
        
        # 权限检查
        if str(friendship.addressee.uuid) != user_id:
            raise ForbiddenException("只能响应发给自己的好友请求")
        
        if friendship.status != 'pending':
            raise ValidationException("好友请求已经被处理过了")
        
        if accept:
            success = friendship.accept()
            if success:
                # 记录社交活动
                self._record_social_activity(
                    friendship.addressee,
                    'friend_accepted',
                    f"{friendship.addressee.username} 接受了 {friendship.requester.username} 的好友请求",
                    target_user=friendship.requester
                )
                
                # 发布好友接受事件
                self._publish_friend_accepted_event(friendship)
                
                return {'accepted': True, 'message': '已接受好友请求'}
            else:
                return {'accepted': False, 'message': '接受失败'}
        else:
            friendship.decline()
            return {'declined': True, 'message': '已拒绝好友请求'}
    
    def get_friends(self, user_id: str, status: str = 'accepted') -> List[Dict[str, Any]]:
        """
        获取好友列表
        
        Args:
            user_id: 用户ID
            status: 好友状态
            
        Returns:
            List: 好友列表
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 获取双向好友关系
        friendships = Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user),
            status=status
        ).select_related('requester', 'addressee')
        
        friends = []
        for friendship in friendships:
            friend = friendship.addressee if friendship.requester == user else friendship.requester
            friends.append({
                'friendship_id': str(friendship.uuid),
                'friend': self._serialize_user_summary(friend),
                'is_close_friend': friendship.is_close_friend,
                'is_favorite': friendship.is_favorite,
                'interaction_count': friendship.interaction_count,
                'last_interaction_at': friendship.last_interaction_at.isoformat() if friendship.last_interaction_at else None,
                'friendship_date': friendship.responded_at.isoformat() if friendship.responded_at else None
            })
        
        return friends
    
    def get_friend_requests(self, user_id: str, request_type: str = 'received') -> List[Dict[str, Any]]:
        """
        获取好友请求列表
        
        Args:
            user_id: 用户ID
            request_type: 请求类型 ('received', 'sent')
            
        Returns:
            List: 好友请求列表
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        if request_type == 'received':
            friendships = Friendship.objects.filter(
                addressee=user,
                status='pending'
            ).select_related('requester')
        else:
            friendships = Friendship.objects.filter(
                requester=user,
                status='pending'
            ).select_related('addressee')
        
        requests = []
        for friendship in friendships:
            other_user = friendship.requester if request_type == 'received' else friendship.addressee
            requests.append({
                'friendship_id': str(friendship.uuid),
                'user': self._serialize_user_summary(other_user),
                'requested_at': friendship.requested_at.isoformat(),
                'type': request_type
            })
        
        return requests
    
    @transaction.atomic
    def follow_user(self, follower_id: str, following_id: str) -> Dict[str, Any]:
        """
        关注用户
        
        Args:
            follower_id: 关注者ID
            following_id: 被关注者ID
            
        Returns:
            Dict: 关注结果
        """
        logger.info(f"关注用户: {follower_id} -> {following_id}")
        
        # 验证用户
        follower, following = self._validate_users(follower_id, following_id)
        
        # 检查是否已关注
        if Follow.objects.filter(follower=follower, following=following).exists():
            raise ConflictException("已经关注了该用户")
        
        # 检查是否被屏蔽
        if BlockedUser.objects.filter(blocker=following, blocked=follower).exists():
            raise ForbiddenException("无法关注该用户")
        
        # 创建关注关系
        follow = Follow.objects.create(
            follower=follower,
            following=following
        )
        
        # 记录社交活动
        self._record_social_activity(
            follower,
            'follow',
            f"{follower.username} 关注了 {following.username}",
            target_user=following
        )
        
        # 发布关注事件
        self._publish_follow_event(follow)
        
        logger.info(f"关注成功: {follower.username} -> {following.username}")
        
        return {
            'follow_id': str(follow.uuid),
            'message': '关注成功'
        }
    
    @transaction.atomic
    def unfollow_user(self, follower_id: str, following_id: str) -> Dict[str, Any]:
        """
        取消关注用户
        
        Args:
            follower_id: 关注者ID
            following_id: 被关注者ID
            
        Returns:
            Dict: 取消关注结果
        """
        logger.info(f"取消关注用户: {follower_id} -> {following_id}")
        
        # 验证用户
        follower, following = self._validate_users(follower_id, following_id)
        
        try:
            follow = Follow.objects.get(follower=follower, following=following)
            follow.delete()
            
            # 记录社交活动
            self._record_social_activity(
                follower,
                'unfollow',
                f"{follower.username} 取消关注了 {following.username}",
                target_user=following
            )
            
            # 发布取消关注事件
            self._publish_unfollow_event(follower, following)
            
            logger.info(f"取消关注成功: {follower.username} -> {following.username}")
            
            return {'unfollowed': True, 'message': '取消关注成功'}
            
        except Follow.DoesNotExist:
            return {'unfollowed': False, 'message': '您还没有关注该用户'}
    
    def get_followers(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取粉丝列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List: 粉丝列表
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        follows = Follow.objects.filter(following=user).select_related('follower')
        
        return [
            {
                'follow_id': str(follow.uuid),
                'follower': self._serialize_user_summary(follow.follower),
                'followed_at': follow.created_at.isoformat(),
                'notify_posts': follow.notify_posts,
                'notify_activities': follow.notify_activities
            }
            for follow in follows
        ]
    
    def get_following(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取关注列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List: 关注列表
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        follows = Follow.objects.filter(follower=user).select_related('following')
        
        return [
            {
                'follow_id': str(follow.uuid),
                'following': self._serialize_user_summary(follow.following),
                'followed_at': follow.created_at.isoformat(),
                'notify_posts': follow.notify_posts,
                'notify_activities': follow.notify_activities
            }
            for follow in follows
        ]
    
    @transaction.atomic
    def create_group(self, owner_id: str, group_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建群组
        
        Args:
            owner_id: 群主ID
            group_data: 群组数据
            
        Returns:
            Dict: 创建的群组信息
        """
        logger.info(f"创建群组: {group_data.get('name')}")
        
        # 验证数据
        self._validate_group_data(group_data)
        
        # 检查业务规则
        self._check_group_creation_rules(owner_id, group_data)
        
        # 创建群组
        group = self._create_group_entity(owner_id, group_data)
        
        # 添加群主为成员
        self._add_group_owner_as_member(group)
        
        # 记录社交活动
        self._record_social_activity(
            group.owner,
            'group_create',
            f"{group.owner.username} 创建了群组 {group.name}",
            target_group=group
        )
        
        # 发布群组创建事件
        self._publish_group_created_event(group)
        
        logger.info(f"群组创建成功: {group.name}")
        
        return {
            'group_id': str(group.uuid),
            'name': group.name,
            'group_type': group.group_type,
            'max_members': group.max_members,
            'created_at': group.created_at.isoformat()
        }
    
    def get_group_by_id(self, group_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        根据ID获取群组信息
        
        Args:
            group_id: 群组ID
            user_id: 请求用户ID
            
        Returns:
            Dict: 群组信息
        """
        try:
            group = Group.objects.select_related('owner').get(uuid=group_id)
            
            # 权限检查
            if not self._can_access_group(group, user_id):
                raise ForbiddenException("无权访问该群组")
            
            return self._serialize_group_detail(group, user_id)
            
        except Group.DoesNotExist:
            raise ResourceNotFoundException(f"群组不存在: {group_id}", "GROUP")
    
    @transaction.atomic
    def join_group(self, group_id: str, user_id: str) -> Dict[str, Any]:
        """
        加入群组
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
            
        Returns:
            Dict: 加入结果
        """
        try:
            group = Group.objects.get(uuid=group_id)
        except Group.DoesNotExist:
            raise ResourceNotFoundException(f"群组不存在: {group_id}", "GROUP")
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 检查是否可以加入
        can_join, reason = group.can_join(user)
        if not can_join:
            raise ValidationException(reason)
        
        # 创建群组成员
        member, created = GroupMember.objects.get_or_create(
            group=group,
            user=user,
            defaults={
                'role': 'member',
                'status': 'pending' if group.require_approval else 'active'
            }
        )
        
        if not created:
            if member.status == 'active':
                return {'joined': False, 'message': '您已经是群组成员'}
            else:
                member.status = 'pending' if group.require_approval else 'active'
                member.save()
        
        # 更新群组成员数
        if member.status == 'active':
            group.current_members += 1
            group.save(update_fields=['current_members'])
        
        # 记录社交活动
        self._record_social_activity(
            user,
            'group_join',
            f"{user.username} 加入了群组 {group.name}",
            target_group=group
        )
        
        # 发布加入群组事件
        self._publish_group_joined_event(group, user, member)
        
        logger.info(f"用户加入群组: {user.username} -> {group.name}")
        
        status_message = '加入成功' if member.status == 'active' else '申请已提交，等待审核'
        return {
            'joined': True,
            'member_id': str(member.uuid),
            'status': member.status,
            'message': status_message
        }
    
    def get_user_recommendations(self, user_id: str, recommendation_type: str = None, 
                               limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取用户推荐
        
        Args:
            user_id: 用户ID
            recommendation_type: 推荐类型
            limit: 限制数量
            
        Returns:
            List: 推荐列表
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 获取推荐
        queryset = UserRecommendation.objects.filter(
            user=user,
            is_dismissed=False
        ).select_related('recommended_user')
        
        if recommendation_type:
            queryset = queryset.filter(recommendation_type=recommendation_type)
        
        recommendations = queryset.order_by('-confidence_score', '-created_at')[:limit]
        
        # 标记为已显示
        for rec in recommendations:
            rec.mark_shown()
        
        return [
            {
                'recommendation_id': str(rec.uuid),
                'recommended_user': self._serialize_user_summary(rec.recommended_user),
                'recommendation_type': rec.recommendation_type,
                'reason': rec.reason,
                'confidence_score': rec.confidence_score,
                'created_at': rec.created_at.isoformat()
            }
            for rec in recommendations
        ]
    
    def generate_friend_recommendations(self, user_id: str) -> int:
        """
        生成好友推荐
        
        Args:
            user_id: 用户ID
            
        Returns:
            int: 生成的推荐数量
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 获取用户的好友
        friend_ids = Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user),
            status='accepted'
        ).values_list('requester_id', 'addressee_id')
        
        user_friend_ids = set()
        for req_id, addr_id in friend_ids:
            user_friend_ids.add(req_id if req_id != user.id else addr_id)
        
        # 获取已屏蔽的用户
        blocked_ids = BlockedUser.objects.filter(
            Q(blocker=user) | Q(blocked=user)
        ).values_list('blocked_id', 'blocker_id')
        
        blocked_user_ids = set()
        for blocked_id, blocker_id in blocked_ids:
            blocked_user_ids.add(blocked_id if blocker_id == user.id else blocker_id)
        
        # 查找共同好友的好友（二度好友）
        mutual_friends = User.objects.filter(
            id__in=user_friend_ids
        ).prefetch_related('sent_friend_requests', 'received_friend_requests')
        
        recommendations = []
        for friend in mutual_friends:
            # 获取这个好友的好友
            friend_of_friend_ids = Friendship.objects.filter(
                Q(requester=friend) | Q(addressee=friend),
                status='accepted'
            ).exclude(
                Q(requester=user) | Q(addressee=user)
            ).values_list('requester_id', 'addressee_id')
            
            for req_id, addr_id in friend_of_friend_ids:
                candidate_id = req_id if req_id != friend.id else addr_id
                
                # 排除已经是好友、已屏蔽、自己的用户
                if (candidate_id not in user_friend_ids and 
                    candidate_id not in blocked_user_ids and 
                    candidate_id != user.id):
                    
                    try:
                        candidate = User.objects.get(id=candidate_id)
                        
                        # 检查是否已有推荐
                        if not UserRecommendation.objects.filter(
                            user=user,
                            recommended_user=candidate,
                            recommendation_type='friend'
                        ).exists():
                            
                            recommendations.append(UserRecommendation(
                                user=user,
                                recommended_user=candidate,
                                recommendation_type='friend',
                                reason=f"您和 {friend.username} 都是好友",
                                confidence_score=0.8,
                                source_data={'mutual_friend': friend.username}
                            ))
                    
                    except User.DoesNotExist:
                        continue
        
        # 批量创建推荐
        if recommendations:
            UserRecommendation.objects.bulk_create(recommendations[:20])  # 限制数量
        
        logger.info(f"为用户 {user.username} 生成了 {len(recommendations)} 个好友推荐")
        
        return len(recommendations)
    
    def get_social_stats(self, user_id: str) -> Dict[str, Any]:
        """
        获取社交统计信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 统计信息
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(uuid=user_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}", "USER")
        
        # 统计好友数量
        friends_count = Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user),
            status='accepted'
        ).count()
        
        # 统计关注数量
        following_count = Follow.objects.filter(follower=user).count()
        followers_count = Follow.objects.filter(following=user).count()
        
        # 统计群组数量
        owned_groups_count = Group.objects.filter(owner=user, status='active').count()
        joined_groups_count = GroupMember.objects.filter(
            user=user,
            status='active'
        ).count()
        
        # 统计活动数量
        activities_count = SocialActivity.objects.filter(user=user).count()
        
        return {
            'user_id': str(user.uuid),
            'friends_count': friends_count,
            'following_count': following_count,
            'followers_count': followers_count,
            'owned_groups_count': owned_groups_count,
            'joined_groups_count': joined_groups_count,
            'activities_count': activities_count,
            'total_social_connections': friends_count + following_count + followers_count
        }
    
    # 私有方法
    
    def _validate_users(self, user1_id: str, user2_id: str):
        """验证两个用户"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if user1_id == user2_id:
            raise ValidationException("不能对自己执行此操作")
        
        try:
            user1 = User.objects.get(uuid=user1_id)
            user2 = User.objects.get(uuid=user2_id)
            return user1, user2
        except User.DoesNotExist:
            raise ResourceNotFoundException("用户不存在", "USER")
    
    def _check_friend_request_rules(self, requester, addressee):
        """检查好友请求业务规则"""
        # 检查是否被屏蔽
        if BlockedUser.objects.filter(blocker=addressee, blocked=requester).exists():
            raise ForbiddenException("无法发送好友请求")
        
        # 检查用户状态
        if not addressee.is_active:
            raise ValidationException("目标用户不可用")
    
    def _validate_group_data(self, data: Dict[str, Any]):
        """验证群组数据"""
        required_fields = ['name', 'group_type']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValidationException(
                f"缺少必填字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )
        
        # 验证群组名称长度
        name = data['name']
        if len(name) < 2 or len(name) > 100:
            raise ValidationException("群组名称长度必须在2-100个字符之间")
    
    def _check_group_creation_rules(self, owner_id: str, data: Dict[str, Any]):
        """检查群组创建业务规则"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            owner = User.objects.get(uuid=owner_id)
        except User.DoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {owner_id}", "USER")
        
        # 检查用户状态
        if owner.status != 'active':
            raise ValidationException("用户状态异常，无法创建群组")
        
        # 检查群组数量限制
        max_groups = self._get_user_max_groups(owner)
        current_groups = Group.objects.filter(owner=owner, status='active').count()
        
        if current_groups >= max_groups:
            raise ValidationException(f"您最多只能创建{max_groups}个群组")
    
    def _create_group_entity(self, owner_id: str, data: Dict[str, Any]) -> Group:
        """创建群组实体"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        owner = User.objects.get(uuid=owner_id)
        
        return Group.objects.create(
            name=data['name'],
            description=data.get('description', ''),
            owner=owner,
            group_type=data['group_type'],
            max_members=data.get('max_members', 500),
            allow_member_invite=data.get('allow_member_invite', True),
            require_approval=data.get('require_approval', False),
            allow_anonymous_posts=data.get('allow_anonymous_posts', False),
            avatar_url=data.get('avatar_url', ''),
            cover_url=data.get('cover_url', ''),
            tags=data.get('tags', []),
            created_by=owner,
            updated_by=owner
        )
    
    def _add_group_owner_as_member(self, group: Group):
        """添加群主为成员"""
        GroupMember.objects.create(
            group=group,
            user=group.owner,
            role='owner',
            status='active',
            can_post=True,
            can_comment=True,
            can_invite=True,
            can_moderate=True
        )
    
    def _can_access_group(self, group: Group, user_id: str = None) -> bool:
        """检查是否可以访问群组"""
        if group.group_type == 'public':
            return True
        
        if not user_id:
            return False
        
        if group.group_type == 'secret':
            # 秘密群组只有成员可以访问
            return GroupMember.objects.filter(
                group=group,
                user__uuid=user_id,
                status='active'
            ).exists()
        
        return True  # private群组认证用户可访问
    
    def _get_user_max_groups(self, user) -> int:
        """获取用户最大群组数量"""
        if user.user_type == 'vip':
            return 20
        elif user.user_type == 'premium':
            return 10
        else:
            return 3
    
    def _record_social_activity(self, user, activity_type: str, description: str, 
                              target_user=None, target_group=None, activity_data: dict = None):
        """记录社交活动"""
        SocialActivity.objects.create(
            user=user,
            activity_type=activity_type,
            description=description,
            target_user=target_user,
            target_group=target_group,
            activity_data=activity_data or {},
            is_public=True
        )
    
    def _serialize_user_summary(self, user) -> Dict[str, Any]:
        """序列化用户摘要"""
        return {
            'id': str(user.uuid),
            'username': user.username,
            'display_name': user.display_name,
            'avatar_url': getattr(user.profile, 'avatar_url', '') if hasattr(user, 'profile') else ''
        }
    
    def _serialize_group_detail(self, group: Group, user_id: str = None) -> Dict[str, Any]:
        """序列化群组详情"""
        data = {
            'id': str(group.uuid),
            'name': group.name,
            'description': group.description,
            'owner': self._serialize_user_summary(group.owner),
            'group_type': group.group_type,
            'status': group.status,
            'max_members': group.max_members,
            'current_members': group.current_members,
            'member_rate': group.member_rate,
            'settings': {
                'allow_member_invite': group.allow_member_invite,
                'require_approval': group.require_approval,
                'allow_anonymous_posts': group.allow_anonymous_posts
            },
            'statistics': {
                'post_count': group.post_count,
                'activity_count': group.activity_count
            },
            'media': {
                'avatar_url': group.avatar_url,
                'cover_url': group.cover_url
            },
            'tags': group.tags,
            'created_at': group.created_at.isoformat(),
            'updated_at': group.updated_at.isoformat()
        }
        
        # 添加用户相关信息
        if user_id:
            try:
                member = GroupMember.objects.get(
                    group=group,
                    user__uuid=user_id,
                    status='active'
                )
                data['user_membership'] = {
                    'member_id': str(member.uuid),
                    'role': member.role,
                    'status': member.status,
                    'joined_at': member.created_at.isoformat()
                }
            except GroupMember.DoesNotExist:
                data['user_membership'] = None
        
        return data
    
    def _publish_friend_request_event(self, friendship: Friendship, message: str):
        """发布好友请求事件"""
        event = DomainEvent(
            event_type='social.friend_request_sent',
            aggregate_id=str(friendship.requester.uuid),
            data={
                'friendship_id': str(friendship.uuid),
                'requester_id': str(friendship.requester.uuid),
                'requester_username': friendship.requester.username,
                'addressee_id': str(friendship.addressee.uuid),
                'addressee_username': friendship.addressee.username,
                'message': message
            }
        )
        event_bus.publish(event)
    
    def _publish_friend_accepted_event(self, friendship: Friendship):
        """发布好友接受事件"""
        event = DomainEvent(
            event_type='social.friend_request_accepted',
            aggregate_id=str(friendship.addressee.uuid),
            data={
                'friendship_id': str(friendship.uuid),
                'requester_id': str(friendship.requester.uuid),
                'requester_username': friendship.requester.username,
                'addressee_id': str(friendship.addressee.uuid),
                'addressee_username': friendship.addressee.username
            }
        )
        event_bus.publish(event)
    
    def _publish_follow_event(self, follow: Follow):
        """发布关注事件"""
        event = DomainEvent(
            event_type='social.user_followed',
            aggregate_id=str(follow.follower.uuid),
            data={
                'follow_id': str(follow.uuid),
                'follower_id': str(follow.follower.uuid),
                'follower_username': follow.follower.username,
                'following_id': str(follow.following.uuid),
                'following_username': follow.following.username
            }
        )
        event_bus.publish(event)
    
    def _publish_unfollow_event(self, follower, following):
        """发布取消关注事件"""
        event = DomainEvent(
            event_type='social.user_unfollowed',
            aggregate_id=str(follower.uuid),
            data={
                'follower_id': str(follower.uuid),
                'follower_username': follower.username,
                'following_id': str(following.uuid),
                'following_username': following.username
            }
        )
        event_bus.publish(event)
    
    def _publish_group_created_event(self, group: Group):
        """发布群组创建事件"""
        event = DomainEvent(
            event_type='social.group_created',
            aggregate_id=str(group.uuid),
            data={
                'group_id': str(group.uuid),
                'group_name': group.name,
                'owner_id': str(group.owner.uuid),
                'owner_username': group.owner.username,
                'group_type': group.group_type
            }
        )
        event_bus.publish(event)
    
    def _publish_group_joined_event(self, group: Group, user, member: GroupMember):
        """发布加入群组事件"""
        event = DomainEvent(
            event_type='social.group_joined',
            aggregate_id=str(group.uuid),
            data={
                'group_id': str(group.uuid),
                'group_name': group.name,
                'user_id': str(user.uuid),
                'username': user.username,
                'member_id': str(member.uuid),
                'member_status': member.status
            }
        )
        event_bus.publish(event)