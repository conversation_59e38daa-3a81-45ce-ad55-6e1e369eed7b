"""
社交URL路由配置 - 独立的社交域路由
职责：定义社交相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    FriendshipViewSet,
    FollowViewSet,
    GroupViewSet,
    RecommendationViewSet,
    SocialStatsViewSet,
    SocialActivityViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'friendships', FriendshipViewSet, basename='friendship')
router.register(r'follows', FollowViewSet, basename='follow')
router.register(r'groups', GroupViewSet, basename='group')
router.register(r'recommendations', RecommendationViewSet, basename='recommendation')
router.register(r'social-stats', SocialStatsViewSet, basename='socialstats')
router.register(r'social-activities', SocialActivityViewSet, basename='socialactivity')

# URL模式
urlpatterns = [
    # API路由
    path('api/v1/', include(router.urls)),
]

# 应用命名空间
app_name = 'social'