"""
社交异步任务 - 后台处理任务
职责：处理社交相关的异步任务，如通知发送、推荐生成等
"""

import logging
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from apps.core.events import event_bus, DomainEvent

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_friend_request_notification(self, friendship_id: int, message: str = ''):
    """
    发送好友请求通知
    
    Args:
        friendship_id: 好友关系ID
        message: 请求消息
    """
    try:
        from .models import Friendship
        
        friendship = Friendship.objects.select_related(
            'requester', 'addressee'
        ).get(id=friendship_id)
        
        # 检查请求是否仍然有效
        if friendship.status != 'pending':
            logger.info(f"好友请求已失效，跳过通知发送: {friendship_id}")
            return
        
        # 检查用户通知偏好
        if hasattr(friendship.addressee, 'preferences'):
            notification_settings = friendship.addressee.preferences.notification_settings
            if not notification_settings.get('friend_requests', True):
                logger.info(f"用户已关闭好友请求通知: {friendship.addressee.username}")
                return
        
        # 渲染邮件模板
        subject = f'{friendship.requester.username} 向您发送了好友请求'
        html_message = render_to_string('emails/friend_request.html', {
            'friendship': friendship,
            'requester': friendship.requester,
            'addressee': friendship.addressee,
            'message': message,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL,
            'accept_url': f"{settings.FRONTEND_URL}/friends/requests/{friendship.uuid}/accept",
            'decline_url': f"{settings.FRONTEND_URL}/friends/requests/{friendship.uuid}/decline"
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[friendship.addressee.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"好友请求通知发送成功: {friendship.addressee.email}")
        
        # 发布通知发送事件
        event = DomainEvent(
            event_type='social.friend_request_notification_sent',
            aggregate_id=str(friendship.requester.uuid),
            data={
                'friendship_id': str(friendship.uuid),
                'addressee_email': friendship.addressee.email
            }
        )
        event_bus.publish(event)
        
    except Exception as exc:
        logger.error(f"发送好友请求通知失败: {friendship_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def send_friend_accepted_notification(self, friendship_id: int):
    """
    发送好友接受通知
    
    Args:
        friendship_id: 好友关系ID
    """
    try:
        from .models import Friendship
        
        friendship = Friendship.objects.select_related(
            'requester', 'addressee'
        ).get(id=friendship_id)
        
        # 检查关系状态
        if friendship.status != 'accepted':
            logger.info(f"好友关系状态异常，跳过通知发送: {friendship_id}")
            return
        
        # 检查用户通知偏好
        if hasattr(friendship.requester, 'preferences'):
            notification_settings = friendship.requester.preferences.notification_settings
            if not notification_settings.get('friend_requests', True):
                logger.info(f"用户已关闭好友请求通知: {friendship.requester.username}")
                return
        
        # 渲染邮件模板
        subject = f'{friendship.addressee.username} 接受了您的好友请求'
        html_message = render_to_string('emails/friend_accepted.html', {
            'friendship': friendship,
            'requester': friendship.requester,
            'addressee': friendship.addressee,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL,
            'profile_url': f"{settings.FRONTEND_URL}/users/{friendship.addressee.uuid}"
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[friendship.requester.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"好友接受通知发送成功: {friendship.requester.email}")
        
    except Exception as exc:
        logger.error(f"发送好友接受通知失败: {friendship_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task(bind=True, max_retries=3)
def send_follow_notification(self, follow_id: int):
    """
    发送关注通知
    
    Args:
        follow_id: 关注关系ID
    """
    try:
        from .models import Follow
        
        follow = Follow.objects.select_related(
            'follower', 'following'
        ).get(id=follow_id)
        
        # 检查用户通知偏好
        if hasattr(follow.following, 'preferences'):
            notification_settings = follow.following.preferences.notification_settings
            if not notification_settings.get('follows', True):
                logger.info(f"用户已关闭关注通知: {follow.following.username}")
                return
        
        # 渲染邮件模板
        subject = f'{follow.follower.username} 关注了您'
        html_message = render_to_string('emails/new_follower.html', {
            'follow': follow,
            'follower': follow.follower,
            'following': follow.following,
            'site_name': 'SOIC',
            'site_url': settings.FRONTEND_URL,
            'follower_profile_url': f"{settings.FRONTEND_URL}/users/{follow.follower.uuid}"
        })
        
        # 发送邮件
        send_mail(
            subject=subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[follow.following.email],
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"关注通知发送成功: {follow.following.email}")
        
    except Exception as exc:
        logger.error(f"发送关注通知失败: {follow_id}, 错误: {exc}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries
            raise self.retry(countdown=countdown, exc=exc)
        
        raise


@shared_task
def generate_friend_recommendations_batch():
    """
    批量生成好友推荐
    """
    try:
        from django.contrib.auth import get_user_model
        from .services import SocialService
        
        User = get_user_model()
        social_service = SocialService()
        
        # 获取活跃用户（最近30天有登录）
        thirty_days_ago = timezone.now() - timedelta(days=30)
        active_users = User.objects.filter(
            last_login__gte=thirty_days_ago,
            is_active=True
        )[:1000]  # 限制处理数量
        
        total_generated = 0
        for user in active_users:
            try:
                count = social_service.generate_friend_recommendations(str(user.uuid))
                total_generated += count
            except Exception as e:
                logger.error(f"为用户 {user.username} 生成推荐失败: {e}")
                continue
        
        logger.info(f"批量生成好友推荐完成: 处理了 {len(active_users)} 个用户，生成了 {total_generated} 个推荐")
        
        return total_generated
        
    except Exception as exc:
        logger.error(f"批量生成好友推荐失败: {exc}")
        return 0


@shared_task
def generate_follow_recommendations(user_id: int):
    """
    生成关注推荐
    
    Args:
        user_id: 用户ID
    """
    try:
        from django.contrib.auth import get_user_model
        from .models import Follow, UserRecommendation, Friendship
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 获取用户已关注的人
        following_ids = Follow.objects.filter(
            follower=user
        ).values_list('following_id', flat=True)
        
        # 获取用户的好友
        friend_ids = Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user),
            status='accepted'
        ).values_list('requester_id', 'addressee_id')
        
        user_friend_ids = set()
        for req_id, addr_id in friend_ids:
            user_friend_ids.add(req_id if req_id != user.id else addr_id)
        
        # 查找好友关注的人（但用户还没关注的）
        recommendations = []
        for friend_id in user_friend_ids:
            friend_following = Follow.objects.filter(
                follower_id=friend_id
            ).exclude(
                following_id__in=following_ids
            ).exclude(
                following_id=user.id
            ).values_list('following_id', flat=True)[:5]  # 限制数量
            
            for following_id in friend_following:
                try:
                    candidate = User.objects.get(id=following_id)
                    friend = User.objects.get(id=friend_id)
                    
                    # 检查是否已有推荐
                    if not UserRecommendation.objects.filter(
                        user=user,
                        recommended_user=candidate,
                        recommendation_type='follow'
                    ).exists():
                        
                        recommendations.append(UserRecommendation(
                            user=user,
                            recommended_user=candidate,
                            recommendation_type='follow',
                            reason=f"{friend.username} 也关注了此用户",
                            confidence_score=0.7,
                            source_data={'mutual_friend': friend.username}
                        ))
                
                except User.DoesNotExist:
                    continue
        
        # 批量创建推荐
        if recommendations:
            UserRecommendation.objects.bulk_create(recommendations[:10])  # 限制数量
        
        logger.info(f"为用户 {user.username} 生成了 {len(recommendations)} 个关注推荐")
        
        return len(recommendations)
        
    except Exception as exc:
        logger.error(f"生成关注推荐失败: {user_id}, 错误: {exc}")
        return 0


@shared_task
def cleanup_expired_recommendations():
    """
    清理过期的推荐
    """
    try:
        from .models import UserRecommendation
        
        # 清理30天前的推荐
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count, _ = UserRecommendation.objects.filter(
            created_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"清理过期推荐完成: {deleted_count}个")
        
    except Exception as exc:
        logger.error(f"清理过期推荐失败: {exc}")


@shared_task
def update_social_statistics():
    """
    更新社交统计信息
    """
    try:
        from django.contrib.auth import get_user_model
        from .models import Friendship, Follow, Group, GroupMember
        
        User = get_user_model()
        
        # 更新用户社交统计
        users = User.objects.filter(is_active=True)[:1000]  # 限制处理数量
        
        for user in users:
            try:
                # 统计好友数量
                friends_count = Friendship.objects.filter(
                    Q(requester=user) | Q(addressee=user),
                    status='accepted'
                ).count()
                
                # 统计关注数量
                following_count = Follow.objects.filter(follower=user).count()
                followers_count = Follow.objects.filter(following=user).count()
                
                # 统计群组数量
                owned_groups_count = Group.objects.filter(owner=user, status='active').count()
                joined_groups_count = GroupMember.objects.filter(
                    user=user,
                    status='active'
                ).count()
                
                # 更新用户元数据
                if hasattr(user, 'profile'):
                    user.profile.set_metadata('social_stats', {
                        'friends_count': friends_count,
                        'following_count': following_count,
                        'followers_count': followers_count,
                        'owned_groups_count': owned_groups_count,
                        'joined_groups_count': joined_groups_count,
                        'last_updated': timezone.now().isoformat()
                    })
                
            except Exception as e:
                logger.error(f"更新用户 {user.username} 社交统计失败: {e}")
                continue
        
        logger.info(f"社交统计更新完成: 处理了 {len(users)} 个用户")
        
    except Exception as exc:
        logger.error(f"更新社交统计失败: {exc}")


@shared_task
def send_group_notification(group_id: int, notification_type: str, data: dict):
    """
    发送群组通知
    
    Args:
        group_id: 群组ID
        notification_type: 通知类型
        data: 通知数据
    """
    try:
        from .models import Group, GroupMember
        
        group = Group.objects.get(id=group_id)
        
        # 获取群组成员
        members = GroupMember.objects.filter(
            group=group,
            status='active'
        ).select_related('user')
        
        # 根据通知类型选择模板
        template_map = {
            'group_created': 'emails/group_created.html',
            'member_joined': 'emails/group_member_joined.html',
            'member_left': 'emails/group_member_left.html',
            'group_updated': 'emails/group_updated.html'
        }
        
        template = template_map.get(notification_type)
        if not template:
            logger.error(f"未知的群组通知类型: {notification_type}")
            return
        
        # 批量发送通知
        for member in members:
            try:
                # 检查用户通知偏好
                if hasattr(member.user, 'preferences'):
                    notification_settings = member.user.preferences.notification_settings
                    if not notification_settings.get('group_activities', True):
                        continue
                
                # 渲染邮件模板
                subject = f"群组通知 - {group.name}"
                html_message = render_to_string(template, {
                    'group': group,
                    'member': member,
                    'user': member.user,
                    'data': data,
                    'site_name': 'SOIC',
                    'site_url': settings.FRONTEND_URL,
                    'group_url': f"{settings.FRONTEND_URL}/groups/{group.uuid}"
                })
                
                # 发送邮件
                send_mail(
                    subject=subject,
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[member.user.email],
                    html_message=html_message,
                    fail_silently=True  # 单个失败不影响其他
                )
                
            except Exception as e:
                logger.error(f"发送群组通知给用户 {member.user.username} 失败: {e}")
                continue
        
        logger.info(f"群组通知发送完成: {group.name} - {notification_type}")
        
    except Exception as exc:
        logger.error(f"发送群组通知失败: {group_id}, 错误: {exc}")


@shared_task
def analyze_social_network(user_id: int):
    """
    分析用户社交网络
    
    Args:
        user_id: 用户ID
    """
    try:
        from django.contrib.auth import get_user_model
        from .models import Friendship, Follow
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 分析好友网络
        friends = Friendship.objects.filter(
            Q(requester=user) | Q(addressee=user),
            status='accepted'
        ).select_related('requester', 'addressee')
        
        friend_connections = {}
        for friendship in friends:
            friend = friendship.addressee if friendship.requester == user else friendship.requester
            
            # 分析与好友的互动频率
            interaction_score = friendship.interaction_count
            
            # 分析共同好友
            mutual_friends = Friendship.objects.filter(
                Q(requester=friend) | Q(addressee=friend),
                status='accepted'
            ).exclude(
                Q(requester=user) | Q(addressee=user)
            ).count()
            
            friend_connections[str(friend.uuid)] = {
                'username': friend.username,
                'interaction_score': interaction_score,
                'mutual_friends_count': mutual_friends,
                'friendship_duration': (timezone.now() - friendship.created_at).days
            }
        
        # 分析关注网络
        following = Follow.objects.filter(follower=user).select_related('following')
        followers = Follow.objects.filter(following=user).select_related('follower')
        
        network_analysis = {
            'user_id': str(user.uuid),
            'analysis_date': timezone.now().isoformat(),
            'friends_network': {
                'total_friends': len(friend_connections),
                'connections': friend_connections
            },
            'follow_network': {
                'following_count': following.count(),
                'followers_count': followers.count(),
                'follow_ratio': followers.count() / max(following.count(), 1)
            },
            'network_metrics': {
                'social_influence': followers.count() * 0.6 + len(friend_connections) * 0.4,
                'network_density': len(friend_connections) / max(following.count() + followers.count(), 1)
            }
        }
        
        # 存储分析结果
        if hasattr(user, 'profile'):
            user.profile.set_metadata('social_network_analysis', network_analysis)
        
        logger.info(f"用户社交网络分析完成: {user.username}")
        
        return network_analysis
        
    except Exception as exc:
        logger.error(f"分析用户社交网络失败: {user_id}, 错误: {exc}")
        return None


# 事件处理器

from apps.core.events import event_handler

@event_handler('social.friend_request_sent', async_handler=True)
def handle_friend_request_sent(event):
    """处理好友请求发送事件"""
    friendship_id = event.data.get('friendship_id')
    message = event.data.get('message', '')
    
    if friendship_id:
        # 发送好友请求通知
        send_friend_request_notification.delay(int(friendship_id), message)


@event_handler('social.friend_request_accepted', async_handler=True)
def handle_friend_request_accepted(event):
    """处理好友请求接受事件"""
    friendship_id = event.data.get('friendship_id')
    
    if friendship_id:
        # 发送好友接受通知
        send_friend_accepted_notification.delay(int(friendship_id))


@event_handler('social.user_followed', async_handler=True)
def handle_user_followed(event):
    """处理用户关注事件"""
    follow_id = event.data.get('follow_id')
    
    if follow_id:
        # 发送关注通知
        send_follow_notification.delay(int(follow_id))


@event_handler('social.group_created', async_handler=True)
def handle_group_created(event):
    """处理群组创建事件"""
    group_id = event.data.get('group_id')
    
    if group_id:
        # 发送群组创建通知
        send_group_notification.delay(
            int(group_id),
            'group_created',
            {
                'title': '群组创建成功',
                'group_name': event.data.get('group_name'),
                'owner_username': event.data.get('owner_username')
            }
        )


@event_handler('social.group_joined', async_handler=True)
def handle_group_joined(event):
    """处理加入群组事件"""
    group_id = event.data.get('group_id')
    username = event.data.get('username')
    
    if group_id and username:
        # 发送成员加入通知
        send_group_notification.delay(
            int(group_id),
            'member_joined',
            {
                'title': '新成员加入',
                'username': username,
                'group_name': event.data.get('group_name')
            }
        )