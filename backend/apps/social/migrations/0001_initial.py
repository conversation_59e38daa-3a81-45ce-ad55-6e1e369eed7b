# Generated by Django 5.2 on 2025-07-30 16:06

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('pending', '待处理'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('name', models.CharField(max_length=100, verbose_name='群组名称')),
                ('description', models.TextField(blank=True, verbose_name='群组描述')),
                ('group_type', models.CharField(choices=[('public', '公开群组'), ('private', '私有群组'), ('secret', '秘密群组')], default='public', max_length=20, verbose_name='群组类型')),
                ('max_members', models.PositiveIntegerField(default=500, validators=[django.core.validators.MinValueValidator(2), django.core.validators.MaxValueValidator(10000)], verbose_name='最大成员数')),
                ('current_members', models.PositiveIntegerField(default=1, verbose_name='当前成员数')),
                ('allow_member_invite', models.BooleanField(default=True, verbose_name='允许成员邀请')),
                ('require_approval', models.BooleanField(default=False, verbose_name='需要审批')),
                ('allow_anonymous_posts', models.BooleanField(default=False, verbose_name='允许匿名发帖')),
                ('avatar_url', models.URLField(blank=True, verbose_name='群组头像')),
                ('cover_url', models.URLField(blank=True, verbose_name='群组封面')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='帖子数量')),
                ('activity_count', models.PositiveIntegerField(default=0, verbose_name='活动数量')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='标签')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_groups', to=settings.AUTH_USER_MODEL, verbose_name='群主')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '群组',
                'verbose_name_plural': '群组',
                'db_table': 'social_groups',
            },
        ),
        migrations.CreateModel(
            name='GroupInvitation',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('message', models.TextField(blank=True, verbose_name='邀请消息')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('accepted', '已接受'), ('declined', '已拒绝'), ('expired', '已过期')], default='pending', max_length=20, verbose_name='状态')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('responded_at', models.DateTimeField(blank=True, null=True, verbose_name='响应时间')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='social.group', verbose_name='群组')),
                ('invitee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_group_invitations', to=settings.AUTH_USER_MODEL, verbose_name='被邀请者')),
                ('inviter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_group_invitations', to=settings.AUTH_USER_MODEL, verbose_name='邀请者')),
            ],
            options={
                'verbose_name': '群组邀请',
                'verbose_name_plural': '群组邀请',
                'db_table': 'social_group_invitations',
            },
        ),
        migrations.CreateModel(
            name='GroupMember',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('role', models.CharField(choices=[('owner', '群主'), ('admin', '管理员'), ('moderator', '版主'), ('member', '成员')], default='member', max_length=20, verbose_name='角色')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '非活跃'), ('banned', '被禁'), ('pending', '待审核')], default='active', max_length=20, verbose_name='状态')),
                ('can_post', models.BooleanField(default=True, verbose_name='可以发帖')),
                ('can_comment', models.BooleanField(default=True, verbose_name='可以评论')),
                ('can_invite', models.BooleanField(default=False, verbose_name='可以邀请')),
                ('can_moderate', models.BooleanField(default=False, verbose_name='可以管理')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='发帖数量')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数量')),
                ('last_active_at', models.DateTimeField(blank=True, null=True, verbose_name='最后活跃时间')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='social.group', verbose_name='群组')),
                ('invited_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='group_invitations_sent', to=settings.AUTH_USER_MODEL, verbose_name='邀请者')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_memberships', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '群组成员',
                'verbose_name_plural': '群组成员',
                'db_table': 'social_group_members',
            },
        ),
        migrations.CreateModel(
            name='SocialActivity',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('activity_type', models.CharField(choices=[('friend_request', '好友请求'), ('friend_accepted', '好友接受'), ('follow', '关注'), ('unfollow', '取消关注'), ('group_join', '加入群组'), ('group_leave', '离开群组'), ('group_create', '创建群组'), ('post_create', '发布动态'), ('post_like', '点赞动态'), ('post_comment', '评论动态')], max_length=20, verbose_name='活动类型')),
                ('description', models.TextField(verbose_name='活动描述')),
                ('activity_data', models.JSONField(default=dict, verbose_name='活动数据')),
                ('is_public', models.BooleanField(default=True, verbose_name='是否公开')),
                ('target_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='group_activities', to='social.group', verbose_name='目标群组')),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='targeted_activities', to=settings.AUTH_USER_MODEL, verbose_name='目标用户')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='social_activities', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '社交活动',
                'verbose_name_plural': '社交活动',
                'db_table': 'social_activities',
            },
        ),
        migrations.CreateModel(
            name='UserRecommendation',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('recommendation_type', models.CharField(choices=[('friend', '好友推荐'), ('follow', '关注推荐'), ('group', '群组推荐'), ('space', '空间推荐')], max_length=20, verbose_name='推荐类型')),
                ('reason', models.CharField(max_length=200, verbose_name='推荐原因')),
                ('confidence_score', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='置信度分数')),
                ('source_data', models.JSONField(default=dict, verbose_name='来源数据')),
                ('is_shown', models.BooleanField(default=False, verbose_name='是否已显示')),
                ('is_dismissed', models.BooleanField(default=False, verbose_name='是否已忽略')),
                ('is_accepted', models.BooleanField(default=False, verbose_name='是否已接受')),
                ('shown_at', models.DateTimeField(blank=True, null=True, verbose_name='显示时间')),
                ('dismissed_at', models.DateTimeField(blank=True, null=True, verbose_name='忽略时间')),
                ('accepted_at', models.DateTimeField(blank=True, null=True, verbose_name='接受时间')),
                ('recommended_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommended_to', to=settings.AUTH_USER_MODEL, verbose_name='推荐用户')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户推荐',
                'verbose_name_plural': '用户推荐',
                'db_table': 'social_user_recommendations',
            },
        ),
        migrations.CreateModel(
            name='BlockedUser',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('reason', models.CharField(blank=True, max_length=200, verbose_name='屏蔽原因')),
                ('blocked', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocked_by', to=settings.AUTH_USER_MODEL, verbose_name='被屏蔽者')),
                ('blocker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocked_users', to=settings.AUTH_USER_MODEL, verbose_name='屏蔽者')),
            ],
            options={
                'verbose_name': '屏蔽用户',
                'verbose_name_plural': '屏蔽用户',
                'db_table': 'social_blocked_users',
                'indexes': [models.Index(fields=['blocker'], name='social_bloc_blocker_8a2b12_idx'), models.Index(fields=['blocked'], name='social_bloc_blocked_82baf4_idx')],
                'unique_together': {('blocker', 'blocked')},
            },
        ),
        migrations.CreateModel(
            name='Follow',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('notify_posts', models.BooleanField(default=True, verbose_name='通知新动态')),
                ('notify_activities', models.BooleanField(default=False, verbose_name='通知活动')),
                ('follower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='following', to=settings.AUTH_USER_MODEL, verbose_name='关注者')),
                ('following', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='followers', to=settings.AUTH_USER_MODEL, verbose_name='被关注者')),
            ],
            options={
                'verbose_name': '关注关系',
                'verbose_name_plural': '关注关系',
                'db_table': 'social_follows',
                'indexes': [models.Index(fields=['follower'], name='social_foll_followe_bcf455_idx'), models.Index(fields=['following'], name='social_foll_followi_ec4eb1_idx'), models.Index(fields=['created_at'], name='social_foll_created_da5cc5_idx')],
                'unique_together': {('follower', 'following')},
            },
        ),
        migrations.CreateModel(
            name='Friendship',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('status', models.CharField(choices=[('pending', '待确认'), ('accepted', '已接受'), ('declined', '已拒绝'), ('blocked', '已屏蔽')], default='pending', max_length=20, verbose_name='状态')),
                ('is_close_friend', models.BooleanField(default=False, verbose_name='是否密友')),
                ('is_favorite', models.BooleanField(default=False, verbose_name='是否特别关注')),
                ('interaction_count', models.PositiveIntegerField(default=0, verbose_name='互动次数')),
                ('last_interaction_at', models.DateTimeField(blank=True, null=True, verbose_name='最后互动时间')),
                ('requested_at', models.DateTimeField(auto_now_add=True, verbose_name='请求时间')),
                ('responded_at', models.DateTimeField(blank=True, null=True, verbose_name='响应时间')),
                ('addressee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_friend_requests', to=settings.AUTH_USER_MODEL, verbose_name='接收者')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_friend_requests', to=settings.AUTH_USER_MODEL, verbose_name='请求者')),
            ],
            options={
                'verbose_name': '好友关系',
                'verbose_name_plural': '好友关系',
                'db_table': 'social_friendships',
                'indexes': [models.Index(fields=['requester', 'status'], name='social_frie_request_1b1ca2_idx'), models.Index(fields=['addressee', 'status'], name='social_frie_address_2f9e03_idx'), models.Index(fields=['status'], name='social_frie_status_89a26d_idx'), models.Index(fields=['is_close_friend'], name='social_frie_is_clos_cb3d69_idx'), models.Index(fields=['last_interaction_at'], name='social_frie_last_in_45a49f_idx')],
                'unique_together': {('requester', 'addressee')},
            },
        ),
        migrations.AddIndex(
            model_name='group',
            index=models.Index(fields=['owner'], name='social_grou_owner_i_ee3789_idx'),
        ),
        migrations.AddIndex(
            model_name='group',
            index=models.Index(fields=['group_type'], name='social_grou_group_t_a2151d_idx'),
        ),
        migrations.AddIndex(
            model_name='group',
            index=models.Index(fields=['status'], name='social_grou_status_398ea6_idx'),
        ),
        migrations.AddIndex(
            model_name='group',
            index=models.Index(fields=['-created_at'], name='social_grou_created_0b701b_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvitation',
            index=models.Index(fields=['inviter'], name='social_grou_inviter_077f10_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvitation',
            index=models.Index(fields=['invitee'], name='social_grou_invitee_28eefc_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvitation',
            index=models.Index(fields=['status'], name='social_grou_status_569043_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvitation',
            index=models.Index(fields=['expires_at'], name='social_grou_expires_87704d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='groupinvitation',
            unique_together={('group', 'invitee')},
        ),
        migrations.AddIndex(
            model_name='groupmember',
            index=models.Index(fields=['group', 'status'], name='social_grou_group_i_f90b8e_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmember',
            index=models.Index(fields=['user', 'status'], name='social_grou_user_id_b39562_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmember',
            index=models.Index(fields=['role'], name='social_grou_role_28371c_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmember',
            index=models.Index(fields=['last_active_at'], name='social_grou_last_ac_f1672b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='groupmember',
            unique_together={('group', 'user')},
        ),
        migrations.AddIndex(
            model_name='socialactivity',
            index=models.Index(fields=['user', 'activity_type'], name='social_acti_user_id_2a6ef3_idx'),
        ),
        migrations.AddIndex(
            model_name='socialactivity',
            index=models.Index(fields=['target_user'], name='social_acti_target__ec694d_idx'),
        ),
        migrations.AddIndex(
            model_name='socialactivity',
            index=models.Index(fields=['target_group'], name='social_acti_target__217b2f_idx'),
        ),
        migrations.AddIndex(
            model_name='socialactivity',
            index=models.Index(fields=['is_public'], name='social_acti_is_publ_962957_idx'),
        ),
        migrations.AddIndex(
            model_name='socialactivity',
            index=models.Index(fields=['-created_at'], name='social_acti_created_dfd6af_idx'),
        ),
        migrations.AddIndex(
            model_name='userrecommendation',
            index=models.Index(fields=['user', 'recommendation_type'], name='social_user_user_id_0dc843_idx'),
        ),
        migrations.AddIndex(
            model_name='userrecommendation',
            index=models.Index(fields=['confidence_score'], name='social_user_confide_b32e37_idx'),
        ),
        migrations.AddIndex(
            model_name='userrecommendation',
            index=models.Index(fields=['is_shown', 'is_dismissed'], name='social_user_is_show_18c68b_idx'),
        ),
        migrations.AddIndex(
            model_name='userrecommendation',
            index=models.Index(fields=['created_at'], name='social_user_created_9767f6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userrecommendation',
            unique_together={('user', 'recommended_user', 'recommendation_type')},
        ),
    ]
