"""
社交域数据模型 - 高内聚的社交关系相关模型
职责：定义好友关系、群组、关注、推荐等数据模型
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, AuditModel, StatusModel, MetadataModel
from django.contrib.auth import get_user_model

User = get_user_model()


class Friendship(BaseModel):
    """好友关系模型"""
    
    STATUS_CHOICES = [
        ('pending', '待确认'),
        ('accepted', '已接受'),
        ('declined', '已拒绝'),
        ('blocked', '已屏蔽'),
    ]
    
    requester = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_friend_requests',
        verbose_name='请求者'
    )
    addressee = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_friend_requests',
        verbose_name='接收者'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    
    # 关系属性
    is_close_friend = models.BooleanField(default=False, verbose_name='是否密友')
    is_favorite = models.BooleanField(default=False, verbose_name='是否特别关注')
    
    # 互动统计
    interaction_count = models.PositiveIntegerField(default=0, verbose_name='互动次数')
    last_interaction_at = models.DateTimeField(null=True, blank=True, verbose_name='最后互动时间')
    
    # 时间记录
    requested_at = models.DateTimeField(auto_now_add=True, verbose_name='请求时间')
    responded_at = models.DateTimeField(null=True, blank=True, verbose_name='响应时间')
    
    class Meta:
        db_table = 'social_friendships'
        verbose_name = '好友关系'
        verbose_name_plural = '好友关系'
        unique_together = ['requester', 'addressee']
        indexes = [
            models.Index(fields=['requester', 'status']),
            models.Index(fields=['addressee', 'status']),
            models.Index(fields=['status']),
            models.Index(fields=['is_close_friend']),
            models.Index(fields=['last_interaction_at']),
        ]
    
    def __str__(self):
        return f"{self.requester.username} -> {self.addressee.username} ({self.status})"
    
    @property
    def is_mutual(self):
        """检查是否为双向好友关系"""
        return (
            self.status == 'accepted' and
            Friendship.objects.filter(
                requester=self.addressee,
                addressee=self.requester,
                status='accepted'
            ).exists()
        )
    
    def accept(self):
        """接受好友请求"""
        if self.status == 'pending':
            self.status = 'accepted'
            self.responded_at = timezone.now()
            self.save()
            
            # 创建反向关系
            Friendship.objects.get_or_create(
                requester=self.addressee,
                addressee=self.requester,
                defaults={
                    'status': 'accepted',
                    'responded_at': timezone.now()
                }
            )
            return True
        return False
    
    def decline(self):
        """拒绝好友请求"""
        if self.status == 'pending':
            self.status = 'declined'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False
    
    def block(self):
        """屏蔽用户"""
        self.status = 'blocked'
        self.responded_at = timezone.now()
        self.save()
        
        # 删除反向关系
        Friendship.objects.filter(
            requester=self.addressee,
            addressee=self.requester
        ).delete()
    
    def increment_interaction(self):
        """增加互动次数"""
        self.interaction_count += 1
        self.last_interaction_at = timezone.now()
        self.save(update_fields=['interaction_count', 'last_interaction_at'])


class Follow(BaseModel):
    """关注关系模型"""
    
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='following',
        verbose_name='关注者'
    )
    following = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='followers',
        verbose_name='被关注者'
    )
    
    # 关注设置
    notify_posts = models.BooleanField(default=True, verbose_name='通知新动态')
    notify_activities = models.BooleanField(default=False, verbose_name='通知活动')
    
    class Meta:
        db_table = 'social_follows'
        verbose_name = '关注关系'
        verbose_name_plural = '关注关系'
        unique_together = ['follower', 'following']
        indexes = [
            models.Index(fields=['follower']),
            models.Index(fields=['following']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"


class Group(AuditModel, StatusModel, MetadataModel):
    """群组模型"""
    
    GROUP_TYPE_CHOICES = [
        ('public', '公开群组'),
        ('private', '私有群组'),
        ('secret', '秘密群组'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='群组名称')
    description = models.TextField(blank=True, verbose_name='群组描述')
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='owned_groups',
        verbose_name='群主'
    )
    
    # 群组属性
    group_type = models.CharField(
        max_length=20,
        choices=GROUP_TYPE_CHOICES,
        default='public',
        verbose_name='群组类型'
    )
    max_members = models.PositiveIntegerField(
        default=500,
        validators=[MinValueValidator(2), MaxValueValidator(10000)],
        verbose_name='最大成员数'
    )
    current_members = models.PositiveIntegerField(default=1, verbose_name='当前成员数')
    
    # 群组设置
    allow_member_invite = models.BooleanField(default=True, verbose_name='允许成员邀请')
    require_approval = models.BooleanField(default=False, verbose_name='需要审批')
    allow_anonymous_posts = models.BooleanField(default=False, verbose_name='允许匿名发帖')
    
    # 媒体资源
    avatar_url = models.URLField(blank=True, verbose_name='群组头像')
    cover_url = models.URLField(blank=True, verbose_name='群组封面')
    
    # 统计信息
    post_count = models.PositiveIntegerField(default=0, verbose_name='帖子数量')
    activity_count = models.PositiveIntegerField(default=0, verbose_name='活动数量')
    
    # 标签
    tags = models.JSONField(default=list, blank=True, verbose_name='标签')
    
    class Meta:
        db_table = 'social_groups'
        verbose_name = '群组'
        verbose_name_plural = '群组'
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['group_type']),
            models.Index(fields=['status']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.owner.username})"
    
    @property
    def is_full(self):
        """检查群组是否已满"""
        return self.current_members >= self.max_members
    
    @property
    def member_rate(self):
        """获取成员占用率"""
        if self.max_members == 0:
            return 0
        return (self.current_members / self.max_members) * 100
    
    def can_join(self, user):
        """检查用户是否可以加入群组"""
        if self.is_full:
            return False, "群组已满"
        
        if self.status != 'active':
            return False, "群组不可用"
        
        if self.group_type == 'secret':
            # 秘密群组需要邀请
            return False, "需要邀请才能加入秘密群组"
        
        # 检查是否已经是成员
        if self.members.filter(user=user, status='active').exists():
            return False, "已经是群组成员"
        
        return True, "可以加入"
    
    def increment_post_count(self):
        """增加帖子数量"""
        self.post_count += 1
        self.save(update_fields=['post_count'])
    
    def increment_activity_count(self):
        """增加活动数量"""
        self.activity_count += 1
        self.save(update_fields=['activity_count'])


class GroupMember(BaseModel):
    """群组成员模型"""
    
    ROLE_CHOICES = [
        ('owner', '群主'),
        ('admin', '管理员'),
        ('moderator', '版主'),
        ('member', '成员'),
    ]
    
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('inactive', '非活跃'),
        ('banned', '被禁'),
        ('pending', '待审核'),
    ]
    
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='members',
        verbose_name='群组'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='group_memberships',
        verbose_name='用户'
    )
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='member',
        verbose_name='角色'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name='状态'
    )
    
    # 权限设置
    can_post = models.BooleanField(default=True, verbose_name='可以发帖')
    can_comment = models.BooleanField(default=True, verbose_name='可以评论')
    can_invite = models.BooleanField(default=False, verbose_name='可以邀请')
    can_moderate = models.BooleanField(default=False, verbose_name='可以管理')
    
    # 统计信息
    post_count = models.PositiveIntegerField(default=0, verbose_name='发帖数量')
    comment_count = models.PositiveIntegerField(default=0, verbose_name='评论数量')
    last_active_at = models.DateTimeField(null=True, blank=True, verbose_name='最后活跃时间')
    
    # 加入信息
    invited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='group_invitations_sent',
        verbose_name='邀请者'
    )
    
    class Meta:
        db_table = 'social_group_members'
        verbose_name = '群组成员'
        verbose_name_plural = '群组成员'
        unique_together = ['group', 'user']
        indexes = [
            models.Index(fields=['group', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['role']),
            models.Index(fields=['last_active_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} in {self.group.name} ({self.role})"
    
    def has_permission(self, permission):
        """检查是否有指定权限"""
        permission_map = {
            'post': self.can_post,
            'comment': self.can_comment,
            'invite': self.can_invite,
            'moderate': self.can_moderate,
        }
        
        # 群主和管理员有所有权限
        if self.role in ['owner', 'admin']:
            return True
        
        return permission_map.get(permission, False)
    
    def increment_post_count(self):
        """增加发帖数量"""
        self.post_count += 1
        self.last_active_at = timezone.now()
        self.save(update_fields=['post_count', 'last_active_at'])
    
    def increment_comment_count(self):
        """增加评论数量"""
        self.comment_count += 1
        self.last_active_at = timezone.now()
        self.save(update_fields=['comment_count', 'last_active_at'])


class GroupInvitation(BaseModel):
    """群组邀请模型"""
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('accepted', '已接受'),
        ('declined', '已拒绝'),
        ('expired', '已过期'),
    ]
    
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='invitations',
        verbose_name='群组'
    )
    inviter = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_group_invitations',
        verbose_name='邀请者'
    )
    invitee = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_group_invitations',
        verbose_name='被邀请者'
    )
    
    message = models.TextField(blank=True, verbose_name='邀请消息')
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    expires_at = models.DateTimeField(verbose_name='过期时间')
    responded_at = models.DateTimeField(null=True, blank=True, verbose_name='响应时间')
    
    class Meta:
        db_table = 'social_group_invitations'
        verbose_name = '群组邀请'
        verbose_name_plural = '群组邀请'
        unique_together = ['group', 'invitee']
        indexes = [
            models.Index(fields=['inviter']),
            models.Index(fields=['invitee']),
            models.Index(fields=['status']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.inviter.username} -> {self.invitee.username} ({self.group.name})"
    
    @property
    def is_expired(self):
        """检查是否已过期"""
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def accept(self):
        """接受邀请"""
        if self.status == 'pending' and not self.is_expired:
            self.status = 'accepted'
            self.responded_at = timezone.now()
            self.save()
            
            # 创建群组成员
            GroupMember.objects.get_or_create(
                group=self.group,
                user=self.invitee,
                defaults={
                    'role': 'member',
                    'status': 'active',
                    'invited_by': self.inviter
                }
            )
            
            # 更新群组成员数
            self.group.current_members += 1
            self.group.save(update_fields=['current_members'])
            
            return True
        return False
    
    def decline(self):
        """拒绝邀请"""
        if self.status == 'pending':
            self.status = 'declined'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False


class UserRecommendation(BaseModel):
    """用户推荐模型"""
    
    RECOMMENDATION_TYPE_CHOICES = [
        ('friend', '好友推荐'),
        ('follow', '关注推荐'),
        ('group', '群组推荐'),
        ('space', '空间推荐'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recommendations',
        verbose_name='用户'
    )
    recommended_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recommended_to',
        verbose_name='推荐用户'
    )
    recommendation_type = models.CharField(
        max_length=20,
        choices=RECOMMENDATION_TYPE_CHOICES,
        verbose_name='推荐类型'
    )
    
    # 推荐原因
    reason = models.CharField(max_length=200, verbose_name='推荐原因')
    confidence_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name='置信度分数'
    )
    
    # 推荐来源
    source_data = models.JSONField(default=dict, verbose_name='来源数据')
    
    # 状态
    is_shown = models.BooleanField(default=False, verbose_name='是否已显示')
    is_dismissed = models.BooleanField(default=False, verbose_name='是否已忽略')
    is_accepted = models.BooleanField(default=False, verbose_name='是否已接受')
    
    shown_at = models.DateTimeField(null=True, blank=True, verbose_name='显示时间')
    dismissed_at = models.DateTimeField(null=True, blank=True, verbose_name='忽略时间')
    accepted_at = models.DateTimeField(null=True, blank=True, verbose_name='接受时间')
    
    class Meta:
        db_table = 'social_user_recommendations'
        verbose_name = '用户推荐'
        verbose_name_plural = '用户推荐'
        unique_together = ['user', 'recommended_user', 'recommendation_type']
        indexes = [
            models.Index(fields=['user', 'recommendation_type']),
            models.Index(fields=['confidence_score']),
            models.Index(fields=['is_shown', 'is_dismissed']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Recommend {self.recommended_user.username} to {self.user.username}"
    
    def mark_shown(self):
        """标记为已显示"""
        if not self.is_shown:
            self.is_shown = True
            self.shown_at = timezone.now()
            self.save(update_fields=['is_shown', 'shown_at'])
    
    def dismiss(self):
        """忽略推荐"""
        if not self.is_dismissed:
            self.is_dismissed = True
            self.dismissed_at = timezone.now()
            self.save(update_fields=['is_dismissed', 'dismissed_at'])
    
    def accept(self):
        """接受推荐"""
        if not self.is_accepted:
            self.is_accepted = True
            self.accepted_at = timezone.now()
            self.save(update_fields=['is_accepted', 'accepted_at'])


class SocialActivity(BaseModel):
    """社交活动模型"""
    
    ACTIVITY_TYPE_CHOICES = [
        ('friend_request', '好友请求'),
        ('friend_accepted', '好友接受'),
        ('follow', '关注'),
        ('unfollow', '取消关注'),
        ('group_join', '加入群组'),
        ('group_leave', '离开群组'),
        ('group_create', '创建群组'),
        ('post_create', '发布动态'),
        ('post_like', '点赞动态'),
        ('post_comment', '评论动态'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='social_activities',
        verbose_name='用户'
    )
    activity_type = models.CharField(
        max_length=20,
        choices=ACTIVITY_TYPE_CHOICES,
        verbose_name='活动类型'
    )
    
    # 活动对象（可以是用户、群组等）
    target_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='targeted_activities',
        verbose_name='目标用户'
    )
    target_group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='group_activities',
        verbose_name='目标群组'
    )
    
    # 活动描述
    description = models.TextField(verbose_name='活动描述')
    activity_data = models.JSONField(default=dict, verbose_name='活动数据')
    
    # 可见性
    is_public = models.BooleanField(default=True, verbose_name='是否公开')
    
    class Meta:
        db_table = 'social_activities'
        verbose_name = '社交活动'
        verbose_name_plural = '社交活动'
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['target_user']),
            models.Index(fields=['target_group']),
            models.Index(fields=['is_public']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()}"


class BlockedUser(BaseModel):
    """屏蔽用户模型"""
    
    blocker = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocked_users',
        verbose_name='屏蔽者'
    )
    blocked = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocked_by',
        verbose_name='被屏蔽者'
    )
    reason = models.CharField(max_length=200, blank=True, verbose_name='屏蔽原因')
    
    class Meta:
        db_table = 'social_blocked_users'
        verbose_name = '屏蔽用户'
        verbose_name_plural = '屏蔽用户'
        unique_together = ['blocker', 'blocked']
        indexes = [
            models.Index(fields=['blocker']),
            models.Index(fields=['blocked']),
        ]
    
    def __str__(self):
        return f"{self.blocker.username} blocked {self.blocked.username}"