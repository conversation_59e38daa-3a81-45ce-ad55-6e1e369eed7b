"""
核心视图
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.db import connection
from django.conf import settings
from django.http import HttpResponse
from django.template.response import TemplateResponse
import time
import json


class APIInfoView(APIView):
    """
    API信息着陆页视图
    为根URL提供API信息和导航
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """获取API信息"""
        # 检查请求类型，决定返回HTML还是JSON
        accept_header = request.META.get('HTTP_ACCEPT', '')
        is_browser_request = 'text/html' in accept_header and 'application/json' not in accept_header

        # 基础API信息
        api_info = {
            'name': 'SOIC API',
            'version': '1.0.0',
            'description': 'Social Innovation Community - 社交创新社区后端API',
            'environment': 'development' if settings.DEBUG else 'production',
            'timestamp': time.time(),
            'django_version': '5.2',
            'endpoints': {
                'authentication': {
                    'url': '/api/v1/auth/',
                    'description': '用户认证和授权',
                    'methods': ['POST', 'GET'],
                    'features': ['注册', '登录', '密码重置', '用户资料']
                },
                'social': {
                    'url': '/api/v1/social/',
                    'description': '社交功能',
                    'methods': ['GET', 'POST', 'PUT', 'DELETE'],
                    'features': ['好友关系', '关注系统', '群组管理', '用户空间']
                },
                'messaging': {
                    'url': '/api/v1/messaging/',
                    'description': '消息系统',
                    'methods': ['GET', 'POST', 'PUT', 'DELETE'],
                    'features': ['私聊', '群聊', '实时消息', '消息历史']
                },
                'content': {
                    'url': '/api/v1/content/',
                    'description': '内容管理',
                    'methods': ['GET', 'POST', 'PUT', 'DELETE'],
                    'features': ['帖子发布', '评论系统', '内容审核', '分类管理']
                },
                'economy': {
                    'url': '/api/v1/economy/',
                    'description': '经济系统',
                    'methods': ['GET', 'POST', 'PUT'],
                    'features': ['虚拟货币', '交易系统', '商品管理', '钱包功能']
                },
                'core': {
                    'url': '/api/v1/core/',
                    'description': '核心功能',
                    'methods': ['GET'],
                    'features': ['健康检查', '系统统计', '配置信息']
                }
            },
            'documentation': {
                'swagger': '/api/docs/',
                'redoc': '/api/redoc/',
                'schema': '/api/schema/'
            },
            'admin': '/admin/',
            'health_check': '/api/v1/core/health/',
            'authentication': {
                'type': 'JWT Token',
                'header': 'Authorization: Bearer <token>',
                'login_endpoint': '/api/v1/auth/login/',
                'register_endpoint': '/api/v1/auth/register/'
            },
            'status': 'operational'
        }

        # 如果是浏览器请求，返回HTML页面
        if is_browser_request:
            return TemplateResponse(request, 'core/api_info.html', {
                'api_info': api_info,
                'api_info_json': json.dumps(api_info, indent=2, ensure_ascii=False)
            })

        # 否则返回JSON
        return Response(api_info, status=status.HTTP_200_OK)


class HealthCheckView(APIView):
    """
    健康检查视图
    用于监控系统状态
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """获取系统健康状态"""
        health_data = {
            'status': 'healthy',
            'timestamp': time.time(),
            'version': '1.0.0',
            'environment': settings.DEBUG and 'development' or 'production',
            'checks': {}
        }

        # 检查数据库连接
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            health_data['checks']['database'] = 'ok'
        except Exception as e:
            health_data['checks']['database'] = f'error: {str(e)}'
            health_data['status'] = 'unhealthy'

        # 检查缓存
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            if cache.get('health_check') == 'ok':
                health_data['checks']['cache'] = 'ok'
            else:
                health_data['checks']['cache'] = 'error'
                health_data['status'] = 'degraded'
        except Exception as e:
            health_data['checks']['cache'] = f'error: {str(e)}'
            health_data['status'] = 'degraded'

        # 检查业务域服务（简化版本）
        try:
            # 简单检查应用是否已加载
            from django.apps import apps

            app_labels = ['users', 'social', 'messaging', 'content', 'economy']
            service_status = {}

            for app_label in app_labels:
                try:
                    app_config = apps.get_app_config(app_label)
                    service_status[app_label] = 'ok'
                except Exception:
                    service_status[app_label] = 'error'
                    health_data['status'] = 'degraded'

            health_data['checks']['services'] = service_status

        except Exception as e:
            health_data['checks']['services'] = f'error: {str(e)}'
            health_data['status'] = 'degraded'

        # 返回适当的HTTP状态码
        if health_data['status'] == 'healthy':
            return Response(health_data, status=status.HTTP_200_OK)
        elif health_data['status'] == 'degraded':
            return Response(health_data, status=status.HTTP_200_OK)
        else:
            return Response(health_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)


class SystemStatsView(APIView):
    """
    系统统计视图
    提供系统运行统计信息
    """
    permission_classes = [AllowAny]
    
    def get(self, request):
        """获取系统统计信息"""
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        stats = {
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(status='active').count(),
            },
            'system': {
                'debug': settings.DEBUG,
                'version': '1.0.0',
                'timestamp': time.time()
            }
        }
        
        # 添加其他统计信息
        try:
            from apps.content.models import Post, Comment
            stats['content'] = {
                'posts': Post.objects.filter(status='active').count(),
                'comments': Comment.objects.filter(status='active').count(),
            }
        except:
            pass
        
        try:
            from apps.economy.models import Order, Transaction
            stats['economy'] = {
                'orders': Order.objects.count(),
                'transactions': Transaction.objects.count(),
            }
        except:
            pass
        
        return Response(stats)
