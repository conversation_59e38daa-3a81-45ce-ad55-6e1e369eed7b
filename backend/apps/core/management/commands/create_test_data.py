"""
创建测试数据命令
生成用于开发和测试的示例数据
"""

import random
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class Command(BaseCommand):
    help = '创建测试数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=10,
            help='创建用户数量 (默认: 10)',
        )
        parser.add_argument(
            '--posts',
            type=int,
            default=50,
            help='创建帖子数量 (默认: 50)',
        )
        parser.add_argument(
            '--comments',
            type=int,
            default=100,
            help='创建评论数量 (默认: 100)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始创建测试数据...'))

        with transaction.atomic():
            users = self.create_test_users(options['users'])
            posts = self.create_test_posts(users, options['posts'])
            self.create_test_comments(users, posts, options['comments'])
            self.create_test_social_data(users)

        self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))

    def create_test_users(self, count):
        """创建测试用户"""
        self.stdout.write(f'创建 {count} 个测试用户...')
        
        users = []
        for i in range(count):
            username = f'user{i+1:03d}'
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@example.com',
                    'display_name': f'用户{i+1:03d}',
                    'bio': f'这是用户{i+1}的个人简介',
                    'status': 'active',
                    'user_type': random.choice(['regular', 'vip', 'premium']),
                    'date_joined': timezone.now() - timedelta(days=random.randint(1, 365))
                }
            )
            if created:
                user.set_password('test123')
                user.save()
                users.append(user)
                
        self.stdout.write(f'  ✓ 创建了 {len(users)} 个用户')
        return User.objects.filter(username__startswith='user')

    def create_test_posts(self, users, count):
        """创建测试帖子"""
        self.stdout.write(f'创建 {count} 个测试帖子...')
        
        from apps.content.models import Post, ContentCategory
        
        categories = list(ContentCategory.objects.filter(status='active'))
        post_types = ['text', 'image', 'video', 'link']
        
        sample_titles = [
            '分享一个有趣的技术发现',
            '今天的学习心得',
            '推荐一个好用的工具',
            '生活中的小确幸',
            '关于创新的思考',
            '最近读的一本好书',
            '工作中遇到的问题和解决方案',
            '旅行见闻分享',
            '美食制作心得',
            '健身运动经验'
        ]
        
        sample_content = [
            '今天在工作中遇到了一个有趣的技术问题，经过深入研究找到了解决方案。分享给大家，希望对遇到类似问题的朋友有帮助。',
            '最近在学习新的技术栈，发现了很多有意思的特性。记录下来作为学习笔记，也希望和大家交流讨论。',
            '推荐一个最近发现的工具，真的很好用！可以大大提高工作效率，强烈推荐给大家试试。',
            '生活中总有一些小小的美好时刻，让人感到温暖和幸福。今天就遇到了这样的事情，想和大家分享。',
            '关于创新，我觉得最重要的是保持好奇心和开放的心态。只有这样才能发现新的可能性。',
            '最近读了一本很棒的书，里面的观点很有启发性。推荐给喜欢阅读的朋友们。',
            '在项目开发过程中遇到了一些技术难题，通过团队合作和不断尝试，最终找到了最佳解决方案。',
            '这次旅行收获很多，不仅看到了美丽的风景，还体验了当地的文化。真是一次难忘的经历。',
            '今天尝试制作了一道新菜，味道出乎意料的好！分享制作过程，大家也可以试试。',
            '坚持运动一个月了，身体状态明显改善。分享一些运动心得和经验。'
        ]
        
        posts = []
        for i in range(count):
            author = random.choice(users)
            category = random.choice(categories) if categories else None
            
            post_data = {
                'title': random.choice(sample_titles) + f' #{i+1}',
                'content': random.choice(sample_content),
                'author': author,
                'post_type': random.choice(post_types),
                'visibility': random.choice(['public', 'public', 'public', 'friends']),  # 大部分公开
                'category': category,
                'tags': random.sample(['技术', '学习', '生活', '分享', '经验', '工具', '思考'], 
                                    random.randint(1, 3)),
                'allow_comments': True,
                'moderation_status': 'approved',
                'published_at': timezone.now() - timedelta(
                    days=random.randint(0, 30),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                ),
                'view_count': random.randint(0, 1000),
                'like_count': random.randint(0, 100),
                'comment_count': 0,  # 稍后更新
                'status': 'active'
            }
            
            post = Post.objects.create(**post_data)
            posts.append(post)
            
        self.stdout.write(f'  ✓ 创建了 {len(posts)} 个帖子')
        return posts

    def create_test_comments(self, users, posts, count):
        """创建测试评论"""
        self.stdout.write(f'创建 {count} 个测试评论...')
        
        from apps.content.models import Comment
        
        sample_comments = [
            '很有用的分享，谢谢！',
            '学到了新知识，感谢分享',
            '这个观点很有意思',
            '我也遇到过类似的问题',
            '赞同你的看法',
            '可以详细说说吗？',
            '有没有更多的资料推荐？',
            '这个方法我试过，确实有效',
            '感谢分享经验',
            '期待更多的内容'
        ]
        
        comments = []
        for i in range(count):
            author = random.choice(users)
            post = random.choice(posts)
            
            # 10%的概率创建回复评论
            parent = None
            if random.random() < 0.1 and comments:
                potential_parents = [c for c in comments if c.post == post]
                if potential_parents:
                    parent = random.choice(potential_parents)
            
            comment_data = {
                'post': post,
                'author': author,
                'content': random.choice(sample_comments),
                'parent': parent,
                'moderation_status': 'approved',
                'like_count': random.randint(0, 20),
                'status': 'active'
            }
            
            comment = Comment.objects.create(**comment_data)
            comments.append(comment)
            
            # 更新帖子评论数
            post.comment_count += 1
            post.save()
            
        self.stdout.write(f'  ✓ 创建了 {len(comments)} 个评论')

    def create_test_social_data(self, users):
        """创建测试社交数据"""
        self.stdout.write('创建测试社交数据...')
        
        from apps.social.models import Friendship, Follow, Group
        
        # 创建好友关系
        friendships = 0
        for user in users[:5]:  # 只为前5个用户创建好友关系
            potential_friends = [u for u in users if u != user]
            friends = random.sample(potential_friends, min(3, len(potential_friends)))
            
            for friend in friends:
                friendship, created = Friendship.objects.get_or_create(
                    requester=user,
                    addressee=friend,
                    defaults={
                        'status': 'accepted',
                        'accepted_at': timezone.now()
                    }
                )
                if created:
                    friendships += 1
        
        self.stdout.write(f'  ✓ 创建了 {friendships} 个好友关系')
        
        # 创建关注关系
        follows = 0
        for user in users:
            potential_follows = [u for u in users if u != user]
            follow_users = random.sample(potential_follows, min(5, len(potential_follows)))
            
            for follow_user in follow_users:
                follow, created = Follow.objects.get_or_create(
                    follower=user,
                    following=follow_user
                )
                if created:
                    follows += 1
        
        self.stdout.write(f'  ✓ 创建了 {follows} 个关注关系')
        
        # 创建测试群组
        group_names = ['技术交流群', '生活分享群', '学习讨论群', '创意设计群', '运动健身群']
        groups = []
        
        for name in group_names:
            creator = random.choice(users)
            group, created = Group.objects.get_or_create(
                name=name,
                defaults={
                    'description': f'{name}的描述信息',
                    'creator': creator,
                    'group_type': 'public',
                    'status': 'active'
                }
            )
            if created:
                groups.append(group)
                # 添加创建者为管理员
                group.members.create(user=creator, role='admin')
                
                # 随机添加其他成员
                members = random.sample([u for u in users if u != creator], 
                                      min(8, len(users) - 1))
                for member in members:
                    group.members.create(user=member, role='member')
        
        self.stdout.write(f'  ✓ 创建了 {len(groups)} 个群组')
