"""
初始化数据命令
创建系统必需的基础数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from decimal import Decimal

User = get_user_model()


class Command(BaseCommand):
    help = '初始化系统基础数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-users',
            action='store_true',
            help='跳过创建测试用户',
        )
        parser.add_argument(
            '--skip-economy',
            action='store_true',
            help='跳过创建经济系统数据',
        )
        parser.add_argument(
            '--skip-content',
            action='store_true',
            help='跳过创建内容分类数据',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化系统数据...'))

        with transaction.atomic():
            # 创建经济系统数据
            if not options['skip_economy']:
                self.create_economy_data()

            # 创建内容分类数据
            if not options['skip_content']:
                self.create_content_categories()

            # 创建测试用户
            if not options['skip_users']:
                self.create_test_users()

        self.stdout.write(self.style.SUCCESS('系统数据初始化完成！'))

    def create_economy_data(self):
        """创建经济系统基础数据"""
        self.stdout.write('创建经济系统数据...')
        
        from apps.economy.models import Currency, EconomySettings, Product
        
        # 创建默认货币
        coin, created = Currency.objects.get_or_create(
            code='COIN',
            defaults={
                'name': 'SOIC Coin',
                'symbol': '🪙',
                'description': '平台默认虚拟货币',
                'decimal_places': 2,
                'exchange_rate_to_usd': Decimal('0.01'),
                'total_supply': Decimal('1000000000'),
                'circulating_supply': Decimal('0'),
                'status': 'active'
            }
        )
        if created:
            self.stdout.write(f'  ✓ 创建货币: {coin.name}')

        # 创建经济系统设置
        settings, created = EconomySettings.objects.get_or_create(
            defaults={
                'default_currency': coin,
                'transaction_fee_rate': Decimal('0.0100'),
                'minimum_transaction_amount': Decimal('0.01'),
                'maximum_transaction_amount': Decimal('10000.00'),
                'initial_balance': Decimal('100.00'),
                'daily_transaction_limit': Decimal('1000.00')
            }
        )
        if created:
            self.stdout.write('  ✓ 创建经济系统设置')

        # 创建示例商品
        products_data = [
            {
                'name': 'VIP会员月卡',
                'description': '享受VIP特权30天',
                'product_type': 'subscription',
                'sku': 'VIP_MONTH',
                'category': 'membership',
                'price': Decimal('29.99'),
                'unlimited_stock': True,
                'is_featured': True
            },
            {
                'name': '个性头像框',
                'description': '炫酷的个性头像框',
                'product_type': 'avatar_item',
                'sku': 'AVATAR_FRAME_001',
                'category': 'avatar',
                'price': Decimal('9.99'),
                'stock_quantity': 1000,
                'unlimited_stock': False
            },
            {
                'name': '空间装饰主题',
                'description': '个人空间装饰主题',
                'product_type': 'space_decoration',
                'sku': 'SPACE_THEME_001',
                'category': 'decoration',
                'price': Decimal('19.99'),
                'unlimited_stock': True
            }
        ]

        for product_data in products_data:
            product, created = Product.objects.get_or_create(
                sku=product_data['sku'],
                defaults={
                    **product_data,
                    'currency': coin,
                    'status': 'active'
                }
            )
            if created:
                self.stdout.write(f'  ✓ 创建商品: {product.name}')

    def create_content_categories(self):
        """创建内容分类数据"""
        self.stdout.write('创建内容分类数据...')
        
        from apps.content.models import ContentCategory
        
        categories_data = [
            {
                'name': '技术分享',
                'slug': 'tech',
                'description': '技术相关的内容分享',
                'icon_url': '🔧',
                'color': '#3498db'
            },
            {
                'name': '生活随笔',
                'slug': 'life',
                'description': '生活感悟和随笔',
                'icon_url': '📝',
                'color': '#2ecc71'
            },
            {
                'name': '创意设计',
                'slug': 'design',
                'description': '设计作品和创意分享',
                'icon_url': '🎨',
                'color': '#e74c3c'
            },
            {
                'name': '学习笔记',
                'slug': 'study',
                'description': '学习心得和笔记分享',
                'icon_url': '📚',
                'color': '#f39c12'
            },
            {
                'name': '娱乐休闲',
                'slug': 'entertainment',
                'description': '娱乐和休闲内容',
                'icon_url': '🎮',
                'color': '#9b59b6'
            }
        ]

        for category_data in categories_data:
            category, created = ContentCategory.objects.get_or_create(
                slug=category_data['slug'],
                defaults={
                    **category_data,
                    'status': 'active',
                    'sort_order': 0
                }
            )
            if created:
                self.stdout.write(f'  ✓ 创建分类: {category.name}')

    def create_test_users(self):
        """创建测试用户"""
        self.stdout.write('创建测试用户...')
        
        # 创建管理员用户
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': '系统',
                'last_name': '管理员',
                'is_staff': True,
                'is_superuser': True,
                'user_type': 'admin',
                'status': 'active'
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write('  ✓ 创建管理员用户: admin (密码: admin123)')

        # 创建测试用户
        test_users_data = [
            {
                'username': 'testuser1',
                'email': '<EMAIL>',
                'first_name': '测试',
                'last_name': '用户1',
                'password': 'test123'
            },
            {
                'username': 'testuser2',
                'email': '<EMAIL>',
                'first_name': '测试',
                'last_name': '用户2',
                'password': 'test123'
            },
            {
                'username': 'vipuser',
                'email': '<EMAIL>',
                'first_name': 'VIP',
                'last_name': '用户',
                'password': 'vip123',
                'user_type': 'vip'
            }
        ]

        for user_data in test_users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'user_type': user_data.get('user_type', 'regular'),
                    'status': 'active'
                }
            )
            if created:
                user.set_password(user_data['password'])
                user.save()
                self.stdout.write(f'  ✓ 创建测试用户: {user.username} (密码: {user_data["password"]})')

        self.stdout.write(self.style.WARNING('注意: 请在生产环境中修改默认密码！'))
