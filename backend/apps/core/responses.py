"""
标准化响应格式 - 统一API响应结构
职责：提供一致的API响应格式，支持成功和错误响应
"""

from typing import Any, Optional, Dict, List, Union
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import QuerySet
import uuid


class APIResponse:
    """标准化API响应类"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = None,
        status_code: int = status.HTTP_200_OK,
        meta: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Response:
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            status_code: HTTP状态码
            meta: 元数据信息
            headers: 响应头
            
        Returns:
            Response: DRF响应对象
        """
        response_data = {
            'success': True,
            'data': data,
            'timestamp': timezone.now().isoformat(),
            'requestId': str(uuid.uuid4()),
            'version': 'v1'
        }
        
        if message:
            response_data['message'] = message
        
        if meta:
            response_data['meta'] = meta
        
        return Response(response_data, status=status_code, headers=headers)
    
    @staticmethod
    def error(
        code: str,
        message: str,
        details: Any = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        headers: Optional[Dict[str, str]] = None
    ) -> Response:
        """
        错误响应
        
        Args:
            code: 错误代码
            message: 错误消息
            details: 错误详情
            status_code: HTTP状态码
            headers: 响应头
            
        Returns:
            Response: DRF响应对象
        """
        response_data = {
            'success': False,
            'error': {
                'code': code,
                'message': message
            },
            'timestamp': timezone.now().isoformat(),
            'requestId': str(uuid.uuid4()),
            'version': 'v1'
        }
        
        if details:
            response_data['error']['details'] = details
        
        return Response(response_data, status=status_code, headers=headers)
    
    @staticmethod
    def paginated(
        queryset: QuerySet,
        page: int = 1,
        page_size: int = 20,
        serializer_class=None,
        context: Optional[Dict] = None,
        message: str = None
    ) -> Response:
        """
        分页响应
        
        Args:
            queryset: 查询集
            page: 页码
            page_size: 每页大小
            serializer_class: 序列化器类
            context: 序列化器上下文
            message: 响应消息
            
        Returns:
            Response: 分页响应对象
        """
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        if serializer_class:
            serializer = serializer_class(
                page_obj.object_list, 
                many=True, 
                context=context or {}
            )
            data = serializer.data
        else:
            data = list(page_obj.object_list.values())
        
        # 分页元数据
        meta = {
            'pagination': {
                'page': page_obj.number,
                'pageSize': page_size,
                'totalPages': paginator.num_pages,
                'totalItems': paginator.count,
                'hasNext': page_obj.has_next(),
                'hasPrevious': page_obj.has_previous(),
                'nextPage': page_obj.next_page_number() if page_obj.has_next() else None,
                'previousPage': page_obj.previous_page_number() if page_obj.has_previous() else None
            }
        }
        
        return APIResponse.success(
            data=data,
            message=message,
            meta=meta
        )
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "创建成功",
        location: str = None
    ) -> Response:
        """
        创建成功响应
        
        Args:
            data: 创建的数据
            message: 响应消息
            location: 资源位置
            
        Returns:
            Response: 创建成功响应
        """
        headers = {}
        if location:
            headers['Location'] = location
        
        return APIResponse.success(
            data=data,
            message=message,
            status_code=status.HTTP_201_CREATED,
            headers=headers
        )
    
    @staticmethod
    def updated(
        data: Any = None,
        message: str = "更新成功"
    ) -> Response:
        """
        更新成功响应
        
        Args:
            data: 更新后的数据
            message: 响应消息
            
        Returns:
            Response: 更新成功响应
        """
        return APIResponse.success(
            data=data,
            message=message,
            status_code=status.HTTP_200_OK
        )
    
    @staticmethod
    def deleted(message: str = "删除成功") -> Response:
        """
        删除成功响应
        
        Args:
            message: 响应消息
            
        Returns:
            Response: 删除成功响应
        """
        return APIResponse.success(
            message=message,
            status_code=status.HTTP_204_NO_CONTENT
        )
    
    @staticmethod
    def not_found(
        message: str = "资源不存在",
        code: str = "RESOURCE_NOT_FOUND"
    ) -> Response:
        """
        资源不存在响应
        
        Args:
            message: 错误消息
            code: 错误代码
            
        Returns:
            Response: 404响应
        """
        return APIResponse.error(
            code=code,
            message=message,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def unauthorized(
        message: str = "未授权访问",
        code: str = "UNAUTHORIZED"
    ) -> Response:
        """
        未授权响应
        
        Args:
            message: 错误消息
            code: 错误代码
            
        Returns:
            Response: 401响应
        """
        return APIResponse.error(
            code=code,
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def forbidden(
        message: str = "禁止访问",
        code: str = "FORBIDDEN"
    ) -> Response:
        """
        禁止访问响应
        
        Args:
            message: 错误消息
            code: 错误代码
            
        Returns:
            Response: 403响应
        """
        return APIResponse.error(
            code=code,
            message=message,
            status_code=status.HTTP_403_FORBIDDEN
        )
    
    @staticmethod
    def validation_error(
        errors: Union[Dict, List],
        message: str = "数据验证失败",
        code: str = "VALIDATION_ERROR"
    ) -> Response:
        """
        验证错误响应
        
        Args:
            errors: 验证错误详情
            message: 错误消息
            code: 错误代码
            
        Returns:
            Response: 422响应
        """
        return APIResponse.error(
            code=code,
            message=message,
            details=errors,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )
    
    @staticmethod
    def server_error(
        message: str = "服务器内部错误",
        code: str = "INTERNAL_SERVER_ERROR",
        details: Any = None
    ) -> Response:
        """
        服务器错误响应
        
        Args:
            message: 错误消息
            code: 错误代码
            details: 错误详情
            
        Returns:
            Response: 500响应
        """
        return APIResponse.error(
            code=code,
            message=message,
            details=details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    @staticmethod
    def rate_limited(
        message: str = "请求过于频繁",
        code: str = "RATE_LIMITED",
        retry_after: int = None
    ) -> Response:
        """
        限流响应
        
        Args:
            message: 错误消息
            code: 错误代码
            retry_after: 重试等待时间（秒）
            
        Returns:
            Response: 429响应
        """
        headers = {}
        if retry_after:
            headers['Retry-After'] = str(retry_after)
        
        return APIResponse.error(
            code=code,
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            headers=headers
        )


class ResponseBuilder:
    """响应构建器 - 链式调用构建响应"""
    
    def __init__(self):
        self._data = None
        self._message = None
        self._status_code = status.HTTP_200_OK
        self._meta = {}
        self._headers = {}
        self._error_code = None
        self._error_details = None
        self._is_error = False
    
    def data(self, data: Any) -> 'ResponseBuilder':
        """设置响应数据"""
        self._data = data
        return self
    
    def message(self, message: str) -> 'ResponseBuilder':
        """设置响应消息"""
        self._message = message
        return self
    
    def status_code(self, code: int) -> 'ResponseBuilder':
        """设置状态码"""
        self._status_code = code
        return self
    
    def meta(self, key: str, value: Any) -> 'ResponseBuilder':
        """添加元数据"""
        self._meta[key] = value
        return self
    
    def header(self, key: str, value: str) -> 'ResponseBuilder':
        """添加响应头"""
        self._headers[key] = value
        return self
    
    def error(self, code: str, message: str, details: Any = None) -> 'ResponseBuilder':
        """设置错误信息"""
        self._is_error = True
        self._error_code = code
        self._message = message
        self._error_details = details
        return self
    
    def build(self) -> Response:
        """构建响应"""
        if self._is_error:
            return APIResponse.error(
                code=self._error_code,
                message=self._message,
                details=self._error_details,
                status_code=self._status_code,
                headers=self._headers or None
            )
        else:
            return APIResponse.success(
                data=self._data,
                message=self._message,
                status_code=self._status_code,
                meta=self._meta or None,
                headers=self._headers or None
            )


# 便捷函数
def success_response(*args, **kwargs) -> Response:
    """成功响应便捷函数"""
    return APIResponse.success(*args, **kwargs)


def error_response(*args, **kwargs) -> Response:
    """错误响应便捷函数"""
    return APIResponse.error(*args, **kwargs)


def paginated_response(*args, **kwargs) -> Response:
    """分页响应便捷函数"""
    return APIResponse.paginated(*args, **kwargs)


def response_builder() -> ResponseBuilder:
    """响应构建器便捷函数"""
    return ResponseBuilder()


# 常用错误代码常量
class ErrorCodes:
    """错误代码常量"""
    
    # 通用错误
    INVALID_REQUEST = "INVALID_REQUEST"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    RATE_LIMITED = "RATE_LIMITED"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    
    # 用户相关错误
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    EMAIL_ALREADY_EXISTS = "EMAIL_ALREADY_EXISTS"
    USERNAME_ALREADY_EXISTS = "USERNAME_ALREADY_EXISTS"
    
    # 空间相关错误
    SPACE_NOT_FOUND = "SPACE_NOT_FOUND"
    SPACE_ACCESS_DENIED = "SPACE_ACCESS_DENIED"
    SPACE_CAPACITY_EXCEEDED = "SPACE_CAPACITY_EXCEEDED"
    
    # 消息相关错误
    MESSAGE_NOT_FOUND = "MESSAGE_NOT_FOUND"
    MESSAGE_TOO_LONG = "MESSAGE_TOO_LONG"
    
    # 文件相关错误
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    INVALID_FILE_TYPE = "INVALID_FILE_TYPE"
    FILE_UPLOAD_FAILED = "FILE_UPLOAD_FAILED"


# 常用成功消息常量
class SuccessMessages:
    """成功消息常量"""
    
    # 通用消息
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    OPERATION_SUCCESS = "操作成功"
    
    # 用户相关消息
    USER_REGISTERED = "用户注册成功"
    USER_UPDATED = "用户信息更新成功"
    PASSWORD_CHANGED = "密码修改成功"
    EMAIL_VERIFIED = "邮箱验证成功"
    
    # 空间相关消息
    SPACE_CREATED = "空间创建成功"
    SPACE_UPDATED = "空间更新成功"
    SPACE_JOINED = "加入空间成功"
    SPACE_LEFT = "离开空间成功"
    
    # 消息相关消息
    MESSAGE_SENT = "消息发送成功"
    MESSAGE_DELETED = "消息删除成功"