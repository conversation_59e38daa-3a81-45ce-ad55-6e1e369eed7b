"""
统一异常处理 - 标准化异常处理机制
职责：定义业务异常类型，提供统一的异常处理逻辑
"""

import logging
from typing import Any, Dict, Optional
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import Http404
from django.db import IntegrityError
from .responses import APIResponse, ErrorCodes

logger = logging.getLogger(__name__)


class BaseBusinessException(Exception):
    """业务异常基类"""
    
    def __init__(
        self,
        message: str,
        code: str = None,
        details: Any = None,
        status_code: int = status.HTTP_400_BAD_REQUEST
    ):
        self.message = message
        self.code = code or self.__class__.__name__.upper()
        self.details = details
        self.status_code = status_code
        super().__init__(message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'code': self.code,
            'message': self.message
        }
        if self.details:
            result['details'] = self.details
        return result


class ValidationException(BaseBusinessException):
    """数据验证异常"""
    
    def __init__(self, message: str = "数据验证失败", details: Any = None):
        super().__init__(
            message=message,
            code=ErrorCodes.VALIDATION_ERROR,
            details=details,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )


class ResourceNotFoundException(BaseBusinessException):
    """资源不存在异常"""
    
    def __init__(self, message: str = "资源不存在", resource_type: str = None):
        code = ErrorCodes.RESOURCE_NOT_FOUND
        if resource_type:
            code = f"{resource_type.upper()}_NOT_FOUND"
        
        super().__init__(
            message=message,
            code=code,
            status_code=status.HTTP_404_NOT_FOUND
        )


class UnauthorizedException(BaseBusinessException):
    """未授权异常"""
    
    def __init__(self, message: str = "未授权访问"):
        super().__init__(
            message=message,
            code=ErrorCodes.UNAUTHORIZED,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class ForbiddenException(BaseBusinessException):
    """禁止访问异常"""
    
    def __init__(self, message: str = "禁止访问"):
        super().__init__(
            message=message,
            code=ErrorCodes.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )


class ConflictException(BaseBusinessException):
    """冲突异常"""
    
    def __init__(self, message: str = "资源冲突", details: Any = None):
        super().__init__(
            message=message,
            code="CONFLICT",
            details=details,
            status_code=status.HTTP_409_CONFLICT
        )


class RateLimitException(BaseBusinessException):
    """限流异常"""
    
    def __init__(self, message: str = "请求过于频繁", retry_after: int = None):
        details = {}
        if retry_after:
            details['retry_after'] = retry_after
        
        super().__init__(
            message=message,
            code=ErrorCodes.RATE_LIMITED,
            details=details or None,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )


class ServerException(BaseBusinessException):
    """服务器异常"""
    
    def __init__(self, message: str = "服务器内部错误", details: Any = None):
        super().__init__(
            message=message,
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            details=details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 用户相关异常
class UserNotFoundException(ResourceNotFoundException):
    """用户不存在异常"""
    
    def __init__(self, user_id: str = None):
        message = "用户不存在"
        if user_id:
            message = f"用户不存在: {user_id}"
        super().__init__(message=message, resource_type="USER")


class UserAlreadyExistsException(ConflictException):
    """用户已存在异常"""
    
    def __init__(self, field: str = "用户", value: str = None):
        message = f"{field}已存在"
        if value:
            message = f"{field}已存在: {value}"
        super().__init__(message=message)


class InvalidCredentialsException(UnauthorizedException):
    """无效凭据异常"""
    
    def __init__(self, message: str = "用户名或密码错误"):
        super().__init__(message=message)
        self.code = ErrorCodes.INVALID_CREDENTIALS


class EmailAlreadyExistsException(UserAlreadyExistsException):
    """邮箱已存在异常"""
    
    def __init__(self, email: str = None):
        super().__init__(field="邮箱", value=email)
        self.code = ErrorCodes.EMAIL_ALREADY_EXISTS


class UsernameAlreadyExistsException(UserAlreadyExistsException):
    """用户名已存在异常"""
    
    def __init__(self, username: str = None):
        super().__init__(field="用户名", value=username)
        self.code = ErrorCodes.USERNAME_ALREADY_EXISTS


# 空间相关异常
class SpaceNotFoundException(ResourceNotFoundException):
    """空间不存在异常"""
    
    def __init__(self, space_id: str = None):
        message = "空间不存在"
        if space_id:
            message = f"空间不存在: {space_id}"
        super().__init__(message=message, resource_type="SPACE")


class SpaceAccessDeniedException(ForbiddenException):
    """空间访问被拒绝异常"""
    
    def __init__(self, message: str = "无权访问该空间"):
        super().__init__(message=message)
        self.code = ErrorCodes.SPACE_ACCESS_DENIED


class SpaceCapacityExceededException(ValidationException):
    """空间容量超限异常"""
    
    def __init__(self, current: int = None, limit: int = None):
        message = "空间容量已满"
        details = None
        
        if current is not None and limit is not None:
            message = f"空间容量已满 ({current}/{limit})"
            details = {'current': current, 'limit': limit}
        
        super().__init__(message=message, details=details)
        self.code = ErrorCodes.SPACE_CAPACITY_EXCEEDED


# 消息相关异常
class MessageNotFoundException(ResourceNotFoundException):
    """消息不存在异常"""
    
    def __init__(self, message_id: str = None):
        message = "消息不存在"
        if message_id:
            message = f"消息不存在: {message_id}"
        super().__init__(message=message, resource_type="MESSAGE")


class MessageTooLongException(ValidationException):
    """消息过长异常"""
    
    def __init__(self, current_length: int = None, max_length: int = None):
        message = "消息内容过长"
        details = None
        
        if current_length is not None and max_length is not None:
            message = f"消息内容过长 ({current_length}/{max_length})"
            details = {'current_length': current_length, 'max_length': max_length}
        
        super().__init__(message=message, details=details)
        self.code = ErrorCodes.MESSAGE_TOO_LONG


# 文件相关异常
class FileTooLargeException(ValidationException):
    """文件过大异常"""
    
    def __init__(self, file_size: int = None, max_size: int = None):
        message = "文件大小超出限制"
        details = None
        
        if file_size is not None and max_size is not None:
            message = f"文件大小超出限制 ({file_size}/{max_size} bytes)"
            details = {'file_size': file_size, 'max_size': max_size}
        
        super().__init__(message=message, details=details)
        self.code = ErrorCodes.FILE_TOO_LARGE


class InvalidFileTypeException(ValidationException):
    """无效文件类型异常"""
    
    def __init__(self, file_type: str = None, allowed_types: list = None):
        message = "不支持的文件类型"
        details = None
        
        if file_type and allowed_types:
            message = f"不支持的文件类型: {file_type}"
            details = {'file_type': file_type, 'allowed_types': allowed_types}
        
        super().__init__(message=message, details=details)
        self.code = ErrorCodes.INVALID_FILE_TYPE


class FileUploadFailedException(ServerException):
    """文件上传失败异常"""
    
    def __init__(self, message: str = "文件上传失败", details: Any = None):
        super().__init__(message=message, details=details)
        self.code = ErrorCodes.FILE_UPLOAD_FAILED


def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    
    # 获取请求信息
    request = context.get('request')
    view = context.get('view')
    
    # 记录异常信息
    logger.error(
        f"异常发生: {exc.__class__.__name__}: {str(exc)}, "
        f"视图: {view.__class__.__name__ if view else 'Unknown'}, "
        f"路径: {request.path if request else 'Unknown'}"
    )
    
    # 处理业务异常
    if isinstance(exc, BaseBusinessException):
        return APIResponse.error(
            code=exc.code,
            message=exc.message,
            details=exc.details,
            status_code=exc.status_code
        )
    
    # 处理Django内置异常
    if isinstance(exc, Http404):
        return APIResponse.not_found()
    
    if isinstance(exc, DjangoValidationError):
        details = None
        if hasattr(exc, 'message_dict'):
            details = exc.message_dict
        elif hasattr(exc, 'messages'):
            details = exc.messages
        
        return APIResponse.validation_error(
            errors=details,
            message="数据验证失败"
        )
    
    if isinstance(exc, IntegrityError):
        return APIResponse.error(
            code="INTEGRITY_ERROR",
            message="数据完整性错误",
            status_code=status.HTTP_409_CONFLICT
        )
    
    # 调用DRF默认异常处理器
    response = exception_handler(exc, context)
    
    if response is not None:
        # 转换DRF异常响应格式
        error_data = response.data
        
        if isinstance(error_data, dict):
            # 处理字段验证错误
            if any(key in error_data for key in ['detail', 'non_field_errors']):
                message = str(error_data.get('detail', error_data.get('non_field_errors', ['验证失败'])[0]))
                details = error_data if len(error_data) > 1 else None
            else:
                message = "请求处理失败"
                details = error_data
        else:
            message = str(error_data)
            details = None
        
        # 确定错误代码
        status_code = response.status_code
        if status_code == status.HTTP_400_BAD_REQUEST:
            code = ErrorCodes.VALIDATION_ERROR
        elif status_code == status.HTTP_401_UNAUTHORIZED:
            code = ErrorCodes.UNAUTHORIZED
        elif status_code == status.HTTP_403_FORBIDDEN:
            code = ErrorCodes.FORBIDDEN
        elif status_code == status.HTTP_404_NOT_FOUND:
            code = ErrorCodes.RESOURCE_NOT_FOUND
        elif status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            code = ErrorCodes.RATE_LIMITED
        else:
            code = ErrorCodes.INVALID_REQUEST
        
        return APIResponse.error(
            code=code,
            message=message,
            details=details,
            status_code=status_code
        )
    
    # 处理未捕获的异常
    logger.exception(f"未处理的异常: {exc}")
    
    return APIResponse.server_error(
        message="服务器内部错误",
        details=str(exc) if hasattr(exc, '__str__') else None
    )


# 异常处理装饰器
def handle_exceptions(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BaseBusinessException:
            # 业务异常直接抛出，由异常处理器处理
            raise
        except Exception as e:
            # 其他异常转换为服务器异常
            logger.exception(f"函数 {func.__name__} 发生未处理异常")
            raise ServerException(
                message="操作失败",
                details=str(e)
            )
    
    return wrapper


# 验证装饰器
def validate_required_fields(*required_fields):
    """验证必填字段装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 假设第一个参数是包含数据的对象
            if args and hasattr(args[0], '__dict__'):
                data = args[0].__dict__
            elif 'data' in kwargs:
                data = kwargs['data']
            else:
                data = {}
            
            missing_fields = []
            for field in required_fields:
                if field not in data or data[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                raise ValidationException(
                    message=f"缺少必填字段: {', '.join(missing_fields)}",
                    details={'missing_fields': missing_fields}
                )
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator