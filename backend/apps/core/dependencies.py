"""
依赖注入容器 - 实现低耦合的依赖管理
职责：管理服务依赖关系，支持依赖注入和服务定位
"""

import logging
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Union
from abc import ABC, abstractmethod
from functools import wraps
import inspect

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceLifetime:
    """服务生命周期枚举"""
    SINGLETON = 'singleton'
    TRANSIENT = 'transient'
    SCOPED = 'scoped'


class ServiceDescriptor:
    """服务描述符"""
    
    def __init__(
        self,
        service_type: Type,
        implementation: Union[Type, Callable],
        lifetime: str = ServiceLifetime.TRANSIENT,
        factory: Optional[Callable] = None
    ):
        self.service_type = service_type
        self.implementation = implementation
        self.lifetime = lifetime
        self.factory = factory
        self.instance = None  # 用于单例模式
    
    def __repr__(self):
        return f"ServiceDescriptor({self.service_type.__name__} -> {self.implementation.__name__})"


class DependencyContainer:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._scoped_instances: Dict[str, Dict[Type, Any]] = {}
        self._current_scope: Optional[str] = None
    
    def register_singleton(self, service_type: Type[T], implementation: Union[Type[T], Callable[[], T]]) -> 'DependencyContainer':
        """注册单例服务"""
        descriptor = ServiceDescriptor(service_type, implementation, ServiceLifetime.SINGLETON)
        self._services[service_type] = descriptor
        logger.debug(f"注册单例服务: {service_type.__name__}")
        return self
    
    def register_transient(self, service_type: Type[T], implementation: Union[Type[T], Callable[[], T]]) -> 'DependencyContainer':
        """注册瞬态服务"""
        descriptor = ServiceDescriptor(service_type, implementation, ServiceLifetime.TRANSIENT)
        self._services[service_type] = descriptor
        logger.debug(f"注册瞬态服务: {service_type.__name__}")
        return self
    
    def register_scoped(self, service_type: Type[T], implementation: Union[Type[T], Callable[[], T]]) -> 'DependencyContainer':
        """注册作用域服务"""
        descriptor = ServiceDescriptor(service_type, implementation, ServiceLifetime.SCOPED)
        self._services[service_type] = descriptor
        logger.debug(f"注册作用域服务: {service_type.__name__}")
        return self
    
    def register_factory(self, service_type: Type[T], factory: Callable[[], T]) -> 'DependencyContainer':
        """注册工厂方法"""
        descriptor = ServiceDescriptor(service_type, factory, ServiceLifetime.TRANSIENT, factory)
        self._services[service_type] = descriptor
        logger.debug(f"注册工厂服务: {service_type.__name__}")
        return self
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'DependencyContainer':
        """注册实例"""
        descriptor = ServiceDescriptor(service_type, type(instance), ServiceLifetime.SINGLETON)
        descriptor.instance = instance
        self._services[service_type] = descriptor
        logger.debug(f"注册实例服务: {service_type.__name__}")
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """解析服务"""
        if service_type not in self._services:
            raise ValueError(f"服务未注册: {service_type.__name__}")
        
        descriptor = self._services[service_type]
        
        # 单例模式
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if descriptor.instance is None:
                descriptor.instance = self._create_instance(descriptor)
            return descriptor.instance
        
        # 作用域模式
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if self._current_scope is None:
                raise ValueError("作用域服务需要在作用域内解析")
            
            scope_instances = self._scoped_instances.get(self._current_scope, {})
            if service_type not in scope_instances:
                instance = self._create_instance(descriptor)
                scope_instances[service_type] = instance
                self._scoped_instances[self._current_scope] = scope_instances
            
            return scope_instances[service_type]
        
        # 瞬态模式
        else:
            return self._create_instance(descriptor)
    
    def _create_instance(self, descriptor: ServiceDescriptor):
        """创建服务实例"""
        try:
            if descriptor.factory:
                # 使用工厂方法
                return self._invoke_with_injection(descriptor.factory)
            else:
                # 使用构造函数
                return self._invoke_with_injection(descriptor.implementation)
        except Exception as e:
            logger.error(f"创建服务实例失败: {descriptor.service_type.__name__}, 错误: {e}")
            raise
    
    def _invoke_with_injection(self, func_or_class):
        """调用函数或构造函数并注入依赖"""
        # 获取函数签名
        sig = inspect.signature(func_or_class)
        kwargs = {}
        
        # 为每个参数解析依赖
        for param_name, param in sig.parameters.items():
            if param.annotation != inspect.Parameter.empty:
                try:
                    kwargs[param_name] = self.resolve(param.annotation)
                except ValueError:
                    # 如果无法解析依赖，检查是否有默认值
                    if param.default == inspect.Parameter.empty:
                        logger.warning(f"无法解析依赖: {param_name}: {param.annotation}")
                    # 有默认值的参数跳过
        
        return func_or_class(**kwargs)
    
    def create_scope(self, scope_id: str = None) -> 'ServiceScope':
        """创建服务作用域"""
        if scope_id is None:
            import uuid
            scope_id = str(uuid.uuid4())
        
        return ServiceScope(self, scope_id)
    
    def _enter_scope(self, scope_id: str):
        """进入作用域"""
        self._current_scope = scope_id
        if scope_id not in self._scoped_instances:
            self._scoped_instances[scope_id] = {}
    
    def _exit_scope(self, scope_id: str):
        """退出作用域"""
        if scope_id in self._scoped_instances:
            # 清理作用域实例
            scope_instances = self._scoped_instances.pop(scope_id)
            for instance in scope_instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logger.error(f"清理作用域实例失败: {e}")
        
        self._current_scope = None
    
    def is_registered(self, service_type: Type) -> bool:
        """检查服务是否已注册"""
        return service_type in self._services
    
    def get_services(self) -> Dict[Type, ServiceDescriptor]:
        """获取所有注册的服务"""
        return self._services.copy()
    
    def clear(self):
        """清空容器"""
        # 清理单例实例
        for descriptor in self._services.values():
            if descriptor.instance and hasattr(descriptor.instance, 'dispose'):
                try:
                    descriptor.instance.dispose()
                except Exception as e:
                    logger.error(f"清理单例实例失败: {e}")
        
        # 清理作用域实例
        for scope_instances in self._scoped_instances.values():
            for instance in scope_instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logger.error(f"清理作用域实例失败: {e}")
        
        self._services.clear()
        self._scoped_instances.clear()
        self._current_scope = None


class ServiceScope:
    """服务作用域上下文管理器"""
    
    def __init__(self, container: DependencyContainer, scope_id: str):
        self.container = container
        self.scope_id = scope_id
    
    def __enter__(self):
        self.container._enter_scope(self.scope_id)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.container._exit_scope(self.scope_id)


# 全局依赖容器
container = DependencyContainer()


# 装饰器：依赖注入
def inject(func):
    """依赖注入装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 获取函数签名
        sig = inspect.signature(func)
        bound_args = sig.bind_partial(*args, **kwargs)
        
        # 为未绑定的参数注入依赖
        for param_name, param in sig.parameters.items():
            if param_name not in bound_args.arguments and param.annotation != inspect.Parameter.empty:
                try:
                    dependency = container.resolve(param.annotation)
                    bound_args.arguments[param_name] = dependency
                except ValueError:
                    # 如果无法解析依赖，检查是否有默认值
                    if param.default == inspect.Parameter.empty:
                        raise ValueError(f"无法解析依赖: {param_name}: {param.annotation}")
        
        return func(*bound_args.args, **bound_args.kwargs)
    
    return wrapper


# 装饰器：服务注册
def service(lifetime: str = ServiceLifetime.TRANSIENT):
    """服务注册装饰器"""
    def decorator(cls):
        if lifetime == ServiceLifetime.SINGLETON:
            container.register_singleton(cls, cls)
        elif lifetime == ServiceLifetime.SCOPED:
            container.register_scoped(cls, cls)
        else:
            container.register_transient(cls, cls)
        
        return cls
    
    return decorator


# 抽象基类：可释放资源
class IDisposable(ABC):
    """可释放资源接口"""
    
    @abstractmethod
    def dispose(self):
        """释放资源"""
        pass


# 抽象基类：服务接口
class IService(ABC):
    """服务基接口"""
    pass


# 示例服务接口
class IUserService(IService):
    """用户服务接口"""
    
    @abstractmethod
    def get_user_by_id(self, user_id: str):
        pass
    
    @abstractmethod
    def create_user(self, user_data: dict):
        pass


class ISpaceService(IService):
    """空间服务接口"""
    
    @abstractmethod
    def get_space_by_id(self, space_id: str):
        pass
    
    @abstractmethod
    def create_space(self, space_data: dict):
        pass


class INotificationService(IService):
    """通知服务接口"""
    
    @abstractmethod
    def send_notification(self, user_id: str, message: str):
        pass


# 配置函数：注册默认服务
def configure_services():
    """配置默认服务"""
    from django.core.cache import cache
    from django.db import connection
    
    # 注册Django内置服务
    container.register_instance(type(cache), cache)
    container.register_instance(type(connection), connection)
    
    logger.info("默认服务配置完成")


# 初始化时配置服务
configure_services()