"""
核心基础模型 - 低耦合的基础设施模型
职责：提供所有业务域共用的基础模型和字段
"""

from django.db import models
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import uuid
import json


class SoftDeleteManager(models.Manager):
    """软删除管理器"""

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


class BaseModel(models.Model):
    """所有模型的基类 - 提供通用字段和方法"""

    id = models.BigAutoField(primary_key=True, verbose_name='ID')
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')

    # 管理器
    objects = models.Manager()
    active_objects = SoftDeleteManager()

    class Meta:
        abstract = True
        ordering = ['-created_at']

    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.save(update_fields=['is_deleted', 'updated_at'])

    def to_dict(self):
        """转换为字典"""
        data = {}
        for field in self._meta.fields:
            value = getattr(self, field.name)
            if isinstance(value, timezone.datetime):
                value = value.isoformat()
            elif isinstance(value, uuid.UUID):
                value = str(value)
            data[field.name] = value
        return data

    def __str__(self):
        return f"{self.__class__.__name__}({self.id})"


class TimestampedModel(models.Model):
    """时间戳模型 - 只包含时间字段"""
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        abstract = True


class AuditModel(BaseModel):
    """审计模型 - 包含创建者和更新者信息"""
    
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建者'
    )
    updated_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='更新者'
    )
    
    class Meta:
        abstract = True


class StatusModel(BaseModel):
    """状态模型 - 包含状态字段"""
    
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('inactive', '非活跃'),
        ('pending', '待处理'),
        ('archived', '已归档'),
    ]
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name='状态'
    )
    
    class Meta:
        abstract = True


class CategoryModel(BaseModel):
    """分类模型 - 通用分类基类"""
    
    name = models.CharField(max_length=100, verbose_name='名称')
    description = models.TextField(blank=True, verbose_name='描述')
    icon_url = models.URLField(blank=True, verbose_name='图标URL')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='颜色')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    class Meta:
        abstract = True
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class TagModel(BaseModel):
    """标签模型 - 通用标签基类"""
    
    name = models.CharField(max_length=50, unique=True, verbose_name='标签名')
    color = models.CharField(max_length=7, default='#6c757d', verbose_name='颜色')
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    
    class Meta:
        abstract = True
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class MetadataModel(BaseModel):
    """元数据模型 - 通用元数据基类"""
    
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    
    class Meta:
        abstract = True
    
    def set_metadata(self, key, value):
        """设置元数据"""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
        self.save(update_fields=['metadata'])
    
    def get_metadata(self, key, default=None):
        """获取元数据"""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)