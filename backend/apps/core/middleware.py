"""
核心中间件 - 提供通用的请求处理功能
职责：请求ID追踪、请求日志、性能监控等
"""

import time
import uuid
import logging
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.conf import settings
# from .models import ActivityLog  # 暂时移除，模型不存在
# from .responses import APIResponse  # 暂时移除

logger = logging.getLogger(__name__)


class RequestIdMiddleware(MiddlewareMixin):
    """请求ID中间件 - 为每个请求生成唯一ID"""
    
    def process_request(self, request):
        """处理请求"""
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.request_id = request_id
        
        # 设置响应头
        request.META['HTTP_X_REQUEST_ID'] = request_id
        
        return None
    
    def process_response(self, request, response):
        """处理响应"""
        # 添加请求ID到响应头
        if hasattr(request, 'request_id'):
            response['X-Request-ID'] = request.request_id
        
        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件 - 记录请求和响应信息"""
    
    def process_request(self, request):
        """处理请求"""
        request.start_time = time.time()
        
        # 记录请求信息
        logger.info(
            f"请求开始: {request.method} {request.path}, "
            f"用户: {getattr(request.user, 'username', 'Anonymous')}, "
            f"IP: {self.get_client_ip(request)}, "
            f"请求ID: {getattr(request, 'request_id', 'Unknown')}"
        )
        
        return None
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 记录响应信息
            logger.info(
                f"请求完成: {request.method} {request.path}, "
                f"状态码: {response.status_code}, "
                f"耗时: {duration:.3f}s, "
                f"请求ID: {getattr(request, 'request_id', 'Unknown')}"
            )
            
            # 记录慢请求
            if duration > getattr(settings, 'SLOW_REQUEST_THRESHOLD', 1.0):
                logger.warning(
                    f"慢请求警告: {request.method} {request.path}, "
                    f"耗时: {duration:.3f}s, "
                    f"请求ID: {getattr(request, 'request_id', 'Unknown')}"
                )
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PerformanceMiddleware(MiddlewareMixin):
    """性能监控中间件"""
    
    def process_request(self, request):
        """处理请求"""
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 添加性能头
            response['X-Response-Time'] = f"{duration:.3f}s"
            
            # 记录性能指标
            self.record_performance_metrics(request, response, duration)
        
        return response
    
    def record_performance_metrics(self, request, response, duration):
        """记录性能指标"""
        try:
            # 这里可以集成到监控系统，如Prometheus
            metrics = {
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration': duration,
                'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
                'request_id': getattr(request, 'request_id', None)
            }
            
            # 发送到监控系统
            # prometheus_client.record_request_metrics(metrics)
            
        except Exception as e:
            logger.error(f"记录性能指标失败: {e}")


class ActivityLoggingMiddleware(MiddlewareMixin):
    """活动日志中间件 - 记录用户活动"""
    
    def process_response(self, request, response):
        """处理响应"""
        # 只记录成功的POST、PUT、PATCH、DELETE请求
        if (
            request.method in ['POST', 'PUT', 'PATCH', 'DELETE'] and
            200 <= response.status_code < 300 and
            hasattr(request, 'user') and
            request.user.is_authenticated
        ):
            try:
                self.log_activity(request, response)
            except Exception as e:
                logger.error(f"记录活动日志失败: {e}")
        
        return response
    
    def log_activity(self, request, response):
        """记录活动"""
        # 确定操作类型
        action_map = {
            'POST': 'create',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete'
        }
        action = action_map.get(request.method, 'unknown')
        
        # 获取IP地址和用户代理
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # 创建活动日志（暂时使用普通日志）
        # from .tasks import create_activity_log  # 暂时禁用Celery任务
        logger.info(
            f"用户活动 - 用户ID: {request.user.id}, "
            f"操作: {action}, "
            f"路径: {request.path}, "
            f"IP: {ip_address}, "
            f"请求ID: {getattr(request, 'request_id', None)}"
        )
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CorsMiddleware(MiddlewareMixin):
    """CORS中间件 - 处理跨域请求"""
    
    def process_response(self, request, response):
        """处理响应"""
        # 添加CORS头
        response['Access-Control-Allow-Origin'] = self.get_allowed_origin(request)
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = (
            'Accept, Accept-Language, Content-Language, Content-Type, '
            'Authorization, X-Requested-With, X-Request-ID'
        )
        response['Access-Control-Expose-Headers'] = 'X-Request-ID, X-Response-Time'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        
        return response
    
    def get_allowed_origin(self, request):
        """获取允许的源"""
        origin = request.META.get('HTTP_ORIGIN')
        allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        
        if origin in allowed_origins:
            return origin
        
        # 开发环境允许localhost
        if settings.DEBUG and origin and 'localhost' in origin:
            return origin
        
        return '*'


class SecurityMiddleware(MiddlewareMixin):
    """安全中间件 - 添加安全头"""
    
    def process_response(self, request, response):
        """处理响应"""
        # 添加安全头
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # HTTPS相关头（生产环境）
        if not settings.DEBUG:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' data:; "
                "connect-src 'self' ws: wss:;"
            )
        
        return response


class RateLimitMiddleware(MiddlewareMixin):
    """限流中间件 - 基于IP和用户的请求限流"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """处理请求"""
        # 检查是否需要限流
        if self.should_rate_limit(request):
            # 获取限流键
            rate_limit_key = self.get_rate_limit_key(request)
            
            # 检查限流
            if self.is_rate_limited(rate_limit_key):
                return self.rate_limit_response()
        
        return None
    
    def should_rate_limit(self, request):
        """判断是否需要限流"""
        # 跳过静态文件和健康检查
        if request.path.startswith('/static/') or request.path == '/health/':
            return False
        
        # 跳过管理员用户
        if hasattr(request, 'user') and request.user.is_superuser:
            return False
        
        return True
    
    def get_rate_limit_key(self, request):
        """获取限流键"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"rate_limit:user:{request.user.id}"
        else:
            ip = self.get_client_ip(request)
            return f"rate_limit:ip:{ip}"
    
    def is_rate_limited(self, key):
        """检查是否被限流"""
        try:
            from django.core.cache import cache
            
            # 获取配置
            limit = getattr(settings, 'RATE_LIMIT_REQUESTS', 100)
            window = getattr(settings, 'RATE_LIMIT_WINDOW', 60)
            
            # 获取当前计数
            current = cache.get(key, 0)
            
            if current >= limit:
                return True
            
            # 增加计数
            cache.set(key, current + 1, window)
            return False
            
        except Exception as e:
            logger.error(f"限流检查失败: {e}")
            return False
    
    def rate_limit_response(self):
        """限流响应"""
        return JsonResponse({
            'success': False,
            'message': "请求过于频繁，请稍后再试",
            'error_code': 'RATE_LIMITED',
            'retry_after': 60
        }, status=429)
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class HealthCheckMiddleware(MiddlewareMixin):
    """健康检查中间件"""
    
    def process_request(self, request):
        """处理请求"""
        if request.path == '/health/':
            return self.health_check_response()
        return None
    
    def health_check_response(self):
        """健康检查响应"""
        try:
            # 检查数据库连接
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            # 检查缓存连接
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            cache_status = cache.get('health_check')
            
            # 返回健康状态
            return JsonResponse({
                'status': 'healthy',
                'database': 'ok',
                'cache': 'ok' if cache_status == 'ok' else 'error',
                'timestamp': time.time()
            })
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return JsonResponse({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }, status=500)