"""
事件驱动系统 - 实现模块间低耦合通信
职责：提供领域事件发布订阅机制，实现业务模块解耦
"""

import logging
import asyncio
from typing import Dict, Any, List, Callable, Optional, Type
from dataclasses import dataclass, field
from django.utils import timezone
from django.conf import settings
from concurrent.futures import ThreadPoolExecutor
import json
import uuid

logger = logging.getLogger(__name__)


@dataclass
class DomainEvent:
    """领域事件基类"""
    event_type: str
    aggregate_id: str
    data: Dict[str, Any]
    timestamp: str = field(default_factory=lambda: timezone.now().isoformat())
    version: int = 1
    correlation_id: Optional[str] = None
    causation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'aggregate_id': self.aggregate_id,
            'data': self.data,
            'timestamp': self.timestamp,
            'version': self.version,
            'correlation_id': self.correlation_id,
            'causation_id': self.causation_id,
            'metadata': self.metadata
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DomainEvent':
        """从字典创建事件"""
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'DomainEvent':
        """从JSON字符串创建事件"""
        data = json.loads(json_str)
        return cls.from_dict(data)


class EventHandler:
    """事件处理器基类"""
    
    def __init__(self, handler_func: Callable, async_handler: bool = False, retry_count: int = 3):
        self.handler_func = handler_func
        self.async_handler = async_handler
        self.retry_count = 0
        self.max_retries = retry_count
        self.handler_name = f"{handler_func.__module__}.{handler_func.__name__}"
    
    async def handle(self, event: DomainEvent) -> bool:
        """处理事件"""
        try:
            if self.async_handler:
                if asyncio.iscoroutinefunction(self.handler_func):
                    await self.handler_func(event)
                else:
                    # 在线程池中执行同步函数
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, self.handler_func, event)
            else:
                self.handler_func(event)
            
            logger.debug(f"事件处理成功: {event.event_type} by {self.handler_name}")
            return True
            
        except Exception as e:
            self.retry_count += 1
            logger.error(
                f"事件处理失败: {event.event_type}, "
                f"处理器: {self.handler_name}, "
                f"错误: {str(e)}, "
                f"重试次数: {self.retry_count}/{self.max_retries}"
            )
            
            if self.retry_count < self.max_retries:
                # 指数退避重试
                await asyncio.sleep(2 ** self.retry_count)
                return await self.handle(event)
            
            # 记录失败事件到死信队列
            self._send_to_dead_letter_queue(event, e)
            return False
    
    def _send_to_dead_letter_queue(self, event: DomainEvent, error: Exception):
        """发送失败事件到死信队列"""
        try:
            from .models import FailedEvent
            FailedEvent.objects.create(
                event_id=event.event_id,
                event_type=event.event_type,
                event_data=event.to_dict(),
                handler_name=self.handler_name,
                error_message=str(error),
                retry_count=self.retry_count
            )
        except Exception as e:
            logger.error(f"无法保存失败事件到死信队列: {e}")


class EventBus:
    """事件总线 - 实现模块间低耦合通信"""
    
    def __init__(self):
        self._handlers: Dict[str, List[EventHandler]] = {}
        self._middleware: List[Callable] = []
        self._event_store: List[DomainEvent] = []
        self._max_store_size = 1000
        self._executor = ThreadPoolExecutor(max_workers=10)
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'handlers_registered': 0
        }
    
    def subscribe(self, event_type: str, handler: Callable, async_handler: bool = False, retry_count: int = 3):
        """订阅事件"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        event_handler = EventHandler(handler, async_handler, retry_count)
        self._handlers[event_type].append(event_handler)
        self._stats['handlers_registered'] += 1
        
        logger.info(f"事件订阅成功: {event_type} -> {event_handler.handler_name}")
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """取消订阅事件"""
        if event_type in self._handlers:
            self._handlers[event_type] = [
                h for h in self._handlers[event_type] 
                if h.handler_func != handler
            ]
            
            if not self._handlers[event_type]:
                del self._handlers[event_type]
            
            logger.info(f"取消事件订阅: {event_type} -> {handler.__name__}")
    
    def add_middleware(self, middleware: Callable):
        """添加中间件"""
        self._middleware.append(middleware)
        logger.info(f"添加事件中间件: {middleware.__name__}")
    
    def publish(self, event: DomainEvent):
        """发布事件（同步）"""
        logger.info(f"发布事件: {event.event_type} (ID: {event.event_id})")
        self._stats['events_published'] += 1
        
        # 存储事件
        self._store_event(event)
        
        # 应用中间件
        processed_event = self._apply_middleware(event)
        if processed_event is None:
            logger.warning(f"事件被中间件拦截: {event.event_type}")
            return
        
        # 获取处理器
        handlers = self._handlers.get(event.event_type, [])
        if not handlers:
            logger.warning(f"没有找到事件处理器: {event.event_type}")
            return
        
        # 同步处理器
        sync_handlers = [h for h in handlers if not h.async_handler]
        for handler in sync_handlers:
            try:
                handler.handler_func(processed_event)
                self._stats['events_processed'] += 1
                logger.debug(f"同步事件处理成功: {event.event_type}")
            except Exception as e:
                self._stats['events_failed'] += 1
                logger.error(f"同步事件处理失败: {event.event_type}, 错误: {str(e)}")
        
        # 异步处理器
        async_handlers = [h for h in handlers if h.async_handler]
        if async_handlers:
            asyncio.create_task(self._handle_async_events(processed_event, async_handlers))
    
    async def publish_async(self, event: DomainEvent):
        """发布事件（异步）"""
        logger.info(f"异步发布事件: {event.event_type} (ID: {event.event_id})")
        self._stats['events_published'] += 1
        
        # 存储事件
        self._store_event(event)
        
        # 应用中间件
        processed_event = await self._apply_middleware_async(event)
        if processed_event is None:
            logger.warning(f"事件被中间件拦截: {event.event_type}")
            return
        
        # 获取处理器
        handlers = self._handlers.get(event.event_type, [])
        if not handlers:
            logger.warning(f"没有找到事件处理器: {event.event_type}")
            return
        
        # 并发处理所有处理器
        tasks = [handler.handle(processed_event) for handler in handlers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计处理结果
        success_count = sum(1 for r in results if r is True)
        failure_count = len(results) - success_count
        
        self._stats['events_processed'] += success_count
        self._stats['events_failed'] += failure_count
        
        logger.info(
            f"异步事件处理完成: {event.event_type}, "
            f"成功: {success_count}, 失败: {failure_count}"
        )
    
    def _apply_middleware(self, event: DomainEvent) -> Optional[DomainEvent]:
        """应用中间件（同步）"""
        current_event = event
        for middleware in self._middleware:
            try:
                current_event = middleware(current_event)
                if current_event is None:
                    return None
            except Exception as e:
                logger.error(f"中间件处理失败: {middleware.__name__}, 错误: {str(e)}")
                return None
        return current_event
    
    async def _apply_middleware_async(self, event: DomainEvent) -> Optional[DomainEvent]:
        """应用中间件（异步）"""
        current_event = event
        for middleware in self._middleware:
            try:
                if asyncio.iscoroutinefunction(middleware):
                    current_event = await middleware(current_event)
                else:
                    current_event = middleware(current_event)
                
                if current_event is None:
                    return None
            except Exception as e:
                logger.error(f"中间件处理失败: {middleware.__name__}, 错误: {str(e)}")
                return None
        return current_event
    
    async def _handle_async_events(self, event: DomainEvent, handlers: List[EventHandler]):
        """处理异步事件"""
        tasks = [handler.handle(event) for handler in handlers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        failure_count = len(results) - success_count
        
        self._stats['events_processed'] += success_count
        self._stats['events_failed'] += failure_count
        
        logger.info(
            f"异步事件处理完成: {event.event_type}, "
            f"成功: {success_count}, 失败: {failure_count}"
        )
    
    def _store_event(self, event: DomainEvent):
        """存储事件"""
        self._event_store.append(event)
        
        # 限制存储大小
        if len(self._event_store) > self._max_store_size:
            self._event_store = self._event_store[-self._max_store_size:]
    
    def get_events(self, event_type: Optional[str] = None, limit: int = 100) -> List[DomainEvent]:
        """获取事件"""
        events = self._event_store
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return events[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'event_types': list(self._handlers.keys()),
            'total_handlers': sum(len(handlers) for handlers in self._handlers.values()),
            'stored_events': len(self._event_store)
        }
    
    def clear_events(self):
        """清空事件存储"""
        self._event_store.clear()
        logger.info("事件存储已清空")
    
    def get_handler_count(self, event_type: str) -> int:
        """获取事件处理器数量"""
        return len(self._handlers.get(event_type, []))
    
    def get_all_event_types(self) -> List[str]:
        """获取所有事件类型"""
        return list(self._handlers.keys())


# 全局事件总线实例
event_bus = EventBus()


# 事件中间件
def logging_middleware(event: DomainEvent) -> DomainEvent:
    """日志中间件"""
    logger.info(f"事件日志: {event.event_type} - {event.aggregate_id}")
    return event


def validation_middleware(event: DomainEvent) -> Optional[DomainEvent]:
    """验证中间件"""
    if not event.event_type:
        logger.error("事件类型不能为空")
        return None
    
    if not event.aggregate_id:
        logger.error("聚合ID不能为空")
        return None
    
    return event


def metrics_middleware(event: DomainEvent) -> DomainEvent:
    """指标中间件"""
    # 这里可以记录事件指标
    # 例如：发送到监控系统
    return event


def audit_middleware(event: DomainEvent) -> DomainEvent:
    """审计中间件"""
    try:
        from .models import EventAuditLog
        EventAuditLog.objects.create(
            event_id=event.event_id,
            event_type=event.event_type,
            aggregate_id=event.aggregate_id,
            event_data=event.to_dict(),
            timestamp=timezone.now()
        )
    except Exception as e:
        logger.error(f"审计日志记录失败: {e}")
    
    return event


# 注册默认中间件
event_bus.add_middleware(validation_middleware)
event_bus.add_middleware(logging_middleware)

if hasattr(settings, 'ENABLE_EVENT_METRICS') and settings.ENABLE_EVENT_METRICS:
    event_bus.add_middleware(metrics_middleware)

if hasattr(settings, 'ENABLE_EVENT_AUDIT') and settings.ENABLE_EVENT_AUDIT:
    event_bus.add_middleware(audit_middleware)


# 装饰器：事件处理器
def event_handler(event_type: str, async_handler: bool = False, retry_count: int = 3):
    """事件处理器装饰器"""
    def decorator(func):
        event_bus.subscribe(event_type, func, async_handler, retry_count)
        return func
    return decorator


# 装饰器：事件发布器
def event_publisher(event_type: str):
    """事件发布器装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 创建事件
            event = DomainEvent(
                event_type=event_type,
                aggregate_id=str(result.get('id', '')),
                data=result,
                metadata={
                    'function': func.__name__,
                    'module': func.__module__
                }
            )
            
            # 发布事件
            event_bus.publish(event)
            
            return result
        return wrapper
    return decorator


# 特定业务事件类
class UserRegisteredEvent(DomainEvent):
    """用户注册事件"""
    def __init__(self, user_id: str, email: str, username: str, **kwargs):
        super().__init__(
            event_type='user.registered',
            aggregate_id=user_id,
            data={
                'user_id': user_id,
                'email': email,
                'username': username,
                **kwargs
            }
        )


class SpaceCreatedEvent(DomainEvent):
    """空间创建事件"""
    def __init__(self, space_id: str, owner_id: str, space_name: str, **kwargs):
        super().__init__(
            event_type='space.created',
            aggregate_id=space_id,
            data={
                'space_id': space_id,
                'owner_id': owner_id,
                'space_name': space_name,
                **kwargs
            }
        )


class MessageSentEvent(DomainEvent):
    """消息发送事件"""
    def __init__(self, message_id: str, sender_id: str, target_type: str, target_id: str, **kwargs):
        super().__init__(
            event_type='message.sent',
            aggregate_id=message_id,
            data={
                'message_id': message_id,
                'sender_id': sender_id,
                'target_type': target_type,
                'target_id': target_id,
                **kwargs
            }
        )