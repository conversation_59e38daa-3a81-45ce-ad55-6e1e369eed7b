"""
分页器 - 标准化分页功能
"""

from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from collections import OrderedDict


class StandardResultsSetPagination(PageNumberPagination):
    """
    标准分页器
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        """
        返回标准化的分页响应
        """
        return Response(OrderedDict([
            ('success', True),
            ('message', '获取成功'),
            ('data', data),
            ('pagination', OrderedDict([
                ('page', self.page.number),
                ('page_size', self.page.paginator.per_page),
                ('total_pages', self.page.paginator.num_pages),
                ('total_items', self.page.paginator.count),
                ('has_next', self.page.has_next()),
                ('has_previous', self.page.has_previous()),
                ('next_page', self.page.next_page_number() if self.page.has_next() else None),
                ('previous_page', self.page.previous_page_number() if self.page.has_previous() else None),
            ])),
            ('timestamp', self.get_timestamp())
        ]))
    
    def get_timestamp(self):
        """获取时间戳"""
        import time
        return time.time()


class LargeResultsSetPagination(PageNumberPagination):
    """
    大数据集分页器
    """
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200
    
    def get_paginated_response(self, data):
        """
        返回标准化的分页响应
        """
        return Response(OrderedDict([
            ('success', True),
            ('message', '获取成功'),
            ('data', data),
            ('pagination', OrderedDict([
                ('page', self.page.number),
                ('page_size', self.page.paginator.per_page),
                ('total_pages', self.page.paginator.num_pages),
                ('total_items', self.page.paginator.count),
                ('has_next', self.page.has_next()),
                ('has_previous', self.page.has_previous()),
            ])),
            ('timestamp', self.get_timestamp())
        ]))
    
    def get_timestamp(self):
        """获取时间戳"""
        import time
        return time.time()


class SmallResultsSetPagination(PageNumberPagination):
    """
    小数据集分页器
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 50
    
    def get_paginated_response(self, data):
        """
        返回标准化的分页响应
        """
        return Response(OrderedDict([
            ('success', True),
            ('message', '获取成功'),
            ('data', data),
            ('pagination', OrderedDict([
                ('page', self.page.number),
                ('page_size', self.page.paginator.per_page),
                ('total_pages', self.page.paginator.num_pages),
                ('total_items', self.page.paginator.count),
                ('has_next', self.page.has_next()),
                ('has_previous', self.page.has_previous()),
            ])),
            ('timestamp', self.get_timestamp())
        ]))
    
    def get_timestamp(self):
        """获取时间戳"""
        import time
        return time.time()
