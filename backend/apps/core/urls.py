"""
核心应用URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import APIInfoView, HealthCheckView, SystemStatsView

# 创建路由器
router = DefaultRouter()

# URL模式
urlpatterns = [
    # API信息页面（仅在核心API路径下）
    path('info/', APIInfoView.as_view(), name='api-info'),

    # 健康检查
    path('health/', HealthCheckView.as_view(), name='health-check'),

    # 系统统计
    path('stats/', SystemStatsView.as_view(), name='system-stats'),

    # 路由器URL
    path('', include(router.urls)),
]

# 应用命名空间
app_name = 'core'
