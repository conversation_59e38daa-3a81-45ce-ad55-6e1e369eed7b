# SOIC - Social Innovation Community Backend

一个基于 Django 的现代化社交平台后端系统，采用领域驱动设计 (DDD) 和微服务架构思想。

## 🚀 项目特点

- **领域驱动设计**：高内聚、低耦合的业务域划分
- **事件驱动架构**：基于领域事件的松耦合通信
- **微服务就绪**：支持独立部署和水平扩展
- **现代化技术栈**：Django 4.2+ + DRF + Celery + Redis + MySQL
- **完整的业务功能**：用户、社交、消息、内容、经济五大业务域

## 📋 功能模块

### 🔐 用户域 (apps/users/)
- 用户注册、登录、个人资料管理
- JWT认证、权限控制
- 用户偏好设置、通知管理
- 账户安全功能

### 👥 社交域 (apps/social/)
- 好友系统、关注/粉丝
- 群组管理、空间系统
- 社交关系图谱
- 活动和互动功能

### 💬 消息域 (apps/messaging/)
- 实时聊天（私聊、群聊）
- 多媒体消息支持
- 在线状态管理
- WebSocket实时通信

### 📝 内容域 (apps/content/)
- 帖子发布、编辑、删除
- 多级评论系统
- 内容审核机制
- 个性化推荐算法

### 💰 经济域 (apps/economy/)
- 多币种虚拟钱包
- 商品管理、订单系统
- 支付处理、交易记录
- 优惠券、礼品系统

## 🛠️ 技术栈

### 后端框架
- **Django 4.2+** - Web框架
- **Django REST Framework** - API开发
- **Celery** - 异步任务处理
- **Redis** - 缓存和消息队列
- **MySQL 8.0+** - 主数据库

### 开发工具
- **Docker** - 容器化部署
- **pytest** - 单元测试
- **Black** - 代码格式化
- **flake8** - 代码质量检查

## 🏗️ 项目结构

```
soic/backend/
├── apps/
│   ├── core/           # 核心基础设施
│   ├── users/          # 用户域
│   ├── social/         # 社交域
│   ├── messaging/      # 消息域
│   ├── content/        # 内容域
│   └── economy/        # 经济域
├── config/             # 项目配置
├── requirements/       # 依赖管理
├── scripts/           # 脚本文件
├── docker/            # Docker配置
└── manage.py          # Django管理脚本
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- MySQL 8.0+
- Redis 6+ (可选，开发环境可使用内存缓存)

### 一键设置（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行执行
setup.bat
```

**Linux/Mac用户：**
```bash
chmod +x setup.sh
./setup.sh
```

### 手动设置

1. **克隆项目**
```bash
git clone <repository-url>
cd soic/backend
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements/local.txt
```

4. **配置MySQL数据库**
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE soic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

5. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息：
# DB_NAME=soic
# DB_USER=root
# DB_PASSWORD=xiaoxiao123
# DB_HOST=localhost
# DB_PORT=3306
```

6. **数据库迁移和初始化**
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py init_data
```

7. **创建超级用户（可选）**
```bash
python manage.py createsuperuser
```

8. **启动开发服务器**
```bash
python manage.py runserver
# 或使用启动脚本
scripts/start_dev.bat    # Windows
scripts/start_dev.sh     # Linux/Mac
```

### Docker 部署

1. **构建并启动服务**
```bash
docker-compose up -d
```

2. **执行数据库迁移**
```bash
docker-compose exec web python manage.py migrate
```

3. **创建超级用户**
```bash
docker-compose exec web python manage.py createsuperuser
```

## 📚 API 文档

启动服务后，访问以下地址查看API文档：

- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **管理后台**: http://localhost:8000/admin/
- **健康检查**: http://localhost:8000/api/v1/core/health/

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定应用测试
pytest apps/users/tests/

# 生成覆盖率报告
pytest --cov=apps --cov-report=html
```

### 代码质量检查
```bash
# 代码格式化
black .

# 代码质量检查
flake8

# 类型检查
mypy apps/
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DEBUG` | 调试模式 | `False` |
| `SECRET_KEY` | Django密钥 | - |
| `DB_NAME` | 数据库名 | `soic` |
| `DB_USER` | 数据库用户 | `root` |
| `DB_PASSWORD` | 数据库密码 | `xiaoxiao123` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `3306` |
| `REDIS_URL` | Redis连接 | `redis://localhost:6379/1` |
| `FRONTEND_URL` | 前端地址 | `http://localhost:3000` |

### 数据库配置

项目使用MySQL 8.0+作为主数据库：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'soic',
        'USER': 'root',
        'PASSWORD': 'xiaoxiao123',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

## 📈 性能优化

### 缓存策略
- **Redis缓存**：API响应缓存
- **数据库查询优化**：使用select_related和prefetch_related
- **静态文件CDN**：生产环境使用CDN

### 异步任务
- **Celery任务队列**：处理耗时操作
- **定时任务**：数据统计、清理任务
- **事件处理**：异步事件处理

## 🔒 安全特性

- **JWT认证**：无状态认证机制
- **权限控制**：基于角色的访问控制
- **数据验证**：严格的输入验证
- **SQL注入防护**：ORM参数化查询
- **XSS防护**：输出数据转义
- **CSRF防护**：跨站请求伪造防护

## 📊 监控和日志

### 日志系统
- **结构化日志**：JSON格式日志
- **日志级别**：DEBUG、INFO、WARNING、ERROR
- **业务日志**：关键业务操作记录
- **性能日志**：API响应时间监控

### 健康检查
- **系统健康**: `/api/v1/core/health/`
- **系统统计**: `/api/v1/core/stats/`

## 🚀 部署指南

### 生产环境部署

1. **环境准备**
```bash
# 安装系统依赖
sudo apt-get update
sudo apt-get install python3-pip mysql-server redis-server nginx

# 创建应用用户
sudo useradd -m -s /bin/bash soic
```

2. **应用部署**
```bash
# 克隆代码
git clone <repository-url> /opt/soic
cd /opt/soic/backend

# 安装依赖
pip install -r requirements/production.txt

# 配置环境变量
cp .env.example .env

# 数据库迁移
python manage.py migrate

# 收集静态文件
python manage.py collectstatic
```

3. **服务配置**
```bash
# 配置 Gunicorn
sudo systemctl enable gunicorn
sudo systemctl start gunicorn

# 配置 Nginx
sudo systemctl reload nginx

# 配置 Celery
sudo systemctl enable celery
sudo systemctl start celery
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 添加适当的类型注解
- 编写完整的文档字符串
- 确保测试覆盖率 > 80%

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目主页**: https://github.com/your-org/soic
- **问题反馈**: https://github.com/your-org/soic/issues
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**SOIC** - 构建下一代社交创新社区 🌟