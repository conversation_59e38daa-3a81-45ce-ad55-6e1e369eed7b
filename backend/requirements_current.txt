# 当前环境的依赖包版本
# 生成时间: 1753895929.9075332

amqp==5.3.1
asgiref==3.9.1
attrs==25.3.0
autobahn==24.4.2
Automat==25.4.16
billiard==4.2.1
celery==5.3.4
celery-stubs==0.1.3
certifi==2025.7.14
cffi @ file:///C:/b/abs_29_b57if3f/croot/cffi_1736184144340/work
channels==4.3.0
channels_redis==4.3.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1.2
click-repl==0.3.0
colorama==0.4.6
constantly==23.10.4
cryptography @ file:///C:/b/abs_cbo_0az6za/croot/cryptography_1753084589654/work
daphne==4.2.1
Django @ file:///C:/b/abs_732pccxzov/croot/django_1745263601709/work
django-cors-headers==4.7.0
django-debug-toolbar==6.0.0
django-extensions==4.1
django-filter==25.1
django-redis==6.0.0
djangorestframework==3.16.0
djangorestframework_simplejwt==5.5.1
drf-spectacular==0.28.0
greenlet @ file:///C:/b/abs_bbl_18a2tm/croot/greenlet_1733860081977/work
hyperlink==21.0.0
idna==3.10
incremental==24.7.2
inflection==0.5.1
iniconfig==2.1.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kombu==5.5.4
msgpack==1.1.1
mysqlclient==2.2.7
packaging==25.0
pillow==11.3.0
pluggy==1.6.0
prompt_toolkit==3.0.51
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
Pygments==2.19.2
PyJWT==2.10.1
pyOpenSSL==25.1.0
pytest==8.4.1
python-dateutil==2.9.0.post0
python-decouple==3.8
pytz @ file:///C:/b/abs_f8wdzeix0n/croot/pytz_1752135878094/work
PyYAML==6.0.2
redis==6.2.0
referencing==0.36.2
requests==2.32.4
rpds-py==0.26.0
service-identity==24.2.0
setuptools==78.1.1
simplejson @ file:///C:/b/abs_c56tlnb2q_/croot/simplejson_1736544707806/work
six==1.17.0
SQLAlchemy @ file:///C:/b/abs_01jvxay_0k/croot/sqlalchemy_1752524450450/work
sqlparse @ file:///C:/b/abs_68npyz0s6_/croot/sqlparse_1734704476369/work
Twisted==25.5.0
txaio==25.6.1
typing_extensions @ file:///C:/b/abs_0ffjxtihug/croot/typing_extensions_1734714875646/work
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.5.0
vine==5.1.0
wcwidth==0.2.13
wheel==0.45.1
zope.interface==7.2
