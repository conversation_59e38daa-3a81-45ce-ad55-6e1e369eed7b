# 元宇宙社交空间项目环境变量配置
# 复制此文件为.env并修改相应配置

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production

# ===========================================
# 数据库配置
# ===========================================
DB_ENGINE=mysql
DB_NAME=soic_dev
DB_USER=root
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# ===========================================
# Redis配置
# ===========================================
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_URL=redis://localhost:6379/1
REDIS_SESSION_URL=redis://localhost:6379/2
REDIS_CHANNELS_URL=redis://localhost:6379/3

# ===========================================
# Celery配置
# ===========================================
CELERY_BROKER_URL=redis://localhost:6379/4
CELERY_RESULT_BACKEND=redis://localhost:6379/4

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_BASE_URL=ws://localhost:8000/ws
VITE_APP_TITLE=元宇宙社交空间
VITE_APP_VERSION=1.0.0

# ===========================================
# 邮件配置
# ===========================================
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# ===========================================
# 文件存储配置
# ===========================================
# 本地存储
MEDIA_URL=/media/
MEDIA_ROOT=./media

# AWS S3存储（可选）
USE_S3=false
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-cloudfront-domain.com

# ===========================================
# 第三方服务配置
# ===========================================
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 微信OAuth
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 支付宝支付
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key

# Stripe支付
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# 短信服务
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# ===========================================
# 监控和日志配置
# ===========================================
# Sentry错误监控
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development

# 日志级别
LOG_LEVEL=DEBUG

# ===========================================
# 安全配置
# ===========================================
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# HTTPS配置（生产环境）
SECURE_SSL_REDIRECT=false
SECURE_HSTS_SECONDS=0
SESSION_COOKIE_SECURE=false
CSRF_COOKIE_SECURE=false

# ===========================================
# 业务配置
# ===========================================
# 用户限制
MAX_SPACES_PER_USER=10
MAX_FILE_SIZE=*********  # 100MB in bytes
DEFAULT_SPACE_CAPACITY=50
MAX_MESSAGE_LENGTH=1000

# 速率限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# ===========================================
# WebRTC配置
# ===========================================
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
TURN_SERVER_URL=turn:your-turn-server.com:3478
TURN_USERNAME=your-turn-username
TURN_PASSWORD=your-turn-password

# ===========================================
# 区块链配置（可选）
# ===========================================
ETHEREUM_NODE_URL=https://mainnet.infura.io/v3/your-project-id
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key
CONTRACT_ADDRESS=your-contract-address

# ===========================================
# 搜索引擎配置
# ===========================================
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=soic

# ===========================================
# 缓存配置
# ===========================================
CACHE_TTL=300  # 5 minutes
SESSION_CACHE_TTL=86400  # 24 hours

# ===========================================
# 开发工具配置
# ===========================================
# Django Debug Toolbar
ENABLE_DEBUG_TOOLBAR=true

# Django Extensions
ENABLE_DJANGO_EXTENSIONS=true

# API文档
ENABLE_API_DOCS=true

# ===========================================
# Docker配置
# ===========================================
COMPOSE_PROJECT_NAME=soic
COMPOSE_FILE=docker-compose.yml

# ===========================================
# 部署配置
# ===========================================
DEPLOYMENT_ENVIRONMENT=development
KUBERNETES_NAMESPACE=soic
DOCKER_REGISTRY=your-registry.com
IMAGE_TAG=latest